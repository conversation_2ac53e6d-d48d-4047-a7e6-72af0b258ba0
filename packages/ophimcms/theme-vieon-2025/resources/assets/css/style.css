/* Import Theme Files */
@import 'theme-colors.css';
@import 'components.css';
@import 'utilities.css';
@import 'movie-detail.css';

/* Additional Global Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--main-color);
}

/* Selection */
::selection {
    background: var(--main-color);
    color: var(--text-inverse);
}

/* Focus Outline */
:focus {
    outline: 2px solid var(--main-color);
    outline-offset: 2px;
}

/* Disable focus outline for mouse users */
:focus:not(:focus-visible) {
    outline: none;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: more) {
    :root {
        --border-primary: rgba(255, 255, 255, 0.3);
        --border-secondary: rgba(255, 255, 255, 0.2);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto;
    }
}

/* User Layout Navigation */
.nav-item {
    @apply flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm;
}

.nav-item i {
    @apply flex-shrink-0;
}

/* Sidebar compact mode */
.sidebar-compact .nav-item {
    @apply justify-center px-3;
}

.sidebar-compact .nav-item span {
    @apply hidden;
}

.sidebar-compact .nav-item i {
    @apply mr-0;
}

/* Mobile sidebar positioning fix */
@media (max-width: 1023px) {
    .sidebar-mobile-fixed {
        position: fixed !important;
        top: 1rem !important;
        left: 1rem !important;
        right: 1rem !important;
        z-index: 50 !important;
    }
}

/* Prevent layout shift on initial load */
.user-layout-container {
    min-height: 100vh;
}

/* Smooth transitions for sidebar state changes */
.sidebar-transition {
    transition: width 0.3s ease-in-out, padding 0.3s ease-in-out;
}

/* Prevent text selection on toggle button */
.sidebar-toggle {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}
