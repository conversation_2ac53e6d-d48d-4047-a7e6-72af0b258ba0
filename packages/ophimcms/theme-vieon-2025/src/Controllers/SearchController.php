<?php

namespace Ophim\ThemeVieon2025\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Movie;
use Illuminate\Support\Facades\Cache;
use Ophim\Core\Models\Category;
use Ophim\Core\Models\Region;

class SearchController extends Controller
{
    public function index(Request $request)
    {
        $keyword = $request->get('keyword', '');
        $category = $request->get('category');
        $region = $request->get('region');
        $year = $request->get('year');
        $type = $request->get('type');
        $sort = $request->get('sort', 'created_at');
        $order = $request->get('order', 'desc');

        // Build query
        $query = Movie::with(['categories', 'regions'])
            ->published();

        // Search by keyword
        if (!empty($keyword)) {
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                    ->orWhere('origin_name', 'like', "%{$keyword}%")
                    ->orWhere('content', 'like', "%{$keyword}%");
            });
        }

        // Filter by category
        if (!empty($category)) {
            $query->whereHas('categories', function ($q) use ($category) {
                $q->where('slug', $category);
            });
        }

        // Filter by region
        if (!empty($region)) {
            $query->whereHas('regions', function ($q) use ($region) {
                $q->where('slug', $region);
            });
        }

        // Filter by year
        if (!empty($year)) {
            $query->where('publish_year', $year);
        }

        // Filter by type
        if (!empty($type)) {
            $query->where('type', $type);
        }

        // Apply sorting
        switch ($sort) {
            case 'name':
                $query->orderBy('name', $order);
                break;
            case 'year':
                $query->orderBy('publish_year', $order);
                break;
            case 'view':
                $query->orderBy('view_total', $order);
                break;
            case 'rating':
                $query->orderBy('rating_star', $order);
                break;
            default:
                $query->orderBy('created_at', $order);
        }

        $movies = $query->paginate(24)->appends($request->query());

        // Get filter options for the search form
        $categories = Cache::remember('search_categories', 3600, function () {
            return Category::orderBy('name')->get();
        });

        $regions = Cache::remember('search_regions', 3600, function () {
            return Region::orderBy('name')->get();
        });

        $years = Cache::remember('search_years', 3600, function () {
            return Movie::selectRaw('DISTINCT publish_year')
                ->whereNotNull('publish_year')
                ->orderBy('publish_year', 'desc')
                ->pluck('publish_year');
        });

        return view('themevieon2025::search', [
            'keyword' => $keyword,
            'movies' => $movies,
            'categories' => $categories,
            'regions' => $regions,
            'years' => $years,
            'filters' => [
                'category' => $category,
                'region' => $region,
                'year' => $year,
                'type' => $type,
                'sort' => $sort,
                'order' => $order
            ]
        ]);
    }

    public function apiSearch(Request $request)
    {
        $keyword = $request->get('keyword', '');

        if (empty($keyword) || strlen($keyword) < 2) {
            return response()->json(['movies' => []]);
        }

        $movies = Movie::select([
                'id', 'name', 'origin_name', 'slug', 'thumb_url',
                'publish_year', 'quality', 'episode_current'
            ])
            ->published()
            ->where(function ($query) use ($keyword) {
                $query->where('name', 'like', "%{$keyword}%")
                    ->orWhere('origin_name', 'like', "%{$keyword}%");
            })
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($movie) {
                return [
                    'id' => $movie->id,
                    'name' => $movie->name,
                    'origin_name' => $movie->origin_name,
                    'thumb_url' => $movie->thumb_url,
                    'publish_year' => $movie->publish_year,
                    'quality' => $movie->quality,
                    'episode_current' => $movie->episode_current,
                    'url' => route('movies.show', $movie->slug)
                ];
            });

        return response()->json(['movies' => $movies]);
    }

    /**
     * Get popular search terms
     */
    public function popularSearches(Request $request)
    {
        // Get popular movies based on view count
        $popularMovies = Movie::select([
                'id', 'name', 'origin_name', 'slug', 'thumb_url',
                'publish_year', 'quality', 'episode_current'
            ])
            ->published()
            ->orderBy('view_total', 'desc')
            ->limit(8)
            ->get()
            ->map(function ($movie) {
                return [
                    'id' => $movie->id,
                    'name' => $movie->name,
                    'origin_name' => $movie->origin_name,
                    'thumb_url' => $movie->thumb_url,
                    'publish_year' => $movie->publish_year,
                    'quality' => $movie->quality,
                    'episode_current' => $movie->episode_current,
                    'url' => route('movies.show', $movie->slug)
                ];
            });

        return response()->json(['movies' => $popularMovies]);
    }

    /**
     * Enhanced search with suggestions for actors, directors, etc.
     */
    public function enhancedSearch(Request $request)
    {
        $keyword = $request->get('keyword', '');

        if (empty($keyword) || strlen($keyword) < 2) {
            return response()->json([
                'movies' => [],
                'actors' => [],
                'directors' => [],
                'categories' => []
            ]);
        }

        // Search movies
        $movies = Movie::select([
                'id', 'name', 'origin_name', 'slug', 'thumb_url',
                'publish_year', 'quality', 'episode_current'
            ])
            ->published()
            ->where(function ($query) use ($keyword) {
                $query->where('name', 'like', "%{$keyword}%")
                    ->orWhere('origin_name', 'like', "%{$keyword}%");
            })
            ->orderBy('created_at', 'desc')
            ->limit(8)
            ->get()
            ->map(function ($movie) {
                return [
                    'id' => $movie->id,
                    'name' => $movie->name,
                    'origin_name' => $movie->origin_name,
                    'thumb_url' => $movie->thumb_url,
                    'publish_year' => $movie->publish_year,
                    'quality' => $movie->quality,
                    'episode_current' => $movie->episode_current,
                    'url' => route('movies.show', $movie->slug)
                ];
            });

        // Search actors (if Actor model exists)
        $actors = [];
        if (class_exists('\Ophim\Core\Models\Actor')) {
            $actors = \Ophim\Core\Models\Actor::where('name', 'like', "%{$keyword}%")
                ->limit(3)
                ->get()
                ->map(function ($actor) {
                    return [
                        'name' => $actor->name,
                        'url' => route('actors.movies.index', $actor->slug)
                    ];
                });
        }

        // Search directors (if Director model exists)
        $directors = [];
        if (class_exists('\Ophim\Core\Models\Director')) {
            $directors = \Ophim\Core\Models\Director::where('name', 'like', "%{$keyword}%")
                ->limit(3)
                ->get()
                ->map(function ($director) {
                    return [
                        'name' => $director->name,
                        'url' => route('directors.movies.index', $director->slug)
                    ];
                });
        }

        // Search categories
        $categories = [];
        if (class_exists('\Ophim\Core\Models\Category')) {
            $categories = \Ophim\Core\Models\Category::where('name', 'like', "%{$keyword}%")
                ->limit(3)
                ->get()
                ->map(function ($category) {
                    return [
                        'name' => $category->name,
                        'url' => route('categories.movies.index', $category->slug)
                    ];
                });
        }

        return response()->json([
            'movies' => $movies,
            'actors' => $actors,
            'directors' => $directors,
            'categories' => $categories
        ]);
    }
}
