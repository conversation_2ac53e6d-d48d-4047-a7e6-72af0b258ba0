<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use \Ophim\Core\Models\Movie as OPhimMovie;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class Movie extends OPhimMovie
{
    use HasFactory;

    /**
     * Scope a query to only include published movies.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('status', '!=', 'trailer');
    }

    /**
     * Scope a query to only include series movies.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSeries(Builder $query): Builder
    {
        return $query->where('type', 'series');
    }

    /**
     * Scope a query to only include single movies.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSingle(Builder $query): Builder
    {
        return $query->where('type', 'single');
    }

    /**
     * Get a new query builder for the model's table.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function newQuery(): Builder
    {
        return parent::newQuery();
    }

    /**
     * Get the movie's rating star average.
     *
     * @return float
     */
    public function getRatingStar(): float
    {
        return (float) ($this->rating_star ?? 0);
    }

    /**
     * Get the movie's rating count.
     *
     * @return int
     */
    public function getRatingCount(): int
    {
        return (int) ($this->rating_count ?? 0);
    }

    /**
     * Relationship with ratings
     */
    public function ratings()
    {
        return $this->hasMany(\Ophim\ThemeVieon2025\Models\Rating::class);
    }

    /**
     * Relationship with favorites
     */
    public function favorites()
    {
        return $this->hasMany(\Ophim\ThemeVieon2025\Models\Favorite::class);
    }

    /**
     * Relationship with watchlists
     */
    public function watchlists()
    {
        return $this->hasMany(\Ophim\ThemeVieon2025\Models\Watchlist::class);
    }

    /**
     * Check if current user has favorited this movie
     */
    public function userFavorite()
    {
        return $this->hasOne(\Ophim\ThemeVieon2025\Models\Favorite::class)
            ->where('user_id', Auth::id());
    }

    /**
     * Check if current user has added this movie to watchlist
     */
    public function userWatchlist()
    {
        return $this->hasOne(\Ophim\ThemeVieon2025\Models\Watchlist::class)
            ->where('user_id', Auth::id());
    }

    /**
     * Scope to load movies with user state for authenticated users
     */
    public function scopeWithUserState(Builder $query)
    {
        if (Auth::check()) {
            return $query->with(['userFavorite', 'userWatchlist']);
        }
        return $query;
    }
}
