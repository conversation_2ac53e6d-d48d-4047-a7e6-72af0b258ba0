@extends('themevieon2025::layouts.app')

@section('title', 'Phim tag ' . $tag->name . ' - Trang ' . ($movies->currentPage() ?? 1))
@section('description', 'Xem phim có tag ' . $tag->name . ' hay nhất, mới nhất. Danh sách phim được gắn tag ' . $tag->name . ' chấ<PERSON> l<PERSON> HD miễn phí.')

@section('content')
    <div class="min-h-screen bg-theme-primary">
        {{-- Breadcrumb --}}
        @include('themevieon2025::components.breadcrumb', [
            'items' => [
                [
                    'label' => 'Từ khóa',
                    'url' => '#',
                    'icon' => 'fas fa-hashtag'
                ],
                [
                    'label' => $tag->name,
                    'icon' => 'fas fa-tag'
                ]
            ]
        ])

        <!-- Page Header -->
        <div class="bg-theme-secondary border-b border-theme-primary">
            <div class="container mx-auto px-4 py-6">

                <!-- Tag Header -->
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-theme-primary mb-2">
                            <i class="fas fa-hashtag mr-2 text-main"></i>
                            {{ $tag->name }}
                        </h1>
                        <p class="text-theme-tertiary">
                            Tìm thấy {{ $movies->total() }} phim có tag {{ $tag->name }}
                        </p>
                    </div>

                    <!-- Tag Stats -->
                    <div class="hidden md:block text-right">
                        <div class="bg-theme-dropdown rounded-lg p-3">
                            <div class="text-2xl font-bold text-main">{{ number_format($movies->total()) }}</div>
                            <div class="text-sm text-theme-tertiary">Tổng phim</div>
                        </div>
                    </div>
                </div>

                <!-- Tag Description -->
                @if($tag->description)
                    <div class="mt-4 p-4 bg-theme-dropdown rounded-lg">
                        <p class="text-theme-secondary text-sm leading-relaxed">
                            {{ $tag->description }}
                        </p>
                    </div>
                @endif
            </div>
        </div>

        <div class="container mx-auto px-4 py-6">
            <!-- Filter & Sort Bar -->
            @include('themevieon2025::components.movie-sort-bar', [
                'currentSort' => $currentSort ?? 'created_at',
                'currentOrder' => $currentOrder ?? 'desc'
            ])

            <!-- Movies Grid/List -->
            @if($movies->count() > 0)
                <div id="movies-container"
                     class="{{ request('view', 'grid') === 'list' ? 'space-y-4' : 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4' }} mb-6">
                    @foreach($movies as $movie)
                        @include('themevieon2025::components.movie-card', [
                            'movie' => $movie,
                            'layout' => request('view', 'grid'),
                            'showRating' => true,
                            'showActions' => true
                        ])
                    @endforeach
                </div>

                <!-- Pagination -->
                @if($movies->hasPages())
                    <div class="bg-theme-secondary rounded-lg p-4">
                        {{ $movies->appends(request()->query())->links() }}
                    </div>
                @endif
            @else
                <!-- No Movies -->
                <div class="bg-theme-secondary rounded-lg p-8 text-center">
                    <div class="max-w-md mx-auto">
                        <i class="fas fa-hashtag text-6xl text-theme-tertiary mb-4"></i>
                        <h3 class="text-xl font-semibold text-theme-primary mb-2">
                            Chưa có phim nào
                        </h3>
                        <p class="text-theme-tertiary mb-6">
                            Hiện tại chưa có phim nào được gắn tag {{ $tag->name }}. Hãy quay lại sau hoặc khám phá các
                            tag khác.
                        </p>
                        <a href="{{ route('home') }}"
                           class="inline-block px-6 py-2 bg-main hover:bg-green-600 text-white rounded-lg transition-colors">
                            Về trang chủ
                        </a>
                    </div>
                </div>
            @endif
        </div>

        <!-- Popular Tags -->
        <div class="bg-theme-secondary border-t border-theme-primary">
            <div class="container mx-auto px-4 py-6">
                <h3 class="text-lg font-semibold text-theme-primary mb-4">
                    <i class="fas fa-fire mr-2"></i>
                    Tag phổ biến
                </h3>

                <div class="flex flex-wrap gap-2">
                    @php
                        $popularTags = [
                            'Hành động', 'Tình cảm', 'Hài hước', 'Kinh dị', 'Phiêu lưu',
                            'Khoa học viễn tưởng', 'Tâm lý', 'Gia đình', 'Học đường', 'Cung đấu',
                            'Xuyên không', 'Trọng sinh', 'Ngọt ngào', 'Báo thù', 'Tổng tài',
                            'Đô thị', 'Cổ trang', 'Hiện đại', 'Thanh xuân', 'Ngược tâm'
                        ];
                    @endphp

                    @foreach($popularTags as $popularTag)
                        <a href="{{ route('tags.movies.index', Str::slug($popularTag)) }}"
                           class="px-3 py-2 bg-theme-dropdown hover:bg-theme-button-hover text-theme-primary rounded-lg transition-colors text-sm {{ $tag->name === $popularTag ? 'ring-2 ring-main' : '' }}">
                            #{{ $popularTag }}
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // View mode toggle
        function setViewMode(mode) {
            const url = new URL(window.location);
            url.searchParams.set('view', mode);
            window.location.href = url.toString();
        }

        // AJAX pagination
        document.addEventListener('DOMContentLoaded', function () {
            // Handle pagination clicks
            document.addEventListener('click', function (e) {
                if (e.target.matches('.pagination a') || e.target.closest('.pagination a')) {
                    e.preventDefault();
                    const link = e.target.matches('a') ? e.target : e.target.closest('a');
                    loadPage(link.href);
                }
            });

            function loadPage(url) {
                // Show loading state
                const container = document.getElementById('movies-container');
                container.style.opacity = '0.5';

                fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'text/html'
                    }
                })
                    .then(response => response.text())
                    .then(html => {
                        // Parse the response and update content
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(html, 'text/html');

                        // Update movies container
                        const newContainer = doc.getElementById('movies-container');
                        if (newContainer) {
                            container.innerHTML = newContainer.innerHTML;
                        }

                        // Update pagination
                        const pagination = document.querySelector('.pagination');
                        const newPagination = doc.querySelector('.pagination');
                        if (pagination && newPagination) {
                            pagination.innerHTML = newPagination.innerHTML;
                        }

                        // Update URL
                        window.history.pushState({}, '', url);

                        // Scroll to top
                        window.scrollTo({top: 0, behavior: 'smooth'});
                    })
                    .catch(error => {
                        console.error('AJAX pagination error:', error);
                    })
                    .finally(() => {
                        container.style.opacity = '1';
                    });
            }
        });
    </script>
@endpush
