# Common JavaScript Functions Summary

## Functions moved to common.js

### 1. **toggleFavorite(movieId)**
- **Purpose**: Toggle movie favorite status
- **Used in**: movie-card.blade.php, movie.blade.php
- **Features**:
  - User authentication check
  - AJAX call to user API
  - UI update (heart icon color change)
  - Notification display
  - Error handling

### 2. **toggleWatchlist(movieId)**
- **Purpose**: Toggle movie watchlist status
- **Used in**: movie-card.blade.php
- **Features**:
  - User authentication check
  - AJAX call to user API
  - UI update (bookmark icon color change)
  - Notification display
  - Error handling

### 3. **shareMovie(movieName, movieUrl)**
- **Purpose**: Share movie using native Web Share API or fallback to copy link
- **Used in**: movie-card.blade.php
- **Features**:
  - Native Web Share API support
  - Fallback to clipboard copy
  - Error handling
  - Notification display

### 4. **showTrailer(trailerUrl)**
- **Purpose**: Display movie trailer in modal
- **Used in**: movie.blade.php
- **Features**:
  - YouTube URL parsing
  - Modal creation and management
  - Autoplay support
  - Responsive design

### 5. **hideTrailer()**
- **Purpose**: Close trailer modal
- **Features**:
  - Modal cleanup
  - Body scroll restoration
  - Content clearing

### 6. **Social Sharing Functions**
- **shareToFacebook(url, title)**
- **shareToTwitter(url, title)**
- **shareToTelegram(url, title)**
- **copyLink(url)**
- **Purpose**: Share to specific social platforms
- **Used in**: movie.blade.php sidebar

### 7. **showReportModal(movieSlug)**
- **Purpose**: Show movie report modal
- **Used in**: movie.blade.php

## Benefits of Common Functions

### ✅ **Code Reusability**
- Functions can be used across multiple components
- No need to redefine same functionality
- Consistent behavior everywhere

### ✅ **Maintainability**
- Single source of truth for each function
- Easy to update functionality globally
- Reduced code duplication

### ✅ **Error Prevention**
- No more "function is not defined" errors
- Proper loading order with layout inclusion
- Global availability

### ✅ **Performance**
- Functions loaded once in layout
- No duplicate function definitions
- Better memory usage

## Loading Order

1. **Layout loads common.js** (before other scripts)
2. **Global aliases created** for backward compatibility
3. **Component-specific scripts** can use common functions
4. **Page-specific scripts** override if needed

## Usage Examples

### In Blade Templates:
```blade
{{-- Movie Card --}}
<button onclick="toggleFavorite({{ $movie->id }})">
    <i class="fas fa-heart"></i>
</button>

{{-- Movie Detail --}}
<button onclick="showTrailer()">
    <i class="fas fa-play-circle"></i> Trailer
</button>

{{-- Social Share --}}
<button onclick="shareToFacebook()">
    <i class="fab fa-facebook-f"></i> Facebook
</button>
```

### In JavaScript:
```javascript
// Direct function call
window.MovieFunctions.toggleFavorite(movieId);

// Using global alias
toggleFavorite(movieId);

// With custom parameters
shareMovie('Movie Name', 'https://example.com/movie');
```

## File Structure

```
resources/assets/js/
├── common.js          # ✅ All shared functions
├── rating.js          # Movie rating specific
├── user-api.js        # User API calls
├── ajax-form.js       # Form handling
└── app.js            # App initialization

components/
├── movie-card.blade.php    # ✅ Uses common functions
└── ...

views/
├── movie.blade.php         # ✅ Uses common functions
└── ...

layouts/
└── app.blade.php          # ✅ Loads common.js first
```

## Next Steps

1. **Test all functions** across different components
2. **Add more common functions** as needed (e.g., rating, search)
3. **Consider moving rating.js** functions to common.js
4. **Optimize loading** with bundling if needed
5. **Add TypeScript definitions** for better development experience
