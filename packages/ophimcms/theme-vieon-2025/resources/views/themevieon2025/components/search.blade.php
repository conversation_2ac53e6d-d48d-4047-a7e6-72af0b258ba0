@php
    $searchKeyword = request('search', '');
@endphp

<div class="search-component relative" x-data="searchComponent()" x-init="initButtonListener()">
    <!-- Search Overlay (Mobile) -->
    <div x-show="isOpen"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-black/80 z-[9999] lg:hidden" style="z-index: 9999;"
         @click.self="closeSearch()">

        <div class="flex flex-col h-full">
            <!-- Search Header -->
            <div class="flex items-center justify-between p-4 bg-black/90">
                <h3 class="text-white text-lg font-semibold">T<PERSON><PERSON> kiếm</h3>
                <button @click="closeSearch()" class="text-white hover:text-main text-xl p-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Search Input -->
            <div class="p-4">
                <div class="relative">
                    <input type="text"
                           x-model="keyword"
                           @input.debounce.300ms="search()"
                           @keydown="handleKeydown($event)"
                           placeholder="Nhập tên phim bạn muốn tìm..."
                           class="w-full bg-gray-800 text-white placeholder-gray-400 rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-main"
                           autocomplete="off">
                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Search Results -->
            <div class="flex-1 overflow-y-auto px-4 pb-4">
                <div x-show="isLoading" class="space-y-3">
                    @for($i = 0; $i < 5; $i++)
                        <div class="flex space-x-3 animate-pulse">
                            <div class="w-16 h-24 bg-gray-700 rounded"></div>
                            <div class="flex-1 space-y-2">
                                <div class="h-4 bg-gray-700 rounded w-3/4"></div>
                                <div class="h-3 bg-gray-700 rounded w-1/2"></div>
                                <div class="h-3 bg-gray-700 rounded w-1/4"></div>
                            </div>
                        </div>
                    @endfor
                </div>

                <div x-show="!isLoading && results.length > 0" class="space-y-3">
                    <template x-for="(movie, index) in results" :key="movie.id">
                        <a :href="movie.url"
                           class="flex space-x-3 p-3 rounded-lg hover:bg-gray-800 transition-colors"
                           :class="{ 'bg-gray-800': selectedIndex === index }">
                            <div class="w-16 h-24 flex-shrink-0">
                                <img
                                    :src="movie.thumb_url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iOTYiIHZpZXdCb3g9IjAgMCA2NCA5NiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9Ijk2IiBmaWxsPSIjMzc0MTUxIi8+CjxwYXRoIGQ9Ik0zMiA0OEMyOC42ODYzIDQ4IDI2IDQ1LjMxMzcgMjYgNDJDMjYgMzguNjg2MyAyOC42ODYzIDM2IDMyIDM2QzM1LjMxMzcgMzYgMzggMzguNjg2MyAzOCA0MkMzOCA0NS4zMTM3IDM1LjMxMzcgNDggMzIgNDhaIiBmaWxsPSIjNjc3MjgwIi8+CjxwYXRoIGQ9Ik0zMiA1NkMyOC42ODYzIDU2IDI2IDUzLjMxMzcgMjYgNTBDMjYgNDYuNjg2MyAyOC42ODYzIDQ0IDMyIDQ0QzM1LjMxMzcgNDQgMzggNDYuNjg2MyAzOCA1MEMzOCA1My4zMTM3IDM1LjMxMzcgNTYgMzIgNTZaIiBmaWxsPSIjNjc3MjgwIi8+Cjwvc3ZnPgo='"
                                    :alt="movie.name"
                                    class="w-full h-full object-cover rounded">
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="text-white font-medium truncate" x-text="movie.name"></h4>
                                <p class="text-gray-400 text-sm truncate" x-text="movie.origin_name"></p>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="text-xs text-gray-500" x-text="movie.publish_year"></span>
                                    <span class="text-gray-500">•</span>
                                    <span class="text-xs text-gray-500" x-text="movie.quality"></span>
                                </div>
                            </div>
                        </a>
                    </template>
                </div>

                <div x-show="!isLoading && keyword && results.length === 0" class="text-center py-8">
                    <i class="fas fa-search text-gray-500 text-4xl mb-4"></i>
                    <p class="text-gray-400">Không tìm thấy kết quả cho "<span x-text="keyword"></span>"</p>
                </div>

                <div x-show="!isLoading && !keyword" class="text-center py-8">
                    <i class="fas fa-film text-gray-500 text-4xl mb-4"></i>
                    <p class="text-gray-400">Nhập tên phim để bắt đầu tìm kiếm</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Dropdown (Desktop) -->
    <div x-show="isOpen && !isMobile"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         :style="dropdownStyle"
         class="fixed w-96 bg-gray-900 rounded-lg shadow-xl border border-gray-700 z-[9999]"
         @click.away="closeSearch()">

        <!-- Search Input -->
        <div class="p-4 border-b border-gray-700">
            <div class="relative">
                <input type="text"
                       x-model="keyword"
                       @input.debounce.300ms="search()"
                       @keydown="handleKeydown($event)"
                       placeholder="Tìm kiếm phim..."
                       class="w-full bg-gray-800 text-white placeholder-gray-400 rounded-lg px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-main"
                       autocomplete="off">
                <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- Search Results -->
        <div class="max-h-96 overflow-y-auto">
            <div x-show="isLoading" class="p-4 space-y-3">
                @for($i = 0; $i < 3; $i++)
                    <div class="flex space-x-3 animate-pulse">
                        <div class="w-12 h-16 bg-gray-700 rounded"></div>
                        <div class="flex-1 space-y-2">
                            <div class="h-3 bg-gray-700 rounded w-3/4"></div>
                            <div class="h-2 bg-gray-700 rounded w-1/2"></div>
                        </div>
                    </div>
                @endfor
            </div>

            <div x-show="!isLoading && results.length > 0" class="p-2">
                <template x-for="(movie, index) in results.slice(0, 6)" :key="movie.id">
                    <a :href="movie.url"
                       class="flex space-x-3 p-2 rounded-lg hover:bg-gray-800 transition-colors"
                       :class="{ 'bg-gray-800': selectedIndex === index }">
                        <div class="w-12 h-16 flex-shrink-0">
                            <img
                                :src="movie.thumb_url || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iOTYiIHZpZXdCb3g9IjAgMCA2NCA5NiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9Ijk2IiBmaWxsPSIjMzc0MTUxIi8+CjxwYXRoIGQ9Ik0zMiA0OEMyOC42ODYzIDQ4IDI2IDQ1LjMxMzcgMjYgNDJDMjYgMzguNjg2MyAyOC42ODYzIDM2IDMyIDM2QzM1LjMxMzcgMzYgMzggMzguNjg2MyAzOCA0MkMzOCA0NS4zMTM3IDM1LjMxMzcgNDggMzIgNDhaIiBmaWxsPSIjNjc3MjgwIi8+CjxwYXRoIGQ9Ik0zMiA1NkMyOC42ODYzIDU2IDI2IDUzLjMxMzcgMjYgNTBDMjYgNDYuNjg2MyAyOC42ODYzIDQ0IDMyIDQ0QzM1LjMxMzcgNDQgMzggNDYuNjg2MyAzOCA1MEMzOCA1My4zMTM3IDM1LjMxMzcgNTYgMzIgNTZaIiBmaWxsPSIjNjc3MjgwIi8+Cjwvc3ZnPgo='"
                                :alt="movie.name"
                                class="w-full h-full object-cover rounded">
                        </div>
                        <div class="flex-1 min-w-0">
                            <h4 class="text-white text-sm font-medium truncate" x-text="movie.name"></h4>
                            <p class="text-gray-400 text-xs truncate" x-text="movie.origin_name"></p>
                            <div class="flex items-center space-x-2 mt-1">
                                <span class="text-xs text-gray-500" x-text="movie.publish_year"></span>
                                <span class="text-gray-500">•</span>
                                <span class="text-xs text-gray-500" x-text="movie.quality"></span>
                            </div>
                        </div>
                    </a>
                </template>

                <div x-show="results.length > 6" class="p-2 text-center">
                    <a :href="'/search?keyword=' + encodeURIComponent(keyword)"
                       class="text-main hover:text-yellow-400 text-sm font-medium">
                        Xem tất cả kết quả
                    </a>
                </div>
            </div>

            <div x-show="!isLoading && keyword && results.length === 0" class="p-4 text-center">
                <i class="fas fa-search text-gray-500 text-2xl mb-2"></i>
                <p class="text-gray-400 text-sm">Không tìm thấy kết quả</p>
            </div>

            <div x-show="!isLoading && !keyword" class="p-4 text-center">
                <i class="fas fa-film text-gray-500 text-2xl mb-2"></i>
                <p class="text-gray-400 text-sm">Nhập tên phim để tìm kiếm</p>
            </div>
        </div>
    </div>
</div>

<script>
    function searchComponent() {
        return {
            keyword: '',
            results: [],
            isLoading: false,
            isOpen: false,
            isMobile: window.innerWidth < 1024,
            selectedIndex: -1,
            dropdownStyle: '',

            init() {
                this.checkMobile();
                window.addEventListener('resize', () => {
                    this.checkMobile();
                    this.updateDropdownPosition();
                });
                window.addEventListener('scroll', () => {
                    this.updateDropdownPosition();
                }, true);
            },

            initButtonListener() {
                window.addEventListener('open-search-overlay', () => {
                    this.isOpen = true;
                    this.$nextTick(() => {
                        this.updateDropdownPosition();
                    });
                });
            },

            updateDropdownPosition() {
                if (!this.isOpen || this.isMobile) return;
                const btn = document.getElementById('search-trigger-btn');
                if (btn) {
                    const rect = btn.getBoundingClientRect();
                    this.dropdownStyle = `top: ${rect.bottom + 8}px; left: ${rect.left - 320 + rect.width}px; z-index: 9999;`;
                }
            },

            checkMobile() {
                this.isMobile = window.innerWidth < 1024;
                if (!this.isMobile && this.isOpen) {
                    this.closeSearch();
                }
            },

            toggleSearch() {
                this.isOpen = !this.isOpen;
                if (this.isOpen) {
                    this.$nextTick(() => {
                        this.$refs.searchInput?.focus();
                    });
                }
            },

            closeSearch() {
                this.isOpen = false;
                this.keyword = '';
                this.results = [];
                this.selectedIndex = -1;
            },

            async search() {
                if (!this.keyword.trim()) {
                    this.results = [];
                    return;
                }

                this.isLoading = true;
                this.selectedIndex = -1;

                try {
                    const response = await fetch(`/api/search?keyword=${encodeURIComponent(this.keyword)}`);
                    const data = await response.json();
                    this.results = data.movies || [];
                } catch (error) {
                    console.error('Search error:', error);
                    this.results = [];
                } finally {
                    this.isLoading = false;
                }
            },

            handleKeydown(event) {
                const items = this.results;

                switch (event.key) {
                    case 'ArrowDown':
                        event.preventDefault();
                        this.selectedIndex = Math.min(this.selectedIndex + 1, items.length - 1);
                        break;
                    case 'ArrowUp':
                        event.preventDefault();
                        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
                        break;
                    case 'Enter':
                        if (this.selectedIndex >= 0 && items[this.selectedIndex]) {
                            window.location.href = items[this.selectedIndex].url;
                        } else if (this.keyword.trim()) {
                            window.location.href = `/search?keyword=${encodeURIComponent(this.keyword)}`;
                        }
                        break;
                    case 'Escape':
                        this.closeSearch();
                        break;
                }
            }
        }
    }
</script>
