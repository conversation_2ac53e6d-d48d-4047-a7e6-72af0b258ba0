@extends('themevieon2025::layouts.app')
@section('content')
    <div class="max-w-md mx-auto mt-20 bg-gray-900 p-8 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold mb-6 text-center"><PERSON><PERSON><PERSON> mật khẩu</h2>
        <form method="POST" action="{{ route('api.password.change') }}" class="ajax-form">
            @csrf
            <div class="mb-4">
                <label class="block mb-1">M<PERSON>t kh<PERSON>u hiện tại</label>
                <input type="password" name="current_password" autocomplete="current-password"
                       class="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" required>
                @error('current_password')
                <div class="text-red-500 text-sm mt-1">{{ $message }}</div>@enderror
            </div>
            <div class="mb-4">
                <label class="block mb-1">M<PERSON><PERSON> kh<PERSON>u mới</label>
                <input type="password" name="new_password" autocomplete="new-password"
                       class="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" required>
                @error('new_password')
                <div class="text-red-500 text-sm mt-1">{{ $message }}</div>@enderror
            </div>
            <div class="mb-4">
                <label class="block mb-1">Xác nhận mật khẩu</label>
                <input type="password" name="new_password_confirmation" autocomplete="new-password"
                       class="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" required>
            </div>
            <button type="submit"
                    class="w-full bg-main hover:bg-yellow-500 text-black font-bold py-2 rounded transition">Đổi mật khẩu
            </button>
        </form>
    </div>
@endsection
