@extends('themevieon2025::layouts.user')

@section('title', 'Quản lý tài khoản - ' . config('app.name') . ' 2025')

@section('user-content')
    <div class="max-w-2xl mx-auto">
        <div class="text-center mb-8">
            <div class="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-red-500/20 mb-4">
                <svg class="h-10 w-10 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
            </div>
            <h2 class="text-3xl font-bold text-white mb-2"><PERSON><PERSON>ản lý tài k<PERSON></h2>
            <p class="text-gray-400"><PERSON><PERSON><PERSON> tù<PERSON> chọn quản lý tài khoản của bạn</p>
        </div>

        {{-- Loading Overlay --}}
        <div id="account-loading"
             class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
            <div class="bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-main"></div>
                <span class="text-white">Đang xử lý...</span>
            </div>
        </div>

        {{-- Message Containers --}}
        <div id="account-error"
             class="hidden mb-6 bg-red-500 bg-opacity-20 border border-red-500 text-red-200 px-4 py-3 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                          clip-rule="evenodd"></path>
                </svg>
                <span id="account-error-message"></span>
            </div>
        </div>

        <div id="account-success"
             class="hidden mb-6 bg-green-500 bg-opacity-20 border border-green-500 text-green-200 px-4 py-3 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clip-rule="evenodd"></path>
                </svg>
                <span id="account-success-message"></span>
            </div>
        </div>

        <!-- Danger Zone -->
        <div class="bg-red-500/5 border border-red-500/20 rounded-lg p-6">
            <div class="flex items-center mb-4">
                <svg class="h-6 w-6 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <h3 class="text-lg font-semibold text-red-400">Vùng nguy hiểm</h3>
            </div>
            <p class="text-gray-300 mb-6">
                Xóa tài khoản sẽ xóa vĩnh viễn tất cả dữ liệu của bạn bao gồm lịch sử xem, danh sách yêu thích, bình
                luận và đánh giá.
                Hành động này không thể hoàn tác.
            </p>

            <form id="delete-account-form" class="space-y-4">
                @csrf

                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Nhập mật khẩu để xác nhận</label>
                    <input type="password" name="password"
                           class="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all"
                           placeholder="Nhập mật khẩu của bạn" required>
                </div>

                <button type="submit"
                        class="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200">
                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Xóa tài khoản
                </button>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('delete-account-form');

            if (form) {
                form.addEventListener('submit', async function (e) {
                    e.preventDefault();

                    // Confirm deletion
                    if (!confirm('Bạn có chắc chắn muốn xóa tài khoản? Hành động này không thể hoàn tác.')) {
                        return;
                    }

                    // Hide previous messages
                    hideMessage();

                    // Show loading
                    showLoading(true);

                    // Get form data
                    const formData = new FormData(form);
                    const password = formData.get('password');

                    try {
                        // Delete account via API
                        const response = await window.userAPI.deleteAccount(password);

                        // If we reach here, the request was successful
                        console.log('Account deletion response:', response);
                        showSuccess(response.message || 'Tài khoản đã được xóa thành công');

                        // Redirect to home after 2 seconds
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 2000);

                    } catch (error) {
                        console.error('Error deleting account:', error);

                        // Parse error message from response
                        let errorMessage = 'Có lỗi xảy ra khi xóa tài khoản';

                        if (error.message) {
                            try {
                                // Try to parse JSON error response
                                const errorData = JSON.parse(error.message);
                                if (errorData.errors) {
                                    // Format validation errors
                                    const errorList = [];
                                    for (const field in errorData.errors) {
                                        errorList.push(...errorData.errors[field]);
                                    }
                                    errorMessage = errorList.join(', ');
                                } else if (errorData.message) {
                                    errorMessage = errorData.message;
                                }
                            } catch (parseError) {
                                // If not JSON, use the original message
                                errorMessage = error.message;
                            }
                        }

                        showError(errorMessage);
                    } finally {
                        showLoading(false);
                    }
                });
            }
        });

        function showLoading(show) {
            const loadingEl = document.getElementById('account-loading');
            if (show) {
                loadingEl.classList.remove('hidden');
            } else {
                loadingEl.classList.add('hidden');
            }
        }

        function showError(message) {
            const errorEl = document.getElementById('account-error');
            const messageEl = document.getElementById('account-error-message');

            if (!errorEl || !messageEl) {
                console.error('Error elements not found');
                return;
            }

            messageEl.textContent = message;
            errorEl.classList.remove('hidden');

            // Auto hide after 5 seconds
            setTimeout(() => {
                errorEl.classList.add('hidden');
            }, 5000);
        }

        function showSuccess(message) {
            const successEl = document.getElementById('account-success');
            const messageEl = document.getElementById('account-success-message');

            if (!successEl || !messageEl) {
                console.error('Success elements not found');
                return;
            }

            messageEl.textContent = message;
            successEl.classList.remove('hidden');

            // Auto hide after 3 seconds
            setTimeout(() => {
                successEl.classList.add('hidden');
            }, 3000);
        }

        function hideMessage() {
            document.getElementById('account-error').classList.add('hidden');
            document.getElementById('account-success').classList.add('hidden');
        }
    </script>
@endpush
