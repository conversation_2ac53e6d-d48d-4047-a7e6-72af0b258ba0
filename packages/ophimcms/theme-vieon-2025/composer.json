{"name": "ophimcms/theme-vieon-2025", "description": "<PERSON><PERSON>'s vieon-2025 theme", "type": "library", "authors": [{"name": "ophimcms", "email": "<EMAIL>"}], "require": {"laravel/framework": "^6|^7|^8", "hacoidev/ophim-core": "^1.1.0"}, "license": "MIT", "autoload": {"psr-4": {"Ophim\\ThemeVieon2025\\": "src/", "Ophim\\ThemeVieon2025\\Database\\Factories\\": "database/factories/", "Ophim\\ThemeVieon2025\\Database\\Seeders\\": "database/seeders/"}}, "extra": {"laravel": {"providers": ["Ophim\\ThemeVieon2025\\ThemeVieon2025ServiceProvider"]}}, "minimum-stability": "stable"}