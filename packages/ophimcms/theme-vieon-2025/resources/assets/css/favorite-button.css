/* Favorite <PERSON> Styles for VieOn 2025 Theme */

.favorite-btn {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.favorite-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.favorite-btn:active {
    transform: translateY(0);
    transition: transform 0.1s;
}

/* Heart icon animation */
.favorite-btn i.fa-heart {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
}

.favorite-btn:hover i.fa-heart {
    transform: scale(1.1);
}

/* Favorited state - red heart */
.favorite-btn[data-is-favorite="true"] i.fa-heart {
    color: #ffffff;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

/* Unfavorited state - outline heart */
.favorite-btn[data-is-favorite="false"] i.fa-heart {
    color: inherit;
}

/* Pulse animation when toggling */
.favorite-btn.toggling i.fa-heart {
    animation: heartPulse 0.6s ease-in-out;
}

@keyframes heartPulse {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1.1); }
    75% { transform: scale(1.15); }
    100% { transform: scale(1); }
}

/* Loading state */
.favorite-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.favorite-btn.loading i.fa-heart {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .favorite-btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .favorite-btn i.fa-heart {
        margin-right: 0.5rem;
    }
}

/* Focus states for accessibility */
.favorite-btn:focus {
    outline: none;
    ring: 2px;
    ring-color: rgba(59, 130, 246, 0.5);
    ring-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .favorite-btn[data-is-favorite="true"] {
        border: 2px solid #ffffff !important;
    }
    
    .favorite-btn[data-is-favorite="false"] {
        border: 2px solid currentColor !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .favorite-btn,
    .favorite-btn i.fa-heart {
        transition: none;
    }
    
    .favorite-btn:hover {
        transform: none;
    }
    
    .favorite-btn.toggling i.fa-heart {
        animation: none;
    }
}
