@extends('themevieon2025::layouts.app')

@section('title', '<PERSON>h sách phim - ' . setting('site_meta_title', 'VieOn 2025'))
@section('description', '<PERSON><PERSON> sách tất cả phim hay, phim mới nhất đ<PERSON> cập nhật liên tục. Xem phim chất lượng HD miễn phí.')

@section('content')
    <div class="bg-gray-50 py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="p-6">
                    <h1 class="text-2xl font-bold text-gray-900 mb-6">Danh Sách Phim</h1>

                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        @foreach($movies as $movie)
                            <div
                                class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                                <a href="{{ route('movies.show', $movie->slug) }}">
                                    <div class="relative">
                                        <img src="{{ $movie->thumb_url }}" alt="{{ $movie->name }}"
                                             class="w-full h-48 object-cover">
                                        <div class="absolute top-2 right-2">
                                            <span
                                                class="px-2 py-1 bg-red-600 text-white text-xs rounded">{{ $movie->quality }}</span>
                                        </div>
                                    </div>
                                    <div class="p-3">
                                        <h3 class="text-sm font-medium text-gray-900 truncate">{{ $movie->name }}</h3>
                                        <p class="text-xs text-gray-500 mt-1 truncate">{{ $movie->origin_name }}</p>
                                        <div class="mt-2 flex items-center text-xs text-gray-500">
                                            <span>{{ $movie->episode_current }}</span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>

                    <div class="mt-6">
                        {{ $movies->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
