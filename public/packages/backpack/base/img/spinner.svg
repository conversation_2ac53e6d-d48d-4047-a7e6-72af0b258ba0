<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="margin: auto; background: none; display: block; shape-rendering: auto;" width="32px" height="32px" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid">
<g transform="rotate(0 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-1.171875s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(22.5 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-1.09375s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(45 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-1.015625s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(67.5 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-0.9375s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(90 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-0.859375s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(112.5 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-0.78125s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(135 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-0.703125s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(157.5 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-0.625s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(180 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-0.546875s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(202.5 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-0.46875s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(225 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-0.390625s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(247.5 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-0.3125s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(270 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-0.234375s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(292.5 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-0.15625s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(315 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="-0.078125s" repeatCount="indefinite"></animate>
  </rect>
</g><g transform="rotate(337.5 50 50)">
  <rect x="48" y="6" rx="2" ry="2.34" width="4" height="26" fill="#1b2a4e">
    <animate attributeName="opacity" values="1;0" keyTimes="0;1" dur="1.25s" begin="0s" repeatCount="indefinite"></animate>
  </rect>
</g>
<!-- [ldio] generated by https://loading.io/ --></svg>