/* Movie Detail Page Styles */

/* Hero Banner Section */
.movie-hero {
    position: relative;
    min-height: 60vh;
    background: linear-gradient(to top, var(--bg-primary), transparent);
}

@media (min-width: 1024px) {
    .movie-hero {
        min-height: 70vh;
    }
}

.movie-hero-bg {
    position: absolute;
    inset: 0;
    z-index: 0;
}

.movie-hero-bg img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.movie-hero-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(
        to top,
        var(--bg-primary) 0%,
        rgba(var(--bg-primary-rgb), 0.6) 50%,
        transparent 100%
    );
}

.movie-hero-overlay::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        to right,
        rgba(var(--bg-primary-rgb), 0.8) 0%,
        transparent 50%,
        rgba(var(--bg-primary-rgb), 0.4) 100%
    );
}

.movie-hero-content {
    position: relative;
    z-index: 10;
}

/* Movie Poster */
.movie-poster {
    border-radius: 0.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    transition: transform 0.3s ease;
}

.movie-poster:hover {
    transform: scale(1.02);
}

/* Action Buttons */
.movie-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    font-weight: 600;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.movie-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.movie-action-btn i {
    margin-right: 0.5rem;
}

/* Primary action button */
.btn-primary {
    background: var(--main-color);
    color: white;
}

.btn-primary:hover {
    background: var(--main-color-hover, #16a34a);
}

/* Secondary action button */
.btn-secondary {
    background: var(--bg-button);
    color: var(--text-primary);
}

.btn-secondary:hover {
    background: var(--bg-button-hover);
}

/* Movie Info */
.movie-title {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

@media (min-width: 1024px) {
    .movie-title {
        font-size: 3rem;
    }
}

.movie-subtitle {
    font-size: 1.125rem;
    color: var(--text-tertiary);
    margin-bottom: 1rem;
}

@media (min-width: 1024px) {
    .movie-subtitle {
        font-size: 1.25rem;
    }
}

.movie-meta {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.movie-meta-item {
    display: flex;
    align-items: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.movie-status-badge {
    padding: 0.25rem 0.75rem;
    background: var(--main-color);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 9999px;
}

.movie-rating {
    display: flex;
    align-items: center;
    color: var(--text-secondary);
}

.movie-rating i {
    color: #fbbf24;
    margin-right: 0.25rem;
}

/* Genre Tags */
.genre-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.genre-tag {
    padding: 0.25rem 0.75rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.75rem;
    border-radius: 9999px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.genre-tag:hover {
    background: var(--bg-button);
    color: var(--main-color);
}

/* Description */
.movie-description {
    max-width: 64rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.description-toggle {
    margin-top: 0.5rem;
    color: var(--main-color);
    font-weight: 600;
    background: none;
    border: none;
    cursor: pointer;
    transition: color 0.3s ease;
}

.description-toggle:hover {
    color: var(--main-color-hover, #16a34a);
}

.description-toggle i {
    margin-left: 0.25rem;
    transition: transform 0.3s ease;
}

/* Episodes Section */
.episodes-section {
    background: var(--bg-secondary);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.section-title {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

@media (min-width: 1024px) {
    .section-title {
        font-size: 1.5rem;
    }
}

.section-title i {
    margin-right: 0.75rem;
    color: var(--main-color);
}

/* Server Tabs */
.server-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.server-tab {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.server-tab.active {
    background: var(--main-color);
    color: white;
}

.server-tab:not(.active) {
    background: var(--bg-button);
    color: var(--text-primary);
}

.server-tab:not(.active):hover {
    background: var(--bg-button-hover);
}

/* Episode Grid */
.episodes-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
}

@media (min-width: 640px) {
    .episodes-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

@media (min-width: 768px) {
    .episodes-grid {
        grid-template-columns: repeat(8, 1fr);
    }
}

@media (min-width: 1024px) {
    .episodes-grid {
        grid-template-columns: repeat(10, 1fr);
    }
}

.episode-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 0.75rem;
    background: var(--bg-button);
    color: var(--text-primary);
    font-weight: 600;
    border-radius: 0.5rem;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s ease;
    min-height: 2.5rem;
}

.episode-item:hover {
    background: var(--main-color);
    color: white;
    transform: translateY(-2px);
}

/* Movie Details Info */
.movie-details-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

@media (min-width: 768px) {
    .movie-details-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.detail-item {
    display: flex;
    align-items: flex-start;
}

.detail-label {
    width: 6rem;
    color: var(--text-tertiary);
    font-weight: 600;
    flex-shrink: 0;
}

.detail-value {
    flex: 1;
    color: var(--text-primary);
}

.detail-value a {
    color: var(--main-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.detail-value a:hover {
    color: var(--main-color-hover, #16a34a);
}

/* Sidebar */
.sidebar-section {
    background: var(--bg-secondary);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.sidebar-title {
    display: flex;
    align-items: center;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.sidebar-title i {
    margin-right: 0.5rem;
}

/* Rating Display */
.rating-display {
    text-align: center;
    margin-bottom: 1.5rem;
}

.rating-score {
    font-size: 2.5rem;
    font-weight: 700;
    color: #fbbf24;
    margin-bottom: 0.5rem;
}

.rating-stars {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
}

.rating-stars i {
    font-size: 1.125rem;
}

.rating-count {
    color: var(--text-tertiary);
    font-size: 0.875rem;
}

/* Social Share */
.social-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.social-btn i {
    margin-right: 0.5rem;
}

.social-btn:hover {
    transform: translateY(-2px);
}

/* Stats */
.stats-list {
    space-y: 0.75rem;
}

.stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stat-label {
    color: var(--text-tertiary);
}

.stat-value {
    color: var(--text-primary);
    font-weight: 600;
}

/* Related Movies Swiper */
.related-movies-swiper .swiper-button-next,
.related-movies-swiper .swiper-button-prev {
    color: var(--main-color);
    background: var(--bg-secondary);
    border-radius: 50%;
    width: 40px;
    height: 40px;
}

.related-movies-swiper .swiper-button-next:hover,
.related-movies-swiper .swiper-button-prev:hover {
    background: var(--bg-button);
}

/* Trailer Modal */
.trailer-modal {
    position: fixed;
    inset: 0;
    z-index: 50;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.75);
}

.trailer-modal.hidden {
    display: none;
}

.trailer-content {
    position: relative;
    width: 100%;
    max-width: 64rem;
    margin: 0 1rem;
}

.trailer-close {
    position: absolute;
    top: -3rem;
    right: 0;
    color: white;
    font-size: 1.5rem;
    background: none;
    border: none;
    cursor: pointer;
    transition: color 0.3s ease;
}

.trailer-close:hover {
    color: var(--text-tertiary);
}

.trailer-video {
    position: relative;
    aspect-ratio: 16/9;
    background: black;
    border-radius: 0.5rem;
    overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .movie-title {
        font-size: 1.875rem;
    }
    
    .movie-subtitle {
        font-size: 1rem;
    }
    
    .episodes-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .social-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .movie-title {
        font-size: 1.5rem;
    }
    
    .episodes-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
