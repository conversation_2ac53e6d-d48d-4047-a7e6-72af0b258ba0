@extends('themevieon2025::layouts.app')

@section('title', 'Lỗi ' . ($code ?? 'Unknown') . ' - ' . ($message ?? 'Có lỗi xảy ra'))
@section('description', $description ?? '<PERSON><PERSON> xảy ra lỗi trong quá trình xử lý yêu cầu của bạn. Vui lòng thử lại sau.')

@section('content')
    <div class="min-h-screen bg-theme-primary flex items-center justify-center px-4">
        <div class="max-w-4xl mx-auto text-center">
            <!-- Error Illustration -->
            <div class="mb-8">
                <div class="relative inline-block">
                    <!-- Error Code -->
                    <div class="text-8xl md:text-9xl font-bold text-orange-500 opacity-20 select-none">
                        {{ $code ?? '???' }}
                    </div>

                    <!-- Icon Overlay -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="bg-theme-secondary rounded-full p-6 shadow-2xl">
                            @switch($code ?? 0)
                                @case(403)
                                    <i class="fas fa-lock text-4xl md:text-5xl text-orange-500"></i>
                                    @break
                                @case(429)
                                    <i class="fas fa-hourglass-half text-4xl md:text-5xl text-orange-500"></i>
                                    @break
                                @case(503)
                                    <i class="fas fa-tools text-4xl md:text-5xl text-orange-500"></i>
                                    @break
                                @default
                                    <i class="fas fa-exclamation-triangle text-4xl md:text-5xl text-orange-500"></i>
                            @endswitch
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-theme-primary mb-4">
                    @switch($code ?? 0)
                        @case(403)
                            Truy cập bị từ chối
                            @break
                        @case(429)
                            Quá nhiều yêu cầu
                            @break
                        @case(503)
                            Dịch vụ tạm thời không khả dụng
                            @break
                        @default
                            {{ $message ?? 'Có lỗi xảy ra' }}
                    @endswitch
                </h1>

                <p class="text-lg text-theme-tertiary mb-6 max-w-2xl mx-auto">
                    @switch($code ?? 0)
                        @case(403)
                            Bạn không có quyền truy cập vào trang này. Vui lòng đăng nhập hoặc liên hệ quản trị viên.
                            @break
                        @case(429)
                            Bạn đã gửi quá nhiều yêu cầu trong thời gian ngắn. Vui lòng chờ một chút rồi thử lại.
                            @break
                        @case(503)
                            Website đang trong quá trình bảo trì. Chúng tôi sẽ sớm trở lại. Cảm ơn sự kiên nhẫn của bạn.
                            @break
                        @default
                            {{ $description ?? 'Đã xảy ra lỗi trong quá trình xử lý yêu cầu của bạn. Vui lòng thử lại sau.' }}
                    @endswitch
                </p>
            </div>

            <!-- Status/Info Box -->
            @if($code == 429)
                <div class="mb-8">
                    <div class="inline-flex items-center px-4 py-2 bg-yellow-100 text-yellow-800 rounded-lg">
                        <div class="w-2 h-2 bg-yellow-500 rounded-full mr-2 animate-pulse"></div>
                        <span class="text-sm font-medium">Vui lòng chờ 60 giây trước khi thử lại</span>
                    </div>
                </div>
            @elseif($code == 503)
                <div class="mb-8">
                    <div class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-lg">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                        <span class="text-sm font-medium">Đang bảo trì hệ thống</span>
                    </div>
                </div>
            @endif

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                @if($code != 503)
                    <button onclick="window.history.back()"
                            class="inline-flex items-center px-6 py-3 bg-main hover:bg-green-600 text-white rounded-lg transition-colors font-medium">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Quay lại
                    </button>
                @endif

                <a href="{{ route('home') }}"
                   class="inline-flex items-center px-6 py-3 bg-theme-button hover:bg-theme-button-hover text-theme-primary rounded-lg transition-colors font-medium border border-theme-primary">
                    <i class="fas fa-home mr-2"></i>
                    Trang chủ
                </a>

                @if($code == 403)
                    <a href="{{ route('login') }}"
                       class="inline-flex items-center px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors font-medium">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Đăng nhập
                    </a>
                @endif
            </div>

            <!-- Helpful Actions -->
            @if($code != 503)
                <div class="mb-12">
                    <h3 class="text-lg font-semibold text-theme-primary mb-6">
                        Bạn có thể thử:
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
                        <div class="p-6 bg-theme-secondary rounded-lg">
                            <i class="fas fa-search text-2xl text-blue-500 mb-3"></i>
                            <h4 class="font-medium text-theme-primary mb-2">Tìm kiếm</h4>
                            <p class="text-sm text-theme-tertiary">Tìm kiếm nội dung bạn đang cần</p>
                            <a href="{{ route('search') }}" class="inline-block mt-2 text-main hover:underline text-sm">
                                Tìm kiếm ngay →
                            </a>
                        </div>

                        <div class="p-6 bg-theme-secondary rounded-lg">
                            <i class="fas fa-fire text-2xl text-red-500 mb-3"></i>
                            <h4 class="font-medium text-theme-primary mb-2">Phim hot</h4>
                            <p class="text-sm text-theme-tertiary">Xem những bộ phim đang thịnh hành</p>
                            <a href="{{ route('search', ['sort' => 'view', 'order' => 'desc']) }}"
                               class="inline-block mt-2 text-main hover:underline text-sm">
                                Xem ngay →
                            </a>
                        </div>

                        <div class="p-6 bg-theme-secondary rounded-lg">
                            <i class="fas fa-star text-2xl text-yellow-500 mb-3"></i>
                            <h4 class="font-medium text-theme-primary mb-2">Phim mới</h4>
                            <p class="text-sm text-theme-tertiary">Khám phá những bộ phim mới nhất</p>
                            <a href="{{ route('search', ['sort' => 'created_at', 'order' => 'desc']) }}"
                               class="inline-block mt-2 text-main hover:underline text-sm">
                                Khám phá →
                            </a>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Contact Info -->
            <div class="text-sm text-theme-tertiary">
                <p>Nếu vấn đề vẫn tiếp tục, vui lòng
                    <a href="mailto:<EMAIL>" class="text-main hover:underline">liên hệ hỗ trợ</a>
                </p>
                <p class="mt-2 text-xs">
                    Mã lỗi: {{ $code ?? 'Unknown' }} | Thời gian: {{ now()->format('d/m/Y H:i:s') }}
                </p>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Rate limiting countdown for 429 errors
        @if($code == 429)
        let countdown = 60;
        const countdownInterval = setInterval(() => {
            countdown--;
            const statusElement = document.querySelector('.bg-yellow-100 span');
            if (statusElement && countdown > 0) {
                statusElement.textContent = `Vui lòng chờ ${countdown} giây trước khi thử lại`;
            } else if (countdown <= 0) {
                clearInterval(countdownInterval);
                if (statusElement) {
                    statusElement.textContent = 'Bạn có thể thử lại ngay bây giờ';
                    statusElement.parentElement.classList.remove('bg-yellow-100', 'text-yellow-800');
                    statusElement.parentElement.classList.add('bg-green-100', 'text-green-800');
                }
            }
        }, 1000);
        @endif

        // Auto-refresh for 503 errors
        @if($code == 503)
        setTimeout(() => {
            window.location.reload();
        }, 60000); // Refresh after 1 minute
        @endif
    </script>
@endpush
