document.addEventListener('DOMContentLoaded', function () {
    function getCsrfToken() {
        let token = document.querySelector('meta[name="csrf-token"]');
        if (token) return token.getAttribute('content');
        let input = document.querySelector('input[name="_token"]');
        if (input) return input.value;
        return '';
    }

    function showFormErrors(form, errors) {
        // Xóa lỗi cũ
        form.querySelectorAll('.ajax-error').forEach(e => e.remove());

        for (const [field, messages] of Object.entries(errors)) {
            let input = form.querySelector(`[name="${field}"]`);
            if (input) {
                let div = document.createElement('div');
                div.className = 'ajax-error text-red-500 text-sm mt-1';
                div.innerText = Array.isArray(messages) ? messages.join('\n') : messages;
                input.parentNode.appendChild(div);
            } else if (field === 'general') {
                // <PERSON><PERSON>n thị lỗi chung ở đầu form
                let div = document.createElement('div');
                div.className = 'ajax-error text-red-500 text-sm mb-4 p-3 bg-red-50 border border-red-200 rounded';
                div.innerText = Array.isArray(messages) ? messages.join('\n') : messages;
                form.prepend(div);
            }
        }
    }

    function showFormSuccess(form, message) {
        let div = document.createElement('div');
        div.className = 'ajax-success text-green-500 text-sm mb-2';
        div.innerText = message;
        form.prepend(div);
        setTimeout(() => div.remove(), 3000);
    }

    document.querySelectorAll('form.ajax-form').forEach(form => {
        form.addEventListener('submit', function (e) {
            e.preventDefault();
            // Xóa thông báo cũ
            form.querySelectorAll('.ajax-error, .ajax-success').forEach(e => e.remove());

            let url = form.action;
            let method = (form.method || 'POST').toUpperCase();
            let enctype = form.enctype;
            let headers = {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
            };
            let body;

            // Lấy method spoofing nếu có
            let methodInput = form.querySelector('input[name="_method"]');
            if (methodInput) {
                method = methodInput.value.toUpperCase();
            }

            if (enctype === 'multipart/form-data') {
                body = new FormData(form);
            } else {
                body = new FormData(form);
            }
            // CSRF
            if (!body.has('_token')) {
                body.append('_token', getCsrfToken());
            }

            // Nếu là DELETE/PATCH thì fetch không hỗ trợ body với form-data, cần chuyển sang gửi JSON hoặc dùng FormData
            let fetchOptions = {
                method: method,
                headers: headers,
                body: body,
            };
            if (method === 'DELETE' || method === 'PATCH') {
                // Nếu không upload file, có thể chuyển sang gửi JSON
                if (enctype !== 'multipart/form-data') {
                    let jsonObj = {};
                    body.forEach((v, k) => { jsonObj[k] = v; });
                    fetchOptions.body = JSON.stringify(jsonObj);
                    fetchOptions.headers['Content-Type'] = 'application/json';
                }
            }

            // Loading state
            let submitBtn = form.querySelector('[type="submit"]');
            if (submitBtn) submitBtn.disabled = true;

            fetch(url, fetchOptions)
                .then(async res => {
                    let data;
                    try {
                        data = await res.json();
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        data = {};
                    }

                    console.log('Response status:', res.status, 'Data:', data);

                    if (res.ok) {
                        // Xử lý đặc biệt cho login/register
                        if (form.action.includes('/login') || form.action.includes('/register')) {
                            // Sử dụng global notification thay vì form notification
                            if (window.showNotification) {
                                window.showNotification('success', data.message || 'Thành công!');
                            } else {
                                showFormSuccess(form, data.message || 'Thành công!');
                            }

                            // Đóng modal nếu có
                            const modal = form.closest('[x-data*="showLoginModal"]');
                            if (modal) {
                                // Trigger Alpine.js để đóng modal
                                window.dispatchEvent(new CustomEvent('close-login-modal'));
                                // Hoặc set trực tiếp nếu có Alpine instance
                                setTimeout(() => {
                                    const alpineData = modal._x_dataStack && modal._x_dataStack[0];
                                    if (alpineData && alpineData.showLoginModal !== undefined) {
                                        alpineData.showLoginModal = false;
                                    }
                                }, 100);
                            }

                            // Redirect sau delay dài hơn để user thấy thông báo
                            setTimeout(() => {
                                if (data.redirect) {
                                    window.location.href = data.redirect;
                                } else {
                                    window.location.reload();
                                }
                            }, 2000);
                        } else {
                            // Xử lý bình thường cho các form khác
                            showFormSuccess(form, data.message || 'Thành công!');
                            if (data.redirect) {
                                setTimeout(() => { window.location.href = data.redirect; }, 1000);
                            } else if (method === 'DELETE') {
                                // Nếu là xóa, remove element cha nếu có data.removeSelector
                                if (data.removeSelector) {
                                    let el = form.closest(data.removeSelector);
                                    if (el) el.remove();
                                }
                            }
                        }
                    } else if (res.status === 422 && data.errors) {
                        showFormErrors(form, data.errors);
                    } else if (res.status === 419) {
                        showFormErrors(form, {general: ['Phiên làm việc đã hết hạn. Vui lòng tải lại trang.']});
                    } else {
                        let errorMessage = data.message || `Lỗi ${res.status}: ${res.statusText}`;
                        showFormErrors(form, {general: [errorMessage]});
                    }
                })
                .catch((error) => {
                    console.error('Fetch error:', error);
                    showFormErrors(form, {general: ['Không thể kết nối máy chủ!']});
                })
                .finally(() => {
                    if (submitBtn) submitBtn.disabled = false;
                });
        });
    });
});
