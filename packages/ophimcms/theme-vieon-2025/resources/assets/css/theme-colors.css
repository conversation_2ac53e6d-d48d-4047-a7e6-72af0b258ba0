/* Theme Variables - Only Dark Theme */
:root {
    --main-color: #00ff66;
    --main-color-hover: #16a34a;
    --color-start: #00000000;
    --color-end: #000000FF;
    --bg-primary: #000000;
    --bg-primary-rgb: 0, 0, 0;
    --bg-secondary: #111111;
    --bg-tertiary: #1a1a1a;
    --bg-card: rgba(17, 24, 39, 0.95);
    --bg-overlay: rgba(0, 0, 0, 0.8);
    --bg-header: rgba(0, 0, 0, 0.95);
    --bg-dropdown: rgba(0, 0, 0, 0.95);
    --bg-footer: #000000;
    --bg-menu: #111111;
    --bg-button: #1f2937;
    --bg-button-hover: rgba(255, 255, 255, 0.1);
    --bg-button-active: rgba(255, 255, 255, 0.2);
    --bg-loading: #23242a;
    --text-primary: #ffffff;
    --text-secondary: #e5e7eb;
    --text-tertiary: #9ca3af;
    --text-muted: #6b7280;
    --text-inverse: #000000;
    --border-primary: rgba(255, 255, 255, 0.1);
    --border-secondary: rgba(255, 255, 255, 0.05);
    --border-input: #374151;
    --border-footer: rgba(255, 255, 255, 0.1);
    --shadow-primary: 0 10px 25px rgba(0, 255, 102, 0.1);
    --shadow-secondary: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-dropdown: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Apply theme variables to body */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Theme-aware classes */
.bg-theme-primary { background-color: var(--bg-primary); }
.bg-theme-secondary { background-color: var(--bg-secondary); }
.bg-theme-tertiary { background-color: var(--bg-tertiary); }
.bg-theme-card { background-color: var(--bg-card); }
.bg-theme-overlay { background-color: var(--bg-overlay); }
.bg-theme-header { background-color: var(--bg-header); }
.bg-theme-dropdown { background-color: var(--bg-dropdown); }
.bg-theme-footer { background-color: var(--bg-footer); }
.bg-theme-menu { background-color: var(--bg-menu); }
.bg-theme-button { background-color: var(--bg-button); }
.bg-theme-button-hover { background-color: var(--bg-button-hover); }
.bg-theme-button-active { background-color: var(--bg-button-active); }
.bg-theme-loading { background-color: var(--bg-loading); }

.text-theme-primary { color: var(--text-primary); }
.text-theme-secondary { color: var(--text-secondary); }
.text-theme-tertiary { color: var(--text-tertiary); }
.text-theme-muted { color: var(--text-muted); }
.text-theme-inverse { color: var(--text-inverse); }

.border-theme-primary { border-color: var(--border-primary); }
.border-theme-secondary { border-color: var(--border-secondary); }
.border-theme-input { border-color: var(--border-input); }
.border-theme-footer { border-color: var(--border-footer); }

.shadow-theme-primary { box-shadow: var(--shadow-primary); }
.shadow-theme-secondary { box-shadow: var(--shadow-secondary); }
.shadow-theme-dropdown { box-shadow: var(--shadow-dropdown); }

/* Theme transition */
.theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
