@extends('themevieon2025::layouts.user')

@section('title', 'Phim yêu thích - ' . config('app.name') . ' 2025')

@section('user-content')
    <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-white mb-2">Phim yêu thích</h2>
        <p class="text-gray-400"><PERSON>h sách phim bạn đã yêu thích</p>
    </div>

    {{-- Loading Overlay --}}
    <div id="favorites-loading"
         class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-main"></div>
            <span class="text-white">Đang tải...</span>
        </div>
    </div>

    {{-- Message Containers --}}
    <div id="favorites-error"
         class="hidden mb-6 bg-red-500 bg-opacity-20 border border-red-500 text-red-200 px-4 py-3 rounded-lg">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd"></path>
            </svg>
            <span id="favorites-error-message"></span>
        </div>
    </div>

    <div id="favorites-success"
         class="hidden mb-6 bg-green-500 bg-opacity-20 border border-green-500 text-green-200 px-4 py-3 rounded-lg">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd"></path>
            </svg>
            <span id="favorites-success-message"></span>
        </div>
    </div>

    {{-- Favorites Content --}}
    <div id="favorites-content">
        {{-- Content will be loaded via AJAX --}}
        <div class="flex items-center justify-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-main"></div>
        </div>
    </div>
    </div>
    </div>
@endsection

@push('scripts')
    <script src="{{ asset('themes/vieon-2025/js/user-api.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            loadFavorites();
        });

        async function loadFavorites() {
            const contentEl = document.getElementById('favorites-content');

            try {
                hideMessage();

                const favoritesData = await window.userAPI.getFavorites();

                if (favoritesData.status) {
                    renderFavorites(favoritesData.data);
                } else {
                    showError('Không thể tải danh sách yêu thích');
                }

            } catch (error) {
                console.error('Error loading favorites:', error);
                showError('Có lỗi xảy ra khi tải danh sách yêu thích');
            }
        }

        function renderFavorites(favorites) {
            const contentEl = document.getElementById('favorites-content');

            if (favorites.length > 0) {
                contentEl.innerHTML = `
            <div class="user-content-grid">
                ${favorites.map(favorite => renderFavoriteCard(favorite)).join('')}
            </div>
        `;
            } else {
                contentEl.innerHTML = `
            <div class="text-center py-12">
                <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-gray-800/50 mb-6">
                    <svg class="h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Chưa có phim yêu thích</h3>
                <p class="text-gray-400 mb-6">Bạn chưa thêm phim nào vào danh sách yêu thích.</p>
                <a href="{{ route('home') }}"
                   class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-black bg-main hover:bg-main/90 transition-all duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                    </svg>
                    Khám phá phim mới
                </a>
            </div>
        `;
            }
        }

        function renderFavoriteCard(favorite) {
            const movie = favorite.movie || favorite; // Handle different data structures
            return `
        <div class="user-card group" data-favorite-id="${favorite.id || movie.id}">
            <a href="/phim/${movie.slug}" class="block">
                <div class="relative overflow-hidden">
                    <img src="${movie.poster_url || movie.thumb_url || '/placeholder.jpg'}"
                         class="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
                         alt="${movie.name}">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="absolute bottom-0 left-0 right-0 p-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                        <div class="text-white">
                            <div class="font-semibold text-lg mb-1">${movie.name}</div>
                            ${movie.year ? `<div class="text-sm text-gray-300">${movie.year}</div>` : ''}
                        </div>
                    </div>
                </div>
                <div class="p-4">
                    <div class="font-semibold text-white text-lg mb-2 line-clamp-2">${movie.name}</div>
                    ${movie.year ? `<div class="text-sm text-gray-400 mb-2">${movie.year}</div>` : ''}
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            ${movie.rating ? `
                                <div class="flex items-center text-yellow-400">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    <span class="text-sm">${movie.rating}</span>
                                </div>
                            ` : ''}
                        </div>
                        <div class="text-xs text-gray-500">
                            ${favorite.created_at ? new Date(favorite.created_at).toLocaleDateString('vi-VN') : ''}
                        </div>
                    </div>
                </div>
            </a>
            <div class="p-4 pt-0">
                <button type="button"
                        data-action="remove-favorite"
                        data-movie-id="${movie.id}"
                        class="w-full text-red-400 hover:text-red-300 text-sm py-2 px-3 rounded-lg hover:bg-red-500/10 transition-all duration-200 flex items-center justify-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    <span class="button-text">Xóa khỏi yêu thích</span>
                    <span class="loading-text hidden">Đang xóa...</span>
                </button>
            </div>
        </div>
    `;
        }

        // Override the default remove favorite handler for this page
        document.addEventListener('click', async function (e) {
            if (e.target.matches('[data-action="remove-favorite"]') || e.target.closest('[data-action="remove-favorite"]')) {
                e.preventDefault();

                const button = e.target.matches('[data-action="remove-favorite"]') ? e.target : e.target.closest('[data-action="remove-favorite"]');
                const movieId = button.dataset.movieId;
                const card = button.closest('[data-favorite-id]');
                const buttonText = button.querySelector('.button-text');
                const loadingText = button.querySelector('.loading-text');

                try {
                    // Show loading state
                    button.disabled = true;
                    buttonText.classList.add('hidden');
                    loadingText.classList.remove('hidden');

                    const result = await window.userAPI.removeFromFavorites(movieId);

                    if (result.status) {
                        showSuccess(result.message);

                        // Remove card with animation
                        card.style.transform = 'scale(0.8)';
                        card.style.opacity = '0';
                        setTimeout(() => {
                            card.remove();

                            // Check if no favorites left
                            const remainingCards = document.querySelectorAll('[data-favorite-id]');
                            if (remainingCards.length === 0) {
                                renderFavorites([]);
                            }
                        }, 300);
                    } else {
                        showError(result.message || 'Có lỗi xảy ra');
                    }

                } catch (error) {
                    console.error('Error removing favorite:', error);
                    showError('Có lỗi xảy ra khi xóa khỏi yêu thích');
                } finally {
                    // Reset button state
                    button.disabled = false;
                    buttonText.classList.remove('hidden');
                    loadingText.classList.add('hidden');
                }
            }
        });

        function showLoading(show) {
            const loadingEl = document.getElementById('favorites-loading');
            if (show) {
                loadingEl.classList.remove('hidden');
            } else {
                loadingEl.classList.add('hidden');
            }
        }

        function showError(message) {
            const errorEl = document.getElementById('favorites-error');
            const messageEl = document.getElementById('favorites-error-message');
            messageEl.textContent = message;
            errorEl.classList.remove('hidden');

            // Auto hide after 5 seconds
            setTimeout(() => {
                errorEl.classList.add('hidden');
            }, 5000);
        }

        function showSuccess(message) {
            const successEl = document.getElementById('favorites-success');
            const messageEl = document.getElementById('favorites-success-message');
            messageEl.textContent = message;
            successEl.classList.remove('hidden');

            // Auto hide after 3 seconds
            setTimeout(() => {
                successEl.classList.add('hidden');
            }, 3000);
        }

        function hideMessage() {
            document.getElementById('favorites-error').classList.add('hidden');
            document.getElementById('favorites-success').classList.add('hidden');
        }
    </script>
@endpush
