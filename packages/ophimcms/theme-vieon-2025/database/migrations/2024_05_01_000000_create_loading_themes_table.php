<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Helpers\MigrationHelper;

class CreateLoadingThemesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        MigrationHelper::safeCreateTable('loading_themes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('title');
            $table->string('subtitle')->nullable();
            $table->text('description')->nullable();
            $table->string('background_color', 7)->default('#23242a');
            $table->string('text_color', 7)->default('#ffffff');
            $table->string('accent_color')->default('var(--main-color, #00ff66)');
            $table->boolean('is_active')->default(true);
            $table->integer('priority')->default(0);
            $table->json('holiday_rules')->nullable();
            $table->timestamps();
            
            $table->index(['slug', 'is_active']);
            $table->index('priority');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loading_themes');
    }
} 