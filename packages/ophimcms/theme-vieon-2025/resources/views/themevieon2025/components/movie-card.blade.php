@php
    $layout = $layout ?? 'grid';
    $showRating = $showRating ?? false;
    $showActions = $showActions ?? false;
    $size = $size ?? 'normal'; // normal, small, large
@endphp

    <!-- Movie Card Component -->
<div
    class="movie-card group relative {{ $layout === 'list' ? 'flex' : 'block' }} bg-theme-secondary rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">

    <!-- Movie Poster/Thumbnail -->
    <div
        class="relative {{ $layout === 'list' ? 'w-24 flex-shrink-0' : 'w-full' }} {{ $size === 'small' ? 'aspect-[2/3]' : ($size === 'large' ? 'aspect-[3/4]' : 'aspect-[2/3]') }}">
        <a href="{{ route('movies.show', $movie->slug) }}" class="block w-full h-full">
            <img src="{{ $movie->thumb_url }}"
                 alt="{{ $movie->name }}"
                 class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                 loading="lazy">
        </a>

        <!-- Quality Badge -->
        @if($movie->quality)
            <div class="absolute top-2 left-2 px-2 py-1 bg-main text-white text-xs font-semibold rounded-lg">
                {{ $movie->quality }}
            </div>
        @endif

        <!-- Episode Badge -->
        @if($movie->episode_current)
            <div class="absolute top-2 right-2 px-2 py-1 bg-black bg-opacity-70 text-white text-xs rounded-lg">
                {{ $movie->episode_current }}
            </div>
        @endif

        <!-- Play Button Overlay -->
        <div
            class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
            <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="w-12 h-12 bg-main rounded-full flex items-center justify-center">
                    <i class="fas fa-play text-white ml-1"></i>
                </div>
            </div>
        </div>

        <!-- Actions Overlay -->
        @if($showActions)
            <div class="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="flex gap-1">
                    @auth
                        <!-- Add to Favorites -->
                        <button onclick="toggleFavorite({{ $movie->id }})"
                                data-movie-id="{{ $movie->id }}"
                                data-is-favorite="{{ isset($movie->userFavorite) && $movie->userFavorite ? 'true' : 'false' }}"
                                class="card-favorite-btn w-8 h-8 rounded-full flex items-center justify-center transition-colors
                                       {{ isset($movie->userFavorite) && $movie->userFavorite
                                          ? 'bg-red-500 hover:bg-red-600 text-white'
                                          : 'bg-black bg-opacity-70 hover:bg-opacity-90 text-white' }}"
                                title="{{ isset($movie->userFavorite) && $movie->userFavorite ? 'Bỏ khỏi yêu thích' : 'Thêm vào yêu thích' }}">
                            <i class="{{ isset($movie->userFavorite) && $movie->userFavorite ? 'fas' : 'far' }} fa-heart text-xs"></i>
                        </button>

                        <!-- Add to Watchlist -->
                        <button onclick="toggleWatchlist({{ $movie->id }})"
                                data-movie-id="{{ $movie->id }}"
                                data-is-watchlist="{{ isset($movie->userWatchlist) && $movie->userWatchlist ? 'true' : 'false' }}"
                                class="card-watchlist-btn w-8 h-8 rounded-full flex items-center justify-center transition-colors
                                       {{ isset($movie->userWatchlist) && $movie->userWatchlist
                                          ? 'bg-blue-500 hover:bg-blue-600 text-white'
                                          : 'bg-black bg-opacity-70 hover:bg-opacity-90 text-white' }}"
                                title="{{ isset($movie->userWatchlist) && $movie->userWatchlist ? 'Xóa khỏi danh sách xem' : 'Thêm vào danh sách xem' }}">
                            <i class="fas fa-bookmark text-xs"></i>
                        </button>
                    @endauth

                    <!-- Share -->
                    <button onclick="shareMovie('{{ $movie->name }}', '{{ route('movies.show', $movie->slug) }}')"
                            class="w-8 h-8 bg-black bg-opacity-70 hover:bg-opacity-90 text-white rounded-full flex items-center justify-center transition-colors"
                            title="Chia sẻ">
                        <i class="fas fa-share text-xs"></i>
                    </button>
                </div>
            </div>
        @endif
    </div>

    <!-- Movie Info -->
    <div class="p-3 {{ $layout === 'list' ? 'flex-1' : '' }}">
        <!-- Title -->
        <h3 class="font-semibold text-theme-primary mb-1 {{ $size === 'small' ? 'text-sm' : 'text-base' }} line-clamp-2">
            <a href="{{ route('movies.show', $movie->slug) }}"
               class="hover:text-main transition-colors">
                {{ $movie->name }}
            </a>
        </h3>

        <!-- Original Name -->
        @if($movie->origin_name && $movie->origin_name !== $movie->name)
            <p class="text-theme-tertiary text-xs mb-2 line-clamp-1">
                {{ $movie->origin_name }}
            </p>
        @endif

        <!-- Movie Details -->
        <div class="flex items-center gap-2 text-xs text-theme-tertiary mb-2">
            @if($movie->publish_year)
                <span>{{ $movie->publish_year }}</span>
                <span>•</span>
            @endif

            @if($movie->time)
                <span>{{ $movie->time }}</span>
                <span>•</span>
            @endif

            <span>{{ number_format($movie->view_total) }} lượt xem</span>
        </div>

        <!-- Rating -->
        @if($showRating && $movie->rating_star > 0)
            <div class="flex items-center gap-1 mb-2">
                <div class="flex text-yellow-400 text-xs">
                    @for($i = 1; $i <= 5; $i++)
                        @if($i <= $movie->rating_star)
                            <i class="fas fa-star"></i>
                        @else
                            <i class="far fa-star"></i>
                        @endif
                    @endfor
                </div>
                <span class="text-theme-tertiary text-xs">{{ number_format($movie->rating_star, 1) }}</span>
                @if($movie->rating_count > 0)
                    <span class="text-theme-tertiary text-xs">({{ $movie->rating_count }})</span>
                @endif
            </div>
        @endif

        <!-- Categories -->
        @if($movie->categories && $movie->categories->count() > 0)
            <div class="flex flex-wrap gap-1 mb-2">
                @foreach($movie->categories->take(2) as $category)
                    <a href="{{ route('categories.movies.index', $category->slug) }}"
                       class="px-2 py-1 bg-theme-button hover:bg-theme-button-hover text-xs rounded-lg transition-colors">
                        {{ $category->name }}
                    </a>
                @endforeach

                @if($movie->categories->count() > 2)
                    <span class="px-2 py-1 bg-theme-button text-xs rounded-lg">
                        +{{ $movie->categories->count() - 2 }}
                    </span>
                @endif
            </div>
        @endif

        <!-- Description (for list layout) -->
        @if($layout === 'list' && $movie->content)
            <p class="text-theme-tertiary text-sm line-clamp-2 mb-3">
                {{ strip_tags($movie->content) }}
            </p>
        @endif

        <!-- Action Buttons (for list layout) -->
        @if($layout === 'list')
            <div class="flex items-center gap-2">
                <a href="{{ route('movies.show', $movie->slug) }}"
                   class="px-3 py-1 bg-main hover:bg-green-600 text-white text-sm rounded-lg transition-colors">
                    Xem phim
                </a>

                @if($movie->episodes && $movie->episodes->count() > 0)
                    <a href="{{ route('episodes.show', [$movie->slug, $movie->episodes->first()->slug]) }}"
                       class="px-3 py-1 bg-theme-button hover:bg-theme-button-hover text-theme-primary text-sm rounded-lg transition-colors">
                        Xem ngay
                    </a>
                @endif
            </div>
        @endif
    </div>

    <!-- Quick Actions (for grid layout) -->
    @if($layout === 'grid' && $showActions)
        <div class="px-3 pb-3">
            <div class="flex items-center justify-between">
                <a href="{{ route('movies.show', $movie->slug) }}"
                   class="flex-1 px-3 py-2 bg-main hover:bg-green-600 text-white text-sm text-center rounded-lg transition-colors mr-2">
                    Chi tiết
                </a>

                @if($movie->episodes && $movie->episodes->count() > 0)
                    <a href="{{ route('episodes.show', [$movie->slug, $movie->episodes->first()->slug]) }}"
                       class="px-3 py-2 bg-theme-button hover:bg-theme-button-hover text-theme-primary text-sm rounded-lg transition-colors">
                        <i class="fas fa-play"></i>
                    </a>
                @endif
            </div>
        </div>
    @endif
</div>
