@extends('themevieon2025::layouts.user')

@section('title', '<PERSON><PERSON>h giá của tôi - ' . config('app.name') . ' 2025')

@section('user-content')
    <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-white mb-2"><PERSON><PERSON><PERSON> giá của tôi</h2>
        <p class="text-gray-400">Quản lý tất cả đánh giá bạn đã đăng</p>
    </div>

    @if($ratings->count())
        <div class="space-y-6">
            @foreach($ratings as $rating)
                <div class="user-card">
                    <div class="p-6">
                        <div class="flex items-start space-x-4">
                            <img
                                src="{{ $rating->user->avatar ?? asset('themes/vieon-2025/images/default-avatar.png') }}"
                                class="w-12 h-12 rounded-full object-cover flex-shrink-0">
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center space-x-2">
                                        <span class="font-semibold text-white">{{ $rating->user->name }}</span>
                                        <span
                                            class="text-xs text-gray-400">{{ $rating->created_at->diffForHumans() }}</span>
                                    </div>
                                    @if(Auth::id() === $rating->user_id)
                                        <form method="POST"
                                              action="{{ route('api.ratings.destroy', $rating->movie_id) }}"
                                              class="inline ajax-form">
                                            @csrf @method('DELETE')
                                            <button type="submit"
                                                    class="text-red-400 hover:text-red-300 text-sm py-1 px-2 rounded hover:bg-red-500/10 transition-all duration-200 flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor"
                                                     viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                          stroke-width="2"
                                                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                                Xóa đánh giá
                                            </button>
                                        </form>
                                    @endif
                                </div>

                                <div class="flex items-center mb-3">
                                    <div class="flex items-center space-x-1 mr-3">
                                        @for($i = 1; $i <= 5; $i++)
                                            <svg
                                                class="w-5 h-5 {{ $i <= $rating->score ? 'text-yellow-400' : 'text-gray-600' }}"
                                                fill="currentColor" viewBox="0 0 20 20">
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                        @endfor
                                    </div>
                                    <span class="text-yellow-400 font-bold">{{ $rating->score }}/5</span>
                                </div>

                                @if($rating->review)
                                    <div class="text-white mb-4 leading-relaxed">{{ $rating->review }}</div>
                                @endif

                                @if($rating->movie)
                                    <div class="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg">
                                        <img src="{{ $rating->movie->poster_url }}"
                                             class="w-16 h-20 object-cover rounded">
                                        <div class="flex-1">
                                            <div class="font-medium text-white">{{ $rating->movie->name }}</div>
                                            <div class="text-sm text-gray-400">{{ $rating->movie->year ?? 'N/A' }}</div>
                                            <a href="{{ route('movies.show', $rating->movie->slug) }}"
                                               class="text-main hover:text-main/80 text-sm transition-colors">
                                                Xem phim →
                                            </a>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        @if($ratings->hasPages())
            <div class="mt-8 flex justify-center">
                <div class="bg-gray-800/50 rounded-lg p-4">
                    {{ $ratings->links() }}
                </div>
            </div>
        @endif
    @else
        <div class="text-center py-12">
            <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-gray-800/50 mb-6">
                <svg class="h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-white mb-2">Chưa có đánh giá nào</h3>
            <p class="text-gray-400 mb-6">Bạn chưa đánh giá phim nào hoặc tất cả đánh giá đã được xóa.</p>
            <a href="{{ route('home') }}"
               class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-black bg-main hover:bg-main/90 transition-all duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                </svg>
                Khám phá phim mới
            </a>
        </div>
        @endif
        </div>
        </div>
        @endsection
