/**
 * Advanced Search Component - Vanilla JavaScript
 * Provides real-time search suggestions and enhanced search functionality
 */

class AdvancedSearch {
    constructor() {
        this.searchContainers = document.querySelectorAll('[data-advanced-search]');
        this.searchTimeout = null;
        this.currentRequest = null;
        this.cache = new Map();
        this.isInitialized = false;
        this.init();
    }

    init() {
        if (this.isInitialized) return;

        this.searchContainers.forEach(container => {
            this.initializeContainer(container);
        });

        this.isInitialized = true;
        console.log('✅ Advanced Search initialized');
    }

    initializeContainer(container) {
        const input = container.querySelector('input[name="keyword"]');
        const form = container.querySelector('form');

        if (!input || !form) return;

        // Create suggestions dropdown
        this.createSuggestionsDropdown(container, input);

        // Bind events
        this.bindEvents(container, input, form);
    }

    createSuggestionsDropdown(container, input) {
        // Check if dropdown already exists in HTML
        let dropdown = container.querySelector('.suggestions-dropdown');

        if (!dropdown) {
            // Create dropdown if it doesn't exist
            dropdown = document.createElement('div');
            dropdown.className = 'suggestions-dropdown absolute top-full left-0 right-0 bg-theme-dropdown border border-theme-primary rounded-b-lg shadow-lg z-50 hidden max-h-96 overflow-y-auto';

            // Insert dropdown after input's parent
            const inputParent = input.closest('.relative');
            if (inputParent) {
                inputParent.appendChild(dropdown);
            }
        }

        // Ensure dropdown has content container
        if (!dropdown.querySelector('.suggestions-content')) {
            dropdown.innerHTML = `
                <div class="suggestions-content">
                    <!-- Content will be populated by JavaScript -->
                </div>
            `;
        }
    }

    bindEvents(container, input, form) {
        // Input events
        input.addEventListener('input', (e) => {
            this.handleInput(e, container);
        });

        input.addEventListener('focus', (e) => {
            this.handleFocus(e, container);
        });

        input.addEventListener('keydown', (e) => {
            this.handleKeydown(e, container);
        });

        // Form submit
        form.addEventListener('submit', (e) => {
            this.handleSubmit(e, container);
        });

        // Click outside to close
        document.addEventListener('click', (e) => {
            if (!container.contains(e.target)) {
                this.hideSuggestions(container);
            }
        });
    }

    handleInput(event, container) {
        const query = event.target.value.trim();

        clearTimeout(this.searchTimeout);

        if (query.length >= 2) {
            this.searchTimeout = setTimeout(() => {
                this.fetchSuggestions(query, container).then(r => {
                    if (r && r.movies && r.movies.length > 0) {
                        this.displaySuggestions(r, container);
                    } else {
                        this.showNoResults(container);
                    }
                });
            }, 300);
        } else {
            this.hideSuggestions(container);
        }
    }

    handleFocus(event, container) {
        const query = event.target.value.trim();

        if (query.length >= 2) {
            this.showSuggestions(container);
        } else {
            this.showPopularSuggestions(container).then(r => {
                if (r && r.movies && r.movies.length > 0) {
                    this.displaySuggestions(r, container);
                } else {
                    this.showNoResults(container);
                }
            });
        }
    }

    handleKeydown(event, container) {
        const dropdown = container.querySelector('.suggestions-dropdown');
        if (!dropdown || dropdown.classList.contains('hidden')) return;

        const items = dropdown.querySelectorAll('.suggestion-item');
        let currentIndex = Array.from(items).findIndex(item =>
            item.classList.contains('highlighted')
        );

        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                currentIndex = Math.min(currentIndex + 1, items.length - 1);
                this.highlightItem(items, currentIndex);
                break;

            case 'ArrowUp':
                event.preventDefault();
                currentIndex = Math.max(currentIndex - 1, -1);
                this.highlightItem(items, currentIndex);
                break;

            case 'Enter':
                event.preventDefault();
                if (currentIndex >= 0 && items[currentIndex]) {
                    const link = items[currentIndex].querySelector('a');
                    if (link) {
                        window.location.href = link.href;
                    }
                } else {
                    // Submit form normally
                    container.querySelector('form').submit();
                }
                break;

            case 'Escape':
                this.hideSuggestions(container);
                event.target.blur();
                break;
        }
    }

    handleSubmit(event, container) {
        // Allow normal form submission
        this.hideSuggestions(container);
    }

    async fetchSuggestions(query, container) {
        // Check cache first
        const cacheKey = `search_${query.toLowerCase()}`;
        if (this.cache.has(cacheKey)) {
            this.displaySuggestions(this.cache.get(cacheKey), container);
            return;
        }

        // Cancel previous request
        if (this.currentRequest) {
            this.currentRequest.abort();
        }

        // Show loading
        this.showLoading(container);

        try {
            const apiUrl = container.dataset.apiUrl || '/api/search';
            const controller = new AbortController();
            this.currentRequest = controller;

            const response = await fetch(`${apiUrl}?keyword=${encodeURIComponent(query)}&limit=8`, {
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            // Cache results
            this.cache.set(cacheKey, data);

            // Display suggestions
            this.displaySuggestions(data, container);

        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('Search suggestions error:', error);
                this.showError(container);
            }
        } finally {
            this.currentRequest = null;
        }
    }

    async showPopularSuggestions(container) {
        const cacheKey = 'popular_searches';

        if (this.cache.has(cacheKey)) {
            this.displaySuggestions(this.cache.get(cacheKey), container, 'Tìm kiếm phổ biến');
            return;
        }

        try {
            const popularUrl = container.dataset.popularUrl || '/api/search/popular';
            const response = await fetch(popularUrl, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.cache.set(cacheKey, data);
                this.displaySuggestions(data, container, 'Tìm kiếm phổ biến');
            }
        } catch (error) {
            console.error('Popular suggestions error:', error);
        }
    }

    displaySuggestions(data, container, title = 'Kết quả tìm kiếm') {
        const dropdown = container.querySelector('.suggestions-dropdown');
        const content = dropdown.querySelector('.suggestions-content');

        if (!data || !data.movies || data.movies.length === 0) {
            this.showNoResults(container);
            return;
        }

        content.innerHTML = `
            <div class="p-3 bg-theme-secondary border-b border-theme-primary">
                <h4 class="text-sm font-semibold text-theme-primary">${title}</h4>
            </div>
            <div class="max-h-80 overflow-y-auto">
                ${data.movies.map((movie, index) => `
                    <div class="suggestion-item p-3 hover:bg-theme-button-hover transition-colors cursor-pointer ${index === 0 ? 'highlighted' : ''}" data-index="${index}">
                        <a href="${movie.url}" class="flex items-center gap-3">
                            <img src="${movie.thumb_url}"
                                 alt="${movie.name}"
                                 class="w-12 h-16 object-cover rounded flex-shrink-0"
                                 loading="lazy">
                            <div class="flex-1 min-w-0">
                                <h5 class="font-medium text-theme-primary truncate">${movie.name}</h5>
                                <p class="text-sm text-theme-tertiary truncate">${movie.origin_name}</p>
                                <div class="flex items-center gap-2 text-xs text-theme-tertiary mt-1">
                                    <span>${movie.publish_year || 'N/A'}</span>
                                    <span>•</span>
                                    <span>${movie.quality || 'HD'}</span>
                                    <span>•</span>
                                    <span>${movie.episode_current || 'Full'}</span>
                                </div>
                            </div>
                        </a>
                    </div>
                `).join('')}
            </div>
            <div class="p-3 bg-theme-secondary border-t border-theme-primary">
                <a href="/search?keyword=${encodeURIComponent(container.querySelector('input').value)}"
                   class="text-sm text-main hover:underline">
                    Xem tất cả kết quả →
                </a>
            </div>
        `;
        this.showSuggestions(container);
    }

    showLoading(container) {
        const dropdown = container.querySelector('.suggestions-dropdown');
        const content = dropdown.querySelector('.suggestions-content');

        content.innerHTML = `
            <div class="p-6 text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-main mx-auto mb-2"></div>
                <p class="text-sm text-theme-tertiary">Đang tìm kiếm...</p>
            </div>
        `;

        this.showSuggestions(container);
    }

    showError(container) {
        const dropdown = container.querySelector('.suggestions-dropdown');
        const content = dropdown.querySelector('.suggestions-content');

        content.innerHTML = `
            <div class="p-6 text-center">
                <i class="fas fa-exclamation-triangle text-2xl text-red-500 mb-2"></i>
                <p class="text-sm text-theme-tertiary">Có lỗi xảy ra khi tìm kiếm</p>
            </div>
        `;

        this.showSuggestions(container);
    }

    showNoResults(container) {
        const dropdown = container.querySelector('.suggestions-dropdown');
        const content = dropdown.querySelector('.suggestions-content');

        content.innerHTML = `
            <div class="p-6 text-center">
                <i class="fas fa-search text-2xl text-theme-tertiary mb-2"></i>
                <p class="text-sm text-theme-tertiary">Không tìm thấy kết quả</p>
            </div>
        `;

        this.showSuggestions(container);
    }

    showSuggestions(container) {
        const dropdown = container.querySelector('.suggestions-dropdown');
        dropdown.classList.remove('hidden');
    }

    hideSuggestions(container) {
        const dropdown = container.querySelector('.suggestions-dropdown');
        dropdown.classList.add('hidden');
    }

    highlightItem(items, index) {
        items.forEach(item => item.classList.remove('highlighted'));

        if (index >= 0 && items[index]) {
            items[index].classList.add('highlighted');
            items[index].scrollIntoView({ block: 'nearest' });
        }
    }
}

// Initialize when DOM is ready (only if not already initialized)
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        if (!window.advancedSearchInstance) {
            window.advancedSearchInstance = new AdvancedSearch();
        }
    });
} else {
    if (!window.advancedSearchInstance) {
        window.advancedSearchInstance = new AdvancedSearch();
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedSearch;
}
