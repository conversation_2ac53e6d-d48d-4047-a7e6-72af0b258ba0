/**
 * Common JavaScript Functions for VieOn 2025 Theme
 * Shared functions used across multiple components
 */

// Global movie functions
window.MovieFunctions = {

    /**
     * Toggle favorite status for a movie
     * @param {number|string} movieId - Movie ID or slug
     * @param {HTMLElement} buttonElement - Optional button element for UI updates
     */
    toggleFavorite: function (movieId, buttonElement = null) {
        console.log('toggleFavorite called with movieId:', movieId);

        // Check if user is authenticated
        if (!window.userAPI) {
            console.log('User not authenticated, showing login modal');
            // Show login modal using Alpine.js custom event
            try {
                window.dispatchEvent(new CustomEvent('show-login-modal'));
                console.log('Login modal event dispatched');
            } catch (error) {
                // Fallback to redirect if modal event fails
                console.log('Modal event failed, redirecting to login:', error);
                window.location.href = '/login';
            }
            return;
        }

        console.log('User authenticated, calling API');

        // Find button element for UI updates
        let button = buttonElement;
        if (!button && typeof event !== 'undefined' && event.target) {
            button = event.target.closest('button');
        }
        if (!button) {
            // Try to find button by onclick attribute or data attribute
            button = document.querySelector(`button[onclick*="toggleFavorite(${movieId})"]`) ||
                     document.querySelector(`button[data-movie-id="${movieId}"]`);
        }

        // Add loading state
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
        }

        // Call user API to toggle favorite
        window.userAPI.toggleFavorite(movieId)
            .then(response => {
                console.log('API response:', response);
                if (response.status) {
                    if (button) {
                        // Add toggle animation
                        button.classList.add('toggling');
                        setTimeout(() => {
                            button.classList.remove('toggling');
                        }, 600);

                        // Update button state
                        window.MovieFunctions.updateFavoriteButton(button, response.is_favorite);
                    }

                    // Show notification
                    if (window.showNotification) {
                        window.showNotification('success', response.message);
                    } else {
                        console.log('showNotification not available yet');
                    }
                } else {
                    // Show error
                    if (window.showNotification) {
                        window.showNotification('error', response.message || 'Có lỗi xảy ra');
                    } else {
                        console.error('Error:', response.message);
                    }
                }
            })
            .catch(error => {
                console.error('Toggle favorite error:', error);
                if (window.showNotification) {
                    window.showNotification('error', 'Không thể kết nối đến server');
                } else {
                    alert('Có lỗi xảy ra khi thêm/xóa yêu thích');
                }
            })
            .finally(() => {
                // Remove loading state
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            });
    },

    /**
     * Show trailer modal
     * @param {string} trailerUrl - YouTube or video URL
     */
    showTrailer: function (trailerUrl) {
        if (!trailerUrl) {
            console.log('No trailer URL provided');
            return;
        }

        // Create or get trailer modal
        let modal = document.getElementById('trailer-modal');
        if (!modal) {
            modal = this.createTrailerModal();
        }

        // Extract video ID if YouTube URL
        let embedUrl = trailerUrl;
        if (trailerUrl.includes('youtube.com') || trailerUrl.includes('youtu.be')) {
            const videoId = this.extractYouTubeId(trailerUrl);
            if (videoId) {
                embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=1`;
            }
        }

        // Set trailer content
        const trailerContent = modal.querySelector('#trailer-content');
        if (trailerContent) {
            trailerContent.innerHTML = `
                <iframe width="100%" height="100%"
                        src="${embedUrl}"
                        frameborder="0"
                        allow="autoplay; encrypted-media"
                        allowfullscreen>
                </iframe>
            `;
        }

        // Show modal
        modal.classList.remove('hidden');
        modal.classList.add('flex');
        document.body.style.overflow = 'hidden';
    },

    /**
     * Hide trailer modal
     */
    hideTrailer: function () {
        const modal = document.getElementById('trailer-modal');
        if (modal) {
            const trailerContent = modal.querySelector('#trailer-content');
            if (trailerContent) {
                trailerContent.innerHTML = '';
            }
            modal.classList.add('hidden');
            modal.classList.remove('flex');
            document.body.style.overflow = 'auto';
        }
    },

    /**
     * Create trailer modal if it doesn't exist
     */
    createTrailerModal: function () {
        const modal = document.createElement('div');
        modal.id = 'trailer-modal';
        modal.className = 'fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-75';
        modal.innerHTML = `
            <div class="relative w-full max-w-4xl mx-4">
                <button onclick="MovieFunctions.hideTrailer()"
                        class="absolute -top-12 right-0 text-white hover:text-gray-300 text-2xl">
                    <i class="fas fa-times"></i>
                </button>

                <div class="relative aspect-video bg-black rounded-lg overflow-hidden">
                    <div id="trailer-content"></div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        return modal;
    },

    /**
     * Extract YouTube video ID from URL
     * @param {string} url - YouTube URL
     * @returns {string|null} Video ID
     */
    extractYouTubeId: function (url) {
        const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
        const match = url.match(regExp);
        return (match && match[2].length === 11) ? match[2] : null;
    },

    /**
     * Social sharing functions
     */
    shareToFacebook: function (url, title) {
        const shareUrl = encodeURIComponent(url || window.location.href);
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${shareUrl}`, '_blank', 'width=600,height=400');
    },

    shareToTwitter: function (url, title) {
        const shareUrl = encodeURIComponent(url || window.location.href);
        const shareTitle = encodeURIComponent(title || document.title);
        window.open(`https://twitter.com/intent/tweet?url=${shareUrl}&text=${shareTitle}`, '_blank', 'width=600,height=400');
    },

    shareToTelegram: function (url, title) {
        const shareUrl = encodeURIComponent(url || window.location.href);
        const shareTitle = encodeURIComponent(title || document.title);
        window.open(`https://t.me/share/url?url=${shareUrl}&text=${shareTitle}`, '_blank');
    },

    copyLink: function (url) {
        const linkToCopy = url || window.location.href;
        navigator.clipboard.writeText(linkToCopy).then(() => {
            if (window.showNotification) {
                window.showNotification('success', 'Đã sao chép link!');
            } else {
                alert('Đã sao chép link!');
            }
        }).catch(err => {
            console.error('Could not copy text: ', err);
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = linkToCopy;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                if (window.showNotification) {
                    window.showNotification('success', 'Đã sao chép link!');
                } else {
                    alert('Đã sao chép link!');
                }
            } catch (err) {
                console.error('Fallback: Could not copy text: ', err);
            }
            document.body.removeChild(textArea);
        });
    },

    /**
     * Toggle watchlist status for a movie
     * @param {number|string} movieId - Movie ID or slug
     */
    toggleWatchlist: function (movieId) {
        // Check if user is authenticated
        if (!window.userAPI) {
            // Show login modal using Alpine.js custom event
            try {
                window.dispatchEvent(new CustomEvent('show-login-modal'));
            } catch (error) {
                // Fallback to redirect if modal event fails
                window.location.href = '/login';
            }
            return;
        }

        // Find button element for UI updates
        let button = null;
        if (typeof event !== 'undefined' && event.target) {
            button = event.target.closest('button');
        }
        if (!button) {
            // Try to find button by onclick attribute or data attribute
            button = document.querySelector(`button[onclick*="toggleWatchlist(${movieId})"]`) ||
                     document.querySelector(`button[data-movie-id="${movieId}"][data-is-watchlist]`);
        }

        // Add loading state
        if (button) {
            button.classList.add('loading');
            button.disabled = true;
        }

        // Call user API to toggle watchlist
        window.userAPI.toggleWatchlist(movieId)
            .then(response => {
                if (response.status) {
                    if (button) {
                        // Add toggle animation
                        button.classList.add('toggling');
                        setTimeout(() => {
                            button.classList.remove('toggling');
                        }, 600);

                        // Update button state
                        window.MovieFunctions.updateWatchlistButton(button, response.is_watchlist);
                    }

                    // Show notification
                    if (window.showNotification) {
                        window.showNotification('success', response.message);
                    } else {
                        // Fallback to custom event
                        window.dispatchEvent(new CustomEvent('show-notification', {
                            detail: {
                                type: 'success',
                                message: response.message
                            }
                        }));
                    }
                } else {
                    // Show error
                    if (window.showNotification) {
                        window.showNotification('error', response.message || 'Có lỗi xảy ra');
                    }
                }
            })
            .catch(error => {
                console.error('Toggle watchlist error:', error);
                if (window.showNotification) {
                    window.showNotification('error', 'Không thể kết nối đến server');
                }
            });
    },

    /**
     * Share movie function
     * @param {string} movieName - Movie name
     * @param {string} movieUrl - Movie URL
     */
    shareMovie: function (movieName, movieUrl) {
        const title = movieName || document.title;
        const url = movieUrl || window.location.href;

        // Use native Web Share API if available
        if (navigator.share) {
            navigator.share({
                title: title,
                url: url
            }).catch(error => {
                console.error('Error sharing:', error);
                // Fallback to copy link
                this.copyLink(url);
            });
        } else {
            // Fallback: copy to clipboard
            this.copyLink(url);
        }
    },

    /**
     * Update favorite button UI based on favorite status
     * @param {HTMLElement} button - Button element to update
     * @param {boolean} isFavorite - Whether movie is favorited
     */
    updateFavoriteButton: function (button, isFavorite) {
        if (!button) return;

        const icon = button.querySelector('i');
        const text = button.querySelector('.favorite-text');

        // Update data attribute
        button.setAttribute('data-is-favorite', isFavorite ? 'true' : 'false');

        if (isFavorite) {
            // Movie is favorited - show filled heart with red styling
            if (icon) {
                icon.classList.remove('far');
                icon.classList.add('fas');
            }
            if (text) {
                text.textContent = 'Đã yêu thích';
            }

            // Update button styling for favorited state
            button.classList.remove('bg-theme-secondary', 'hover:bg-theme-tertiary', 'text-theme-primary', 'border-transparent', 'hover:border-theme-accent');
            button.classList.add('bg-red-500', 'hover:bg-red-600', 'text-white', 'border-red-500', 'hover:border-red-600');
            button.title = 'Bỏ khỏi yêu thích';
        } else {
            // Movie is not favorited - show outline heart with default styling
            if (icon) {
                icon.classList.remove('fas');
                icon.classList.add('far');
            }
            if (text) {
                text.textContent = 'Yêu thích';
            }

            // Update button styling for unfavorited state
            button.classList.remove('bg-red-500', 'hover:bg-red-600', 'text-white', 'border-red-500', 'hover:border-red-600');
            button.classList.add('bg-theme-secondary', 'hover:bg-theme-tertiary', 'text-theme-primary', 'border-transparent', 'hover:border-theme-accent');
            button.title = 'Thêm vào yêu thích';
        }
    },

    /**
     * Update watchlist button UI based on watchlist status
     * @param {HTMLElement} button - Button element to update
     * @param {boolean} isWatchlist - Whether movie is in watchlist
     */
    updateWatchlistButton: function (button, isWatchlist) {
        if (!button) return;

        const icon = button.querySelector('i');
        const text = button.querySelector('.watchlist-text');

        // Update data attribute
        button.setAttribute('data-is-watchlist', isWatchlist ? 'true' : 'false');

        if (isWatchlist) {
            // Movie is in watchlist - show blue styling
            if (text) {
                text.textContent = 'Đã thêm vào danh sách';
            }

            // Update button styling for watchlist state
            if (button.classList.contains('hero-watchlist-btn')) {
                // Hero button styling
                button.classList.remove('border-white/20', 'text-white', 'hover:border-white/40', 'hover:bg-white/10');
                button.classList.add('bg-blue-500', 'border-blue-500', 'text-white', 'hover:bg-blue-600', 'hover:border-blue-600');
            } else if (button.classList.contains('slider-watchlist-btn')) {
                // Slider button styling
                button.classList.remove('bg-white/20', 'text-white', 'hover:bg-white/30');
                button.classList.add('bg-blue-500', 'text-white', 'hover:bg-blue-600');
            } else if (button.classList.contains('card-watchlist-btn')) {
                // Card button styling
                button.classList.remove('bg-black', 'bg-opacity-70', 'hover:bg-opacity-90');
                button.classList.add('bg-blue-500', 'hover:bg-blue-600');
            }
            button.title = 'Xóa khỏi danh sách xem';
        } else {
            // Movie is not in watchlist - show default styling
            if (text) {
                text.textContent = 'Thêm vào danh sách';
            }

            // Update button styling for non-watchlist state
            if (button.classList.contains('hero-watchlist-btn')) {
                // Hero button styling
                button.classList.remove('bg-blue-500', 'border-blue-500', 'text-white', 'hover:bg-blue-600', 'hover:border-blue-600');
                button.classList.add('border-white/20', 'text-white', 'hover:border-white/40', 'hover:bg-white/10');
            } else if (button.classList.contains('slider-watchlist-btn')) {
                // Slider button styling
                button.classList.remove('bg-blue-500', 'text-white', 'hover:bg-blue-600');
                button.classList.add('bg-white/20', 'text-white', 'hover:bg-white/30');
            } else if (button.classList.contains('card-watchlist-btn')) {
                // Card button styling
                button.classList.remove('bg-blue-500', 'hover:bg-blue-600');
                button.classList.add('bg-black', 'bg-opacity-70', 'hover:bg-opacity-90');
            }
            button.title = 'Thêm vào danh sách xem';
        }

        // Remove loading state
        button.classList.remove('loading');
        button.disabled = false;
    },

    /**
     * Report movie function
     * @param {string} movieSlug - Movie slug
     */
    showReportModal: function (movieSlug) {
        // Check if report modal exists
        const reportModal = document.querySelector('[data-report-modal]');
        if (reportModal && window.ReportModal) {
            window.ReportModal.show(movieSlug);
        } else {
            console.log('Report modal not available');
        }
    }
};

// Create global aliases for backward compatibility
window.toggleFavorite = function (movieId) {
    // Try to get the button element from the current event
    let buttonElement = null;
    if (typeof event !== 'undefined' && event.target) {
        buttonElement = event.target.closest('button');
    }
    window.MovieFunctions.toggleFavorite(movieId, buttonElement);
};

window.showTrailer = function (trailerUrl) {
    // Get trailer URL from global movieData if not provided
    if (!trailerUrl && window.movieData && window.movieData.trailer) {
        window.MovieFunctions.showTrailer(window.movieData.trailer);
    } else {
        window.MovieFunctions.showTrailer(trailerUrl);
    }
};

window.hideTrailer = function () {
    window.MovieFunctions.hideTrailer();
};

window.shareToFacebook = function (url, title) {
    window.MovieFunctions.shareToFacebook(url, title);
};

window.shareToTwitter = function (url, title) {
    window.MovieFunctions.shareToTwitter(url, title);
};

window.shareToTelegram = function (url, title) {
    window.MovieFunctions.shareToTelegram(url, title);
};

window.copyLink = function (url) {
    window.MovieFunctions.copyLink(url);
};

window.showReportModal = function (movieSlug) {
    window.MovieFunctions.showReportModal(movieSlug);
};

window.toggleWatchlist = function (movieId) {
    window.MovieFunctions.toggleWatchlist(movieId);
};

// Function to show login required message for unauthenticated users
window.showLoginRequired = function () {
    if (window.showNotification) {
        window.showNotification('warning', 'Bạn cần đăng nhập để sử dụng tính năng này');
    } else {
        alert('Bạn cần đăng nhập để sử dụng tính năng này');
    }

    // Optionally redirect to login page after a delay
    setTimeout(() => {
        window.location.href = '/login';
    }, 2000);
};

window.shareMovie = function (movieName, movieUrl) {
    window.MovieFunctions.shareMovie(movieName, movieUrl);
};

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', function () {
    console.log('VieOn 2025 Common Functions loaded');

    // Close trailer modal when clicking outside
    document.addEventListener('click', function (e) {
        const trailerModal = document.getElementById('trailer-modal');
        if (trailerModal && e.target === trailerModal) {
            window.MovieFunctions.hideTrailer();
        }
    });

    // Close trailer modal with Escape key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            window.MovieFunctions.hideTrailer();
        }
    });
});
