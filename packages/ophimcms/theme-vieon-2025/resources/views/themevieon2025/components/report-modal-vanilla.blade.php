{{-- Report Modal Component - Vanilla JavaScript Version --}}
@php
    $movie = $movie ?? null;
    $episode = $episode ?? null;

    if (!$movie || !$episode) {
        return;
    }
@endphp

    <!-- Report Modal -->
<div id="reportModal" class="fixed inset-0 z-50 overflow-y-auto hidden opacity-0 transition-opacity duration-300">
    <!-- Background Overlay -->
    <div class="fixed inset-0 bg-black bg-opacity-75 backdrop-blur-sm transition-opacity"
         onclick="EpisodeReporting.closeModal()"></div>

    <!-- Modal Container -->
    <div class="flex min-h-full items-center justify-center p-4">
        <div
            class="relative w-full max-w-md transform overflow-hidden rounded-lg bg-theme-secondary shadow-xl transition-all scale-95"
            id="reportModalContent">

            <!-- Modal Header -->
            <div class="flex items-center justify-between p-4 border-b border-theme-primary">
                <h3 class="text-lg font-semibold text-theme-primary">
                    <i class="fas fa-flag mr-2 text-red-500"></i>
                    Báo cáo tập phim
                </h3>
                <button onclick="EpisodeReporting.closeModal()"
                        class="text-theme-tertiary hover:text-theme-primary transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <form id="reportForm"
                  class="ajax-form"
                  action="{{ route('episodes.report', [$movie->slug, $episode->slug]) }}"
                  method="POST">
                @csrf

                <div class="p-4 space-y-4">
                    <!-- Episode Info -->
                    <div class="bg-theme-dropdown rounded-lg p-3">
                        <div class="flex items-center gap-3">
                            <img src="{{ $movie->getThumbUrl() }}"
                                 alt="{{ $movie->name }}"
                                 class="w-12 h-16 object-cover rounded">
                            <div class="flex-1 min-w-0">
                                <h4 class="font-medium text-theme-primary truncate">{{ $movie->name }}</h4>
                                <p class="text-sm text-theme-tertiary">{{ $episode->name }}</p>
                                <p class="text-xs text-theme-quaternary">{{ $movie->origin_name }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Report Reason -->
                    <div>
                        <label for="reason" class="block text-sm font-medium text-theme-primary mb-2">
                            <i class="fas fa-exclamation-triangle mr-1 text-yellow-500"></i>
                            Lý do báo cáo
                        </label>
                        <select name="reason"
                                id="reason"
                                class="w-full px-3 py-2 bg-theme-dropdown border border-theme-primary rounded-lg text-theme-primary focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent">
                            <option value="">Chọn lý do...</option>
                            <option value="Link không hoạt động">Link không hoạt động</option>
                            <option value="Chất lượng kém">Chất lượng kém</option>
                            <option value="Không có phụ đề">Không có phụ đề</option>
                            <option value="Phụ đề sai">Phụ đề sai</option>
                            <option value="Âm thanh có vấn đề">Âm thanh có vấn đề</option>
                            <option value="Video bị lag">Video bị lag</option>
                            <option value="Quảng cáo quá nhiều">Quảng cáo quá nhiều</option>
                            <option value="Nội dung không phù hợp">Nội dung không phù hợp</option>
                            <option value="Khác">Khác</option>
                        </select>
                    </div>

                    <!-- Report Message -->
                    <div>
                        <label for="message" class="block text-sm font-medium text-theme-primary mb-2">
                            <i class="fas fa-comment mr-1 text-blue-500"></i>
                            Mô tả chi tiết <span class="text-red-500">*</span>
                        </label>
                        <textarea name="message"
                                  id="message"
                                  rows="4"
                                  required
                                  maxlength="500"
                                  placeholder="Vui lòng mô tả chi tiết vấn đề bạn gặp phải..."
                                  class="w-full px-3 py-2 bg-theme-dropdown border border-theme-primary rounded-lg text-theme-primary placeholder-theme-quaternary focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent resize-none"></textarea>
                        <div class="flex justify-between items-center mt-1">
                            <p class="text-xs text-theme-quaternary">Tối đa 500 ký tự</p>
                            <span id="messageCounter" class="text-xs text-theme-quaternary">0/500</span>
                        </div>
                    </div>

                    <!-- Contact Info (Optional) -->
                    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                        <div class="flex items-start gap-2">
                            <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                            <div class="text-sm text-blue-700 dark:text-blue-300">
                                <p class="font-medium mb-1">Thông tin hữu ích:</p>
                                <ul class="text-xs space-y-1 text-blue-600 dark:text-blue-400">
                                    <li>• Báo cáo của bạn sẽ được xem xét trong 24h</li>
                                    <li>• Cung cấp thông tin chi tiết giúp chúng tôi xử lý nhanh hơn</li>
                                    <li>• Bạn có thể báo cáo nhiều vấn đề khác nhau</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="flex items-center justify-end gap-3 p-4 border-t border-theme-primary bg-theme-dropdown">
                    <button type="button"
                            onclick="EpisodeReporting.closeModal()"
                            class="px-4 py-2 text-sm font-medium text-theme-tertiary hover:text-theme-primary transition-colors">
                        Hủy bỏ
                    </button>
                    <button type="submit"
                            id="submitReportBtn"
                            class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                        <span class="submit-text">
                            <i class="fas fa-paper-plane mr-1"></i>
                            Gửi báo cáo
                        </span>
                        <span class="loading-text hidden">
                            <i class="fas fa-spinner fa-spin mr-1"></i>
                            Đang gửi...
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Success/Error Messages Container -->
<div id="reportNotification" class="fixed top-4 right-4 z-60 hidden">
    <div
        class="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4 max-w-sm">
        <div class="flex items-start gap-3">
            <div id="notificationIcon" class="flex-shrink-0 mt-0.5">
                <!-- Icon will be inserted by JavaScript -->
            </div>
            <div class="flex-1 min-w-0">
                <p id="notificationTitle" class="text-sm font-medium text-gray-900 dark:text-white">
                    <!-- Title will be inserted by JavaScript -->
                </p>
                <p id="notificationMessage" class="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    <!-- Message will be inserted by JavaScript -->
                </p>
            </div>
            <button onclick="EpisodeReporting.hideNotification()"
                    class="flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

<script>
    // Global function for backward compatibility
    function showReportModal() {
        EpisodeReporting.openModal();
    }

    function closeReportModal() {
        EpisodeReporting.closeModal();
    }
</script>
