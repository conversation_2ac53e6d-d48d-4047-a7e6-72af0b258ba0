@extends('themevieon2025::layouts.app')

@section('title', 'Phim ' . $catalog->name . ' - Trang ' . ($movies->currentPage() ?? 1))
@section('description', 'Xem phim ' . $catalog->name . ' hay nhất, mới nhất. Danh sách phim ' . $catalog->name . ' chất lư<PERSON>ng HD miễn phí.')

@section('content')
    <div class="min-h-screen bg-theme-primary">
        {{-- Breadcrumb --}}
        @include('themevieon2025::components.breadcrumb', [
            'items' => [
                [
                    'label' => 'Danh sách',
                    'url' => '#',
                    'icon' => 'fas fa-list'
                ],
                [
                    'label' => $catalog->name,
                    'icon' => 'fas fa-folder'
                ]
            ]
        ])

        <!-- Page Header -->
        <div class="bg-theme-secondary border-b border-theme-primary">
            <div class="container mx-auto px-4 py-6">

                <!-- Type Header -->
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-theme-primary mb-2">
                            <i class="fas fa-list mr-2 text-main"></i>
                            {{ $catalog->name }}
                        </h1>
                        <p class="text-theme-tertiary">
                            Tìm thấy {{ $movies->total() }} phim {{ $catalog->name }}
                        </p>
                    </div>

                    <!-- Type Stats -->
                    <div class="hidden md:block text-right">
                        <div class="bg-theme-dropdown rounded-lg p-3">
                            <div class="text-2xl font-bold text-main">{{ number_format($movies->total()) }}</div>
                            <div class="text-sm text-theme-tertiary">Tổng phim</div>
                        </div>
                    </div>
                </div>

                <!-- Type Description -->
                @if($catalog->description)
                    <div class="mt-4 p-4 bg-theme-dropdown rounded-lg">
                        <p class="text-theme-secondary text-sm leading-relaxed">
                            {{ $catalog->description }}
                        </p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Filter & Sort Section -->
        <div class="bg-theme-secondary border-b border-theme-primary">
            <div class="container mx-auto px-4 py-4">
                <div class="flex flex-wrap items-center justify-between gap-4">
                    <!-- View Toggle -->
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-theme-tertiary">Hiển thị:</span>
                        <div class="flex bg-theme-dropdown rounded-lg p-1">
                            <a href="{{ request()->fullUrlWithQuery(['view' => 'grid']) }}"
                               class="px-3 py-1 text-sm rounded transition-all {{ request('view', 'grid') === 'grid' ? 'bg-main text-white' : 'text-theme-secondary hover:text-main' }}">
                                <i class="fas fa-th"></i> Lưới
                            </a>
                            <a href="{{ request()->fullUrlWithQuery(['view' => 'list']) }}"
                               class="px-3 py-1 text-sm rounded transition-all {{ request('view') === 'list' ? 'bg-main text-white' : 'text-theme-secondary hover:text-main' }}">
                                <i class="fas fa-list"></i> Danh sách
                            </a>
                        </div>
                    </div>

                    <!-- Sort Options -->
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-theme-tertiary">Sắp xếp:</span>
                        <select
                            class="bg-theme-dropdown border border-theme-primary rounded-lg px-3 py-1 text-sm text-theme-secondary focus:outline-none focus:border-main"
                            onchange="window.location.href = this.value">
                            <option
                                value="{{ request()->fullUrlWithQuery(['sort' => 'created_at', 'order' => 'desc']) }}"
                                {{ request('sort') === 'created_at' && request('order', 'desc') === 'desc' ? 'selected' : '' }}>
                                Mới nhất
                            </option>
                            <option
                                value="{{ request()->fullUrlWithQuery(['sort' => 'view_total', 'order' => 'desc']) }}"
                                {{ request('sort') === 'view_total' && request('order') === 'desc' ? 'selected' : '' }}>
                                Xem nhiều nhất
                            </option>
                            <option value="{{ request()->fullUrlWithQuery(['sort' => 'name', 'order' => 'asc']) }}"
                                {{ request('sort') === 'name' && request('order') === 'asc' ? 'selected' : '' }}>
                                Tên A-Z
                            </option>
                            <option value="{{ request()->fullUrlWithQuery(['sort' => 'name', 'order' => 'desc']) }}"
                                {{ request('sort') === 'name' && request('order') === 'desc' ? 'selected' : '' }}>
                                Tên Z-A
                            </option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Movies Content -->
        <div class="container mx-auto px-4 py-6">
            <!-- Movies Grid/List -->
            @if($movies->count() > 0)
                <div id="movies-container"
                     class="{{ request('view', 'grid') === 'list' ? 'space-y-4' : 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4' }} mb-6">
                    @foreach($movies as $movie)
                        @include('themevieon2025::components.movie-card', [
                            'movie' => $movie,
                            'layout' => request('view', 'grid'),
                            'showRating' => true,
                            'showActions' => true
                        ])
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="flex justify-center">
                    {{ $movies->appends(request()->query())->links('themevieon2025::components.pagination') }}
                </div>
            @else
                <!-- Empty State -->
                <div class="text-center py-12">
                    <div class="bg-theme-secondary rounded-lg p-8 max-w-md mx-auto">
                        <i class="fas fa-film text-4xl text-theme-tertiary mb-4"></i>
                        <h3 class="text-xl font-semibold text-theme-primary mb-2">Không tìm thấy phim</h3>
                        <p class="text-theme-tertiary mb-4">Hiện tại chưa có phim nào trong danh
                            mục {{ $catalog->name }}.</p>
                        <a href="{{ route('home') }}"
                           class="inline-flex items-center px-4 py-2 bg-main text-white rounded-lg hover:bg-main/90 transition-colors">
                            <i class="fas fa-home mr-2"></i>
                            Về trang chủ
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>

    @push('scripts')
        <script>
            // AJAX pagination
            document.addEventListener('DOMContentLoaded', function () {
                // Handle pagination clicks
                document.addEventListener('click', function (e) {
                    if (e.target.matches('.pagination a') || e.target.closest('.pagination a')) {
                        e.preventDefault();
                        const link = e.target.matches('a') ? e.target : e.target.closest('a');
                        loadPage(link.href);
                    }
                });

                function loadPage(url) {
                    // Show loading state
                    const container = document.getElementById('movies-container');
                    container.style.opacity = '0.5';

                    fetch(url, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'text/html'
                        }
                    })
                        .then(response => response.text())
                        .then(html => {
                            // Parse the response and update content
                            const parser = new DOMParser();
                            const doc = parser.parseFromString(html, 'text/html');

                            // Update movies container
                            const newContainer = doc.getElementById('movies-container');
                            if (newContainer) {
                                container.innerHTML = newContainer.innerHTML;
                            }

                            // Update pagination
                            const pagination = document.querySelector('.pagination');
                            const newPagination = doc.querySelector('.pagination');
                            if (pagination && newPagination) {
                                pagination.innerHTML = newPagination.innerHTML;
                            }

                            // Update URL
                            window.history.pushState({}, '', url);

                            // Restore opacity
                            container.style.opacity = '1';

                            // Scroll to top of movies section
                            container.scrollIntoView({behavior: 'smooth', block: 'start'});
                        })
                        .catch(error => {
                            console.error('Error loading page:', error);
                            container.style.opacity = '1';
                        });
                }
            });
        </script>
    @endpush
@endsection
