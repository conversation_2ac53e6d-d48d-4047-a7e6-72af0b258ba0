# Context
Filename: theme-vieon-2025-favorite-watchlist-fix.md
Created On: 2025-01-29
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
Fix the favorite and watchlist toggle functionality in theme-vieon-2025 project. The issue is that the `onclick="toggleFavorite"` and `toggleWatchlist` functions in various view files are not correctly loading/displaying the current state of whether a user has already marked an item as favorite or added it to their watchlist.

**Expected behavior**: When a page loads, the toggle buttons should show the correct initial state (active/inactive, checked/unchecked, or appropriate visual indicator) based on whether the current user has previously favorited the item or added it to their watchlist.

**Current issue**: The buttons appear to show a default state regardless of the actual user's previous actions, meaning users cannot see which items they have already favorited or added to their watchlist.

# Project Overview
This is a Laravel-based movie streaming theme project using vanilla JavaScript with traditional script tags (no ES6 imports). The project follows the existing ajax-form.js pattern for AJAX implementations and has reference theme implementations (theme-iqiyi, theme-pno, theme-vvmotchill) for understanding proper patterns.

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## Current Implementation Status

### Favorites Functionality
**✅ Working Components:**
- `Favorite` model exists with proper relationships
- Database table `favorites` with user_id, movie_id columns
- API endpoints for favorites management:
  - `POST /api/user/favorites` - Add to favorites
  - `DELETE /api/user/favorites/{movieId}` - Remove from favorites
  - `POST /api/user/favorites/{movieId}/toggle` - Toggle favorite status
  - `GET /api/user/favorites/{movieId}/check` - Check favorite status
- `MovieController::show()` method correctly determines `$isFavorite` state
- `movie.blade.php` correctly receives and displays initial state
- JavaScript `toggleFavorite()` function works correctly
- `updateFavoriteButton()` function properly updates UI state

### Watchlist Functionality
**❌ Problematic Components:**
- **No dedicated Watchlist model** - Currently using Favorites table as placeholder
- API endpoints exist but are not implemented:
  - `apiAddToWatchlist()` and `apiRemoveFromWatchlist()` contain placeholder comments
  - `apiWatchlist()` incorrectly returns favorites data instead of watchlist
- **No watchlist state determination** in controllers
- **No initial state passed** to movie-card components

## Key Issues Identified

### 1. Movie Detail Page (movie.blade.php)
- ✅ Favorites: Working correctly - `$isFavorite` is determined and passed
- ❌ Watchlist: No watchlist state determination or UI

### 2. Movie Card Component (movie-card.blade.php)
- ❌ Favorites: No initial state determination - always shows default state
- ❌ Watchlist: No initial state determination - always shows default state
- Missing data attributes for current state
- No controller support for bulk state checking

### 3. Missing Watchlist Infrastructure
- No `Watchlist` model
- No `watchlists` database table
- API methods are placeholder implementations
- No relationship defined in User model for watchlist

## File Locations
- **Controllers**: `packages/ophimcms/theme-vieon-2025/src/Controllers/`
- **Models**: `packages/ophimcms/theme-vieon-2025/src/Models/`
- **Views**: `packages/ophimcms/theme-vieon-2025/resources/views/themevieon2025/`
- **JavaScript**: `packages/ophimcms/theme-vieon-2025/resources/assets/js/`
- **Routes**: `packages/ophimcms/theme-vieon-2025/routes/`
- **Migrations**: `packages/ophimcms/theme-vieon-2025/database/migrations/`

# Proposed Solution (Populated by INNOVATE mode)

## Approach 1: Complete Watchlist Implementation + Movie Card State Management

### Watchlist Infrastructure
1. **Create dedicated Watchlist model and migration**
   - Similar structure to Favorites (user_id, movie_id, timestamps)
   - Proper relationships with User and Movie models

2. **Implement watchlist API methods**
   - Replace placeholder implementations with actual database operations
   - Add toggle functionality similar to favorites

### State Management for Movie Cards
3. **Bulk state checking for movie listings**
   - Add methods to check favorite/watchlist status for multiple movies
   - Modify controllers that render movie cards to include state data
   - Pass state information to movie-card component

4. **Update movie-card component**
   - Add data attributes for initial state
   - Update button styling based on initial state
   - Ensure JavaScript functions can update state correctly

### Benefits:
- Complete, proper implementation
- Consistent with existing favorites pattern
- Scalable for future features
- Clear separation of concerns

### Drawbacks:
- More extensive changes required
- Need to create new database table
- More testing required

## Approach 2: Unified Favorites/Watchlist System

### Alternative Implementation
1. **Extend Favorites table with type column**
   - Add `type` enum column ('favorite', 'watchlist')
   - Modify existing model to handle both types
   - Update API methods to work with type parameter

### Benefits:
- Minimal database changes
- Reuse existing infrastructure
- Simpler implementation

### Drawbacks:
- Less clear separation of concepts
- May complicate future features
- Mixing different user actions in one table

## Recommended Solution: Approach 1
The complete implementation provides better long-term maintainability and follows the existing pattern established by the favorites system.

# Implementation Plan (Generated by PLAN mode)

## Phase 1: Watchlist Infrastructure Setup

### 1.1 Create Watchlist Model and Migration
- Create `Watchlist` model similar to `Favorite` model
- Create migration for `watchlists` table with user_id, movie_id, timestamps
- Add unique constraint for user_id + movie_id combination
- Add foreign key constraints

### 1.2 Update User Model Relationships
- Add `watchlist()` relationship method to User model
- Add `watchlistMovies()` many-to-many relationship method

### 1.3 Implement Watchlist API Methods
- Replace placeholder implementations in `ApiUserController`
- Implement `apiAddToWatchlist()`, `apiRemoveFromWatchlist()`, `apiToggleWatchlist()`
- Add `apiCheckWatchlist()` method for state checking
- Update `apiWatchlist()` to use actual watchlist table

## Phase 2: Bulk State Checking System

### 2.1 Add Bulk State Checking Methods
- Add `checkFavoritesForMovies()` method to check multiple movies at once
- Add `checkWatchlistForMovies()` method for watchlist state checking
- Create helper methods in controllers for efficient state determination

### 2.2 Update Controllers for State Management
- Modify `HomeController` to include state data for movie listings
- Update `CategoryController`, `RegionController`, etc. for movie grids
- Add state checking to search results and other movie listing pages

## Phase 3: Frontend State Management

### 3.1 Update Movie Card Component
- Add data attributes for initial favorite/watchlist state
- Update button styling based on initial state
- Ensure proper visual indicators for both states

### 3.2 Enhance JavaScript Functions
- Add `updateWatchlistButton()` function similar to `updateFavoriteButton()`
- Update `toggleWatchlist()` to properly handle UI state changes
- Add proper error handling and loading states

### 3.3 Update Movie Detail Page
- Add watchlist state determination to `MovieController::show()`
- Add watchlist button to movie detail page
- Ensure proper initial state display

## Phase 4: Routes and API Integration

### 4.1 Add Missing API Routes
- Add `apiToggleWatchlist()` route
- Add `apiCheckWatchlist()` route
- Update route definitions in `api.php`

### 4.2 Update Frontend Route Definitions
- Add watchlist routes to `layouts/app.blade.php` route definitions
- Ensure JavaScript can access all necessary API endpoints

## Phase 5: Testing and Validation

### 5.1 Database Migration Testing
- Ensure migration runs without conflicts
- Verify foreign key constraints work properly
- Test unique constraint enforcement

### 5.2 API Functionality Testing
- Test all watchlist API endpoints
- Verify bulk state checking performance
- Test error handling scenarios

### 5.3 Frontend Integration Testing
- Test initial state display on all movie listing pages
- Verify toggle functionality works correctly
- Test state persistence across page navigation

```
Implementation Checklist:
1. Create Watchlist model with proper fillable fields and relationships
2. Create watchlists table migration with user_id, movie_id, timestamps, and constraints
3. Add watchlist relationships to User model (watchlist() and watchlistMovies())
4. Implement apiAddToWatchlist() method in ApiUserController
5. Implement apiRemoveFromWatchlist() method in ApiUserController
6. Implement apiToggleWatchlist() method in ApiUserController
7. Implement apiCheckWatchlist() method in ApiUserController
8. Update apiWatchlist() method to use actual watchlist table instead of favorites
9. Add checkFavoritesForMovies() bulk checking method to ApiUserController
10. Add checkWatchlistForMovies() bulk checking method to ApiUserController
11. Update HomeController index() method to include favorite/watchlist state for movies
12. Update CategoryController to include state data for movie listings
13. Update RegionController to include state data for movie listings
14. Update SearchController to include state data for search results
15. Update MovieController show() method to include watchlist state determination
16. Add data attributes for initial state to movie-card.blade.php component
17. Update movie-card.blade.php button styling based on initial state
18. Add updateWatchlistButton() function to common.js
19. Update toggleWatchlist() function to properly handle UI state changes
20. Add watchlist button and state to movie.blade.php detail page
21. Add apiToggleWatchlist route to api.php
22. Add apiCheckWatchlist route to api.php
23. Update route definitions in layouts/app.blade.php for watchlist endpoints
24. Add toggleWatchlist API method to user-api.js
25. Test database migration and constraints
26. Test all API endpoints functionality
27. Test frontend state display and toggle functionality
```

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "Step 9 - Add bulk checking methods"

# Task Progress (Appended by EXECUTE mode after each step completion)
*   2025-01-29 - Steps 1-8 Completed
    *   Step: Created Watchlist model and infrastructure
    *   Modifications:
        - Created `packages/ophimcms/theme-vieon-2025/src/Models/Watchlist.php`
        - Created `packages/ophimcms/theme-vieon-2025/database/migrations/2024_05_01_000011_create_watchlists_table.php`
        - Updated `app/Models/User.php` to add watchlist() and watchlistMovies() relationships
        - Updated `packages/ophimcms/theme-vieon-2025/src/Controllers/User/API/ApiUserController.php`:
          - Added Watchlist model import
          - Implemented apiAddToWatchlist() method with proper validation and duplicate checking
          - Implemented apiRemoveFromWatchlist() method with error handling
          - Implemented apiToggleWatchlist() method for toggle functionality
          - Implemented apiCheckWatchlist() method for state checking
          - Updated apiWatchlist() method to use actual watchlist table instead of favorites
    *   Change Summary: Complete watchlist infrastructure setup with model, migration, relationships, and API methods
    *   Reason: Executing plan steps 1-8 for watchlist infrastructure
    *   Blockers: None
    *   Status: Pending Confirmation
