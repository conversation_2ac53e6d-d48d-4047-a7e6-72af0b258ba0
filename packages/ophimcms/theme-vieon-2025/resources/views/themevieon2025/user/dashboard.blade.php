@extends('themevieon2025::layouts.app')

@section('title', 'Dashboard - ' . setting('site_meta_title', 'VieOn 2025'))
@section('description', 'Trang quản lý cá nhân - <PERSON><PERSON> l<PERSON> sử, danh sách y<PERSON><PERSON> th<PERSON>, đ<PERSON><PERSON> giá và quản lý tài khoản của bạn.')

@section('content')
    <div class="container mx-auto px-4 py-8">
        {{-- Page Header --}}
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-white mb-2">Dashboard</h1>
            <p class="text-gray-400">Chào mừng trở lại, {{ auth()->user()->name }}!</p>
        </div>

        {{-- Loading Overlay --}}
        <div id="dashboard-loading"
             class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
            <div class="bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-main"></div>
                <span class="text-white">Đang tải...</span>
            </div>
        </div>

        {{-- Error Message Container --}}
        <div id="dashboard-error"
             class="hidden mb-6 bg-red-500 bg-opacity-20 border border-red-500 text-red-200 px-4 py-3 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                          clip-rule="evenodd"></path>
                </svg>
                <span id="dashboard-error-message"></span>
            </div>
        </div>

        {{-- Success Message Container --}}
        <div id="dashboard-success"
             class="hidden mb-6 bg-green-500 bg-opacity-20 border border-green-500 text-green-200 px-4 py-3 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clip-rule="evenodd"></path>
                </svg>
                <span id="dashboard-success-message"></span>
            </div>
        </div>

        {{-- Dashboard Stats --}}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm">Yêu thích</p>
                        <p id="favorites-count" class="text-2xl font-bold text-white">--</p>
                    </div>
                    <div class="bg-red-500 bg-opacity-20 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                  d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                                  clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm">Danh sách xem</p>
                        <p id="watchlist-count" class="text-2xl font-bold text-white">--</p>
                    </div>
                    <div class="bg-blue-500 bg-opacity-20 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm">Lịch sử xem</p>
                        <p id="history-count" class="text-2xl font-bold text-white">--</p>
                    </div>
                    <div class="bg-green-500 bg-opacity-20 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                                  clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm">Đánh giá</p>
                        <p id="ratings-count" class="text-2xl font-bold text-white">--</p>
                    </div>
                    <div class="bg-yellow-500 bg-opacity-20 p-3 rounded-lg">
                        <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        {{-- Quick Actions --}}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {{-- Recent Favorites --}}
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Yêu thích gần đây</h2>
                    <a href="{{ route('user.favorites') }}" class="text-main hover:text-main-light text-sm">Xem tất
                        cả</a>
                </div>
                <div id="recent-favorites" class="space-y-3">
                    <div class="flex items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-main"></div>
                    </div>
                </div>
            </div>

            {{-- Recent History --}}
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Xem gần đây</h2>
                    <a href="{{ route('user.history') }}" class="text-main hover:text-main-light text-sm">Xem tất cả</a>
                </div>
                <div id="recent-history" class="space-y-3">
                    <div class="flex items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-main"></div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Recommendations --}}
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-white">Đề xuất cho bạn</h2>
                <a href="{{ route('user.recommendations') }}" class="text-main hover:text-main-light text-sm">Xem tất
                    cả</a>
            </div>
            <div id="recommendations" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <div class="col-span-full flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-main"></div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            loadDashboardData();
        });

        async function loadDashboardData() {
            const loadingEl = document.getElementById('dashboard-loading');
            const errorEl = document.getElementById('dashboard-error');

            try {
                showLoading(true);
                hideMessage();

                // Load dashboard data
                const dashboardData = await window.userAPI.getDashboard();

                if (dashboardData.status) {
                    updateDashboardStats(dashboardData.data);
                    loadRecentFavorites();
                    loadRecentHistory();
                    loadRecommendations();
                }

            } catch (error) {
                console.error('Error loading dashboard:', error);
                showError('Không thể tải dữ liệu dashboard');
            } finally {
                showLoading(false);
            }
        }

        function updateDashboardStats(data) {
            // Update counters (placeholder - implement based on your data structure)
            document.getElementById('favorites-count').textContent = data.favoriteMovies?.length || 0;
            document.getElementById('history-count').textContent = data.recentMovies?.length || 0;
            document.getElementById('watchlist-count').textContent = '0'; // Implement based on your data
            document.getElementById('ratings-count').textContent = '0'; // Implement based on your data
        }

        async function loadRecentFavorites() {
            try {
                const favoritesData = await window.userAPI.getFavorites();
                const container = document.getElementById('recent-favorites');

                if (favoritesData.status && favoritesData.data.length > 0) {
                    container.innerHTML = favoritesData.data.slice(0, 3).map(movie => `
                <div class="flex items-center space-x-3 p-3 bg-gray-700 rounded-lg">
                    <img src="${movie.thumb_url || '/placeholder.jpg'}" alt="${movie.name}" class="w-12 h-16 object-cover rounded">
                    <div class="flex-1">
                        <h3 class="text-white font-medium text-sm">${movie.name}</h3>
                        <p class="text-gray-400 text-xs">${movie.origin_name || ''}</p>
                    </div>
                </div>
            `).join('');
                } else {
                    container.innerHTML = '<p class="text-gray-400 text-center py-4">Chưa có phim yêu thích</p>';
                }
            } catch (error) {
                document.getElementById('recent-favorites').innerHTML = '<p class="text-red-400 text-center py-4">Lỗi tải dữ liệu</p>';
            }
        }

        async function loadRecentHistory() {
            try {
                const historyData = await window.userAPI.getHistory();
                const container = document.getElementById('recent-history');

                if (historyData.status && historyData.data.length > 0) {
                    container.innerHTML = historyData.data.slice(0, 3).map(movie => `
                <div class="flex items-center space-x-3 p-3 bg-gray-700 rounded-lg">
                    <img src="${movie.thumb_url || '/placeholder.jpg'}" alt="${movie.name}" class="w-12 h-16 object-cover rounded">
                    <div class="flex-1">
                        <h3 class="text-white font-medium text-sm">${movie.name}</h3>
                        <p class="text-gray-400 text-xs">${movie.origin_name || ''}</p>
                    </div>
                </div>
            `).join('');
                } else {
                    container.innerHTML = '<p class="text-gray-400 text-center py-4">Chưa có lịch sử xem</p>';
                }
            } catch (error) {
                document.getElementById('recent-history').innerHTML = '<p class="text-red-400 text-center py-4">Lỗi tải dữ liệu</p>';
            }
        }

        async function loadRecommendations() {
            try {
                const recommendationsData = await window.userAPI.getRecommendations();
                const container = document.getElementById('recommendations');

                if (recommendationsData.status && recommendationsData.data.length > 0) {
                    container.innerHTML = recommendationsData.data.slice(0, 6).map(movie => `
                <div class="group cursor-pointer">
                    <div class="relative overflow-hidden rounded-lg">
                        <img src="${movie.thumb_url || '/placeholder.jpg'}" alt="${movie.name}"
                             class="w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300"></div>
                    </div>
                    <h3 class="text-white text-sm font-medium mt-2 line-clamp-2">${movie.name}</h3>
                </div>
            `).join('');
                } else {
                    container.innerHTML = '<p class="text-gray-400 text-center py-4 col-span-full">Chưa có đề xuất</p>';
                }
            } catch (error) {
                document.getElementById('recommendations').innerHTML = '<p class="text-red-400 text-center py-4 col-span-full">Lỗi tải dữ liệu</p>';
            }
        }

        function showLoading(show) {
            const loadingEl = document.getElementById('dashboard-loading');
            if (show) {
                loadingEl.classList.remove('hidden');
            } else {
                loadingEl.classList.add('hidden');
            }
        }

        function showError(message) {
            const errorEl = document.getElementById('dashboard-error');
            const messageEl = document.getElementById('dashboard-error-message');
            messageEl.textContent = message;
            errorEl.classList.remove('hidden');
        }

        function showSuccess(message) {
            const successEl = document.getElementById('dashboard-success');
            const messageEl = document.getElementById('dashboard-success-message');
            messageEl.textContent = message;
            successEl.classList.remove('hidden');
        }

        function hideMessage() {
            document.getElementById('dashboard-error').classList.add('hidden');
            document.getElementById('dashboard-success').classList.add('hidden');
        }
    </script>
@endpush
