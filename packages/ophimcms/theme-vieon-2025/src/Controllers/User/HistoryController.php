<?php

namespace Ophim\ThemeVieon2025\Controllers\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Ophim\ThemeVieon2025\Models\History;

class HistoryController extends Controller
{
    public function index()
    {
        $histories = History::where('user_id', Auth::id())->with('movie')->latest()->paginate(12);
        return view('themevieon2025::user.histories.index', compact('histories'));
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'movie_id' => 'required|exists:movies,id',
            'episode_id' => 'nullable|exists:episodes,id',
            'progress' => 'nullable|integer',
        ]);
        $data['user_id'] = Auth::id();
        History::updateOrCreate(
            [
                'user_id' => $data['user_id'],
                'movie_id' => $data['movie_id'],
            ],
            [
                'episode_id' => $data['episode_id'] ?? null,
                'progress' => $data['progress'] ?? 0,
            ]
        );
        return response()->json(['status' => 'success']);
    }

    public function destroy($id)
    {
        $history = History::where('user_id', Auth::id())->where('id', $id)->firstOrFail();
        $history->delete();
        return back()->with('status', 'Đã xóa khỏi lịch sử!');
    }

    public function apiDestroy($id)
    {
        $history = History::where('user_id', Auth::id())->where('id', $id)->first();
        if (!$history) {
            return response()->json([
                'status' => false,
                'message' => 'Không tìm thấy lịch sử!'
            ], 404);
        }
        $history->delete();
        return response()->json([
            'status' => true,
            'message' => 'Đã xoá khỏi lịch sử!',
            'removeSelector' => '.history-item-' . $id
        ]);
    }
} 