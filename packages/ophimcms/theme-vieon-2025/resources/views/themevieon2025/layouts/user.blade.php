@extends('themevieon2025::layouts.app')

@section('content')
    <div class="min-h-screen bg-theme-background" x-data="{
        sidebarOpen: false,
        sidebarCompact: false,
        init() {
            // Load sidebar compact state from localStorage
            try {
                const savedState = localStorage.getItem('vieon_sidebar_compact');
                if (savedState !== null) {
                    this.sidebarCompact = savedState === 'true';
                    console.log('Loaded sidebar state from localStorage:', this.sidebarCompact);
                }
            } catch (e) {
                console.warn('Could not load sidebar state from localStorage:', e);
            }

            // Watch for changes and save to localStorage
            this.$watch('sidebarCompact', (value) => {
                try {
                    localStorage.setItem('vieon_sidebar_compact', value.toString());
                    console.log('Saved sidebar state to localStorage:', value);
                } catch (e) {
                    console.warn('Could not save sidebar state to localStorage:', e);
                }
            });
        }
    }">
        <div class="max-w-7xl mx-auto px-4 py-8">
            <!-- Mobile Menu Toggle -->
            <div class="lg:hidden mb-4">
                <button @click="sidebarOpen = !sidebarOpen"
                        class="flex items-center px-4 py-2 bg-theme-card rounded-lg text-white hover:bg-gray-700 transition-all">
                    <i class="fas fa-bars mr-2"></i>
                    Menu
                </button>
            </div>

            <div class="flex flex-col lg:flex-row gap-4 lg:gap-6">

                {{-- Sidebar Navigation --}}
                <div class="w-full lg:w-auto lg:flex-shrink-0" :class="{ 'lg:w-16': sidebarCompact, 'lg:w-56': !sidebarCompact }"
                     style="transition: width 0.3s ease-in-out;">
                    <!-- Mobile Overlay -->
                    <div x-show="sidebarOpen"
                         x-transition:enter="transition-opacity ease-linear duration-300"
                         x-transition:enter-start="opacity-0"
                         x-transition:enter-end="opacity-100"
                         x-transition:leave="transition-opacity ease-linear duration-300"
                         x-transition:leave-start="opacity-100"
                         x-transition:leave-end="opacity-0"
                         @click="sidebarOpen = false"
                         class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"></div>

                    <!-- Sidebar -->
                    <div
                        class="bg-theme-card rounded-2xl shadow-xl transition-all duration-300 lg:sticky lg:top-8"
                        :class="{
                         'translate-x-0': sidebarOpen,
                         '-translate-x-full': !sidebarOpen,
                         'lg:translate-x-0': true,
                         'lg:p-3': sidebarCompact,
                         'lg:p-6': !sidebarCompact,
                         'p-6': true,
                         'fixed top-4 left-4 right-4 z-50': !sidebarOpen,
                         'lg:relative lg:top-8 lg:left-auto lg:right-auto': true,
                         'w-full': true,
                         'sidebar-compact': sidebarCompact
                     }"
                        x-show="sidebarOpen"
                        x-show.lg="true"
                        x-transition:enter="transform transition ease-in-out duration-300"
                        x-transition:enter-start="-translate-x-full"
                        x-transition:enter-end="translate-x-0"
                        x-transition:leave="transform transition ease-in-out duration-300"
                        x-transition:leave-start="translate-x-0"
                        x-transition:leave-end="-translate-x-full"
                        style="transition: width 0.3s ease-in-out, padding 0.3s ease-in-out;">
                        <!-- Close Button (Mobile) -->
                        <div class="lg:hidden flex justify-between items-center mb-4 pb-4 border-b border-gray-700">
                            <h3 class="text-lg font-semibold text-white">Menu</h3>
                            <button @click="sidebarOpen = false" class="text-gray-400 hover:text-white">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>

                        {{-- User Info --}}
                        <div class="text-center mb-6 pb-4 border-b border-gray-700 lg:block"
                             x-show="!sidebarCompact"
                             x-transition:enter="transition-opacity duration-300"
                             x-transition:enter-start="opacity-0"
                             x-transition:enter-end="opacity-100"
                             x-transition:leave="transition-opacity duration-300"
                             x-transition:leave-start="opacity-100"
                             x-transition:leave-end="opacity-0">
                            <div
                                class="w-12 h-12 lg:w-16 lg:h-16 bg-main/20 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-user text-xl lg:text-2xl text-main"></i>
                            </div>
                            <h3 class="text-base lg:text-lg font-semibold text-white mb-1">{{ auth()->user()->name ?? 'User' }}</h3>
                            <p class="text-gray-400 text-xs lg:text-sm">{{ auth()->user()->email ?? '' }}</p>
                        </div>

                        {{-- Navigation Menu --}}
                        <nav class="space-y-1">
                            <!-- Profile -->
                            <a href="{{ route('user.profile.show') }}" :title="sidebarCompact ? 'Hồ sơ' : ''"
                               class="nav-item {{ request()->routeIs('user.profile.*') ? 'bg-main text-white' : 'text-gray-300 hover:bg-gray-700/50 hover:text-white' }}">
                                <i class="fas fa-user-circle text-lg"
                                   :class="{ 'lg:mr-0': sidebarCompact, 'mr-3': !sidebarCompact }"></i>
                                <span x-show="!sidebarCompact" x-show.lg="!sidebarCompact">Hồ sơ</span>
                            </a>

                            <!-- Favorites -->
                            <a href="{{ route('user.favorites') }}" :title="sidebarCompact ? 'Yêu thích' : ''"
                               class="nav-item {{ request()->routeIs('user.favorites') || request()->routeIs('favorites.*') ? 'bg-main text-white' : 'text-gray-300 hover:bg-gray-700/50 hover:text-white' }}">
                                <i class="fas fa-heart text-lg"
                                   :class="{ 'lg:mr-0': sidebarCompact, 'mr-3': !sidebarCompact }"></i>
                                <span x-show="!sidebarCompact" x-show.lg="!sidebarCompact">Yêu thích</span>
                            </a>

                            <!-- History -->
                            <a href="{{ route('user.history') }}" :title="sidebarCompact ? 'Lịch sử' : ''"
                               class="nav-item {{ request()->routeIs('user.history') || request()->routeIs('history.*') ? 'bg-main text-white' : 'text-gray-300 hover:bg-gray-700/50 hover:text-white' }}">
                                <i class="fas fa-history text-lg"
                                   :class="{ 'lg:mr-0': sidebarCompact, 'mr-3': !sidebarCompact }"></i>
                                <span x-show="!sidebarCompact" x-show.lg="!sidebarCompact">Lịch sử</span>
                            </a>

                            <!-- Watchlist -->
                            <a href="{{ route('user.watchlist') }}" :title="sidebarCompact ? 'Danh sách xem' : ''"
                               class="nav-item {{ request()->routeIs('user.watchlist') ? 'bg-main text-white' : 'text-gray-300 hover:bg-gray-700/50 hover:text-white' }}">
                                <i class="fas fa-bookmark text-lg"
                                   :class="{ 'lg:mr-0': sidebarCompact, 'mr-3': !sidebarCompact }"></i>
                                <span x-show="!sidebarCompact" x-show.lg="!sidebarCompact">Danh sách xem</span>
                            </a>

                            <!-- Comments -->
                            <a href="{{ route('user.comments.index') }}" :title="sidebarCompact ? 'Bình luận' : ''"
                               class="nav-item {{ request()->routeIs('user.comments.*') ? 'bg-main text-white' : 'text-gray-300 hover:bg-gray-700/50 hover:text-white' }}">
                                <i class="fas fa-comments text-lg"
                                   :class="{ 'lg:mr-0': sidebarCompact, 'mr-3': !sidebarCompact }"></i>
                                <span x-show="!sidebarCompact" x-show.lg="!sidebarCompact">Bình luận</span>
                            </a>

                            <!-- Ratings -->
                            <a href="{{ route('user.ratings.index') }}" :title="sidebarCompact ? 'Đánh giá' : ''"
                               class="nav-item {{ request()->routeIs('user.ratings.*') ? 'bg-main text-white' : 'text-gray-300 hover:bg-gray-700/50 hover:text-white' }}">
                                <i class="fas fa-star text-lg"
                                   :class="{ 'lg:mr-0': sidebarCompact, 'mr-3': !sidebarCompact }"></i>
                                <span x-show="!sidebarCompact" x-show.lg="!sidebarCompact">Đánh giá</span>
                            </a>

                            <!-- Settings -->
                            <a href="{{ route('user.settings.show') }}" :title="sidebarCompact ? 'Cài đặt' : ''"
                               class="nav-item {{ request()->routeIs('user.settings.*') ? 'bg-main text-white' : 'text-gray-300 hover:bg-gray-700/50 hover:text-white' }}">
                                <i class="fas fa-cog text-lg"
                                   :class="{ 'lg:mr-0': sidebarCompact, 'mr-3': !sidebarCompact }"></i>
                                <span x-show="!sidebarCompact" x-show.lg="!sidebarCompact">Cài đặt</span>
                            </a>

                            <!-- Password -->
                            <a href="{{ route('user.password.show') }}" :title="sidebarCompact ? 'Đổi mật khẩu' : ''"
                               class="nav-item {{ request()->routeIs('user.password.*') ? 'bg-main text-white' : 'text-gray-300 hover:bg-gray-700/50 hover:text-white' }}">
                                <i class="fas fa-key text-lg"
                                   :class="{ 'lg:mr-0': sidebarCompact, 'mr-3': !sidebarCompact }"></i>
                                <span x-show="!sidebarCompact" x-show.lg="!sidebarCompact">Đổi mật khẩu</span>
                            </a>
                        </nav>

                        {{-- Logout Button --}}
                        <div class="mt-6 pt-4 border-t border-gray-700">
                            <form method="POST" action="{{ route('logout') }}" class="w-full">
                                @csrf
                                <button type="submit" :title="sidebarCompact ? 'Đăng xuất' : ''"
                                        class="nav-item w-full bg-red-500/10 border border-red-500/20 text-red-400 hover:bg-red-500/20 hover:text-red-300">
                                    <i class="fas fa-sign-out-alt text-lg"
                                       :class="{ 'lg:mr-0': sidebarCompact, 'mr-3': !sidebarCompact }"></i>
                                    <span x-show="!sidebarCompact" x-show.lg="!sidebarCompact">Đăng xuất</span>
                                </button>
                            </form>
                        </div>

                        <!-- Compact Toggle (Desktop) -->
                        <div class="hidden lg:block mt-4 pt-4 border-t border-gray-700">
                            <button @click="sidebarCompact = !sidebarCompact"
                                    :title="sidebarCompact ? 'Mở rộng sidebar' : 'Thu gọn sidebar'"
                                    class="sidebar-toggle w-full flex items-center justify-center px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-lg transition-all duration-200">
                                <i class="fa-solid fa-angles-left transition-transform duration-200"
                                   :class="{ 'rotate-180': sidebarCompact }"></i>
                            </button>
                        </div>
                    </div>
                </div>

                {{-- Main Content Area --}}
                <div class="flex-1 min-w-0 w-full lg:w-auto">
                    <div class="bg-theme-card rounded-2xl shadow-xl p-4 lg:p-8">
                        @yield('user-content')
                    </div>
                </div>

            </div>
        </div>
    </div>
@endsection

@stack('scripts')
