@extends('themevieon2025::layouts.app')

@section('title', 'Trang không tìm thấy - 404')
@section('description', 'Trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển. Hãy quay lại trang chủ hoặc tìm kiếm nội dung khác.')

@section('content')
    <div class="min-h-screen bg-theme-primary flex items-center justify-center px-4">
        <div class="max-w-4xl mx-auto text-center">
            <!-- Error Illustration -->
            <div class="mb-8">
                <div class="relative inline-block">
                    <!-- 404 Number -->
                    <div class="text-8xl md:text-9xl font-bold text-main opacity-20 select-none">
                        404
                    </div>

                    <!-- Film Icon Overlay -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="bg-theme-secondary rounded-full p-6 shadow-2xl">
                            <i class="fas fa-film text-4xl md:text-5xl text-main"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-theme-primary mb-4">
                    Oops! Trang không tìm thấy
                </h1>
                <p class="text-lg text-theme-tertiary mb-6 max-w-2xl mx-auto">
                    Trang bạn đang tìm kiếm có thể đã bị di chuyển, xóa hoặc không tồn tại.
                    Đừng lo lắng, chúng tôi sẽ giúp bạn tìm thấy những bộ phim tuyệt vời khác!
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <a href="{{ route('home') }}"
                   class="inline-flex items-center px-6 py-3 bg-main hover:bg-green-600 text-white rounded-lg transition-colors font-medium">
                    <i class="fas fa-home mr-2"></i>
                    Về trang chủ
                </a>

                <a href="{{ route('search') }}"
                   class="inline-flex items-center px-6 py-3 bg-theme-button hover:bg-theme-button-hover text-theme-primary rounded-lg transition-colors font-medium border border-theme-primary">
                    <i class="fas fa-search mr-2"></i>
                    Tìm kiếm phim
                </a>
            </div>

            <!-- Quick Search -->
            <div class="mb-12">
                <div class="max-w-md mx-auto">
                    <h3 class="text-lg font-semibold text-theme-primary mb-4">
                        Tìm kiếm nhanh
                    </h3>
                    <form method="GET" action="{{ route('search') }}" class="flex gap-2">
                        <input type="text"
                               name="keyword"
                               placeholder="Nhập tên phim..."
                               class="flex-1 px-4 py-3 bg-theme-dropdown border border-theme-primary rounded-lg text-theme-primary placeholder-theme-tertiary focus:outline-none focus:ring-2 focus:ring-main">
                        <button type="submit"
                                class="px-6 py-3 bg-main hover:bg-green-600 text-white rounded-lg transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Popular Categories -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
                <a href="{{ route('categories.movies.index', 'hanh-dong') }}"
                   class="p-4 bg-theme-secondary hover:bg-theme-button-hover rounded-lg transition-colors group">
                    <i class="fas fa-fist-raised text-2xl text-main mb-2 group-hover:scale-110 transition-transform"></i>
                    <div class="text-sm font-medium text-theme-primary">Hành Động</div>
                </a>

                <a href="{{ route('categories.movies.index', 'tinh-cam') }}"
                   class="p-4 bg-theme-secondary hover:bg-theme-button-hover rounded-lg transition-colors group">
                    <i class="fas fa-heart text-2xl text-red-500 mb-2 group-hover:scale-110 transition-transform"></i>
                    <div class="text-sm font-medium text-theme-primary">Tình Cảm</div>
                </a>

                <a href="{{ route('categories.movies.index', 'hai-huoc') }}"
                   class="p-4 bg-theme-secondary hover:bg-theme-button-hover rounded-lg transition-colors group">
                    <i class="fas fa-laugh text-2xl text-yellow-500 mb-2 group-hover:scale-110 transition-transform"></i>
                    <div class="text-sm font-medium text-theme-primary">Hài Hước</div>
                </a>

                <a href="{{ route('categories.movies.index', 'kinh-di') }}"
                   class="p-4 bg-theme-secondary hover:bg-theme-button-hover rounded-lg transition-colors group">
                    <i class="fas fa-ghost text-2xl text-purple-500 mb-2 group-hover:scale-110 transition-transform"></i>
                    <div class="text-sm font-medium text-theme-primary">Kinh Dị</div>
                </a>
            </div>

            <!-- Help Text -->
            <div class="mt-12 text-sm text-theme-tertiary">
                <p>Nếu bạn tin rằng đây là lỗi, vui lòng
                    <a href="mailto:<EMAIL>" class="text-main hover:underline">liên hệ với chúng tôi</a>
                </p>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Auto-focus search input
        document.addEventListener('DOMContentLoaded', function () {
            const searchInput = document.querySelector('input[name="keyword"]');
            if (searchInput) {
                // Focus after a short delay to ensure page is fully loaded
                setTimeout(() => {
                    searchInput.focus();
                }, 500);
            }
        });
    </script>
@endpush
