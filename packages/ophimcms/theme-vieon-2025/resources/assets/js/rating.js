/**
 * Movie Rating System
 * Handles user ratings for movies with AJAX
 */

// Internal functions
function initializeRatingSystem() {
    const ratingContainers = document.querySelectorAll('[data-movie-rating]');

    ratingContainers.forEach(container => {
        const maxStars = parseInt(container.dataset.maxStars) || 5;
        const currentRating = parseFloat(container.dataset.currentRating) || 0;
        const movieSlug = container.dataset.movieSlug;
        const ratingUrl = container.dataset.ratingUrl;
        const readonly = container.dataset.readonly === 'true';
        const showHint = container.dataset.showHint === 'true';

        createRatingInterface(container, {
            maxStars,
            currentRating,
            movieSlug,
            ratingUrl,
            readonly,
            showHint
        });
    });
}

function createRatingInterface(container, options) {
    const { maxStars, currentRating, movieSlug, ratingUrl, readonly, showHint } = options;

    // Clear container
    container.innerHTML = '';

    // Create stars container
    const starsContainer = document.createElement('div');
    starsContainer.className = 'flex items-center justify-center gap-1 mb-2';

    // Create stars
    for (let i = 1; i <= maxStars; i++) {
        const star = document.createElement('i');
        star.className = `fas fa-star text-2xl cursor-pointer transition-colors duration-200 ${
            i <= currentRating ? 'text-yellow-400' : 'text-gray-300'
        }`;
        star.dataset.rating = i;

        if (!readonly) {
            // Add hover effects
            star.addEventListener('mouseenter', () => highlightStars(starsContainer, i));
            star.addEventListener('mouseleave', () => highlightStars(starsContainer, currentRating));
            star.addEventListener('click', () => {
                if (window.MovieRating && window.MovieRating.submitRating) {
                    window.MovieRating.submitRating(movieSlug, ratingUrl, i, container);
                }
            });
        }

        starsContainer.appendChild(star);
    }

    container.appendChild(starsContainer);

    // Add hint text if enabled
    if (showHint && !readonly) {
        const hintText = document.createElement('p');
        hintText.className = 'text-sm text-theme-tertiary text-center';
        hintText.textContent = 'Click để đánh giá phim';
        container.appendChild(hintText);
    }

    // Add rating display
    if (currentRating > 0) {
        const ratingDisplay = document.createElement('p');
        ratingDisplay.className = 'text-sm text-theme-secondary text-center mt-1';
        ratingDisplay.textContent = `${currentRating.toFixed(1)}/5`;
        container.appendChild(ratingDisplay);
    }
}

function highlightStars(starsContainer, rating) {
    const stars = starsContainer.querySelectorAll('i');
    stars.forEach((star, index) => {
        if (index < rating) {
            star.className = star.className.replace('text-gray-300', 'text-yellow-400');
        } else {
            star.className = star.className.replace('text-yellow-400', 'text-gray-300');
        }
    });
}

function submitRating(movieSlug, ratingUrl, rating, container) {
    // Show loading state
    showRatingLoading(container);

    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    if (!csrfToken) {
        showRatingError(container, 'Không thể xác thực yêu cầu. Vui lòng tải lại trang.');
        return;
    }

    // Submit rating via AJAX
    fetch(ratingUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': csrfToken,
            'Accept': 'application/json'
        },
        body: JSON.stringify({
            rating: rating,
            movie_slug: movieSlug
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status) {
            handleRatingSuccess(container, data, rating);
        } else {
            handleRatingError(container, data.message || 'Có lỗi xảy ra khi đánh giá phim.');
        }
    })
    .catch(error => {
        console.error('Rating error:', error);
        handleRatingError(container, 'Không thể kết nối đến server. Vui lòng thử lại.');
    });
}

function showRatingLoading(container) {
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'flex items-center justify-center py-4';
    loadingDiv.innerHTML = `
        <i class="fas fa-spinner fa-spin text-2xl text-main mr-2"></i>
        <span class="text-theme-primary">Đang đánh giá...</span>
    `;

    container.innerHTML = '';
    container.appendChild(loadingDiv);
}

function handleRatingSuccess(container, data, userRating) {
    // Update movie rating display
    if (window.MovieRating && window.MovieRating.updateDisplay) {
        window.MovieRating.updateDisplay(data.rating_star, data.rating_count);
    }

    // Show success message
    if (window.showNotification) {
        window.showNotification('success', data.message || 'Đánh giá phim thành công!');
    }

    // Update the rating interface with new user rating
    const movieSlug = container.closest('[data-movie-rating]').dataset.movieSlug;
    const ratingUrl = container.closest('[data-movie-rating]').dataset.ratingUrl;

    const options = {
        maxStars: 5,
        currentRating: userRating,
        movieSlug: movieSlug,
        ratingUrl: ratingUrl,
        readonly: false,
        showHint: true
    };

    createRatingInterface(container, options);

    // Store user rating for future reference
    container.dataset.currentUserRating = userRating;
}

function handleRatingError(container, message) {
    // Show error message only if message is not empty
    if (window.showNotification && message && message.trim()) {
        window.showNotification('error', message);
    }

    // Restore original rating interface
    const movieSlug = container.closest('[data-movie-rating]').dataset.movieSlug;
    const ratingUrl = container.closest('[data-movie-rating]').dataset.ratingUrl;
    const currentRating = parseFloat(container.dataset.currentUserRating) || 0;

    const options = {
        maxStars: 5,
        currentRating: currentRating,
        movieSlug: movieSlug,
        ratingUrl: ratingUrl,
        readonly: false,
        showHint: true
    };

    createRatingInterface(container, options);
}

function updateMovieRatingDisplay(newRating, newCount) {
    // Update all rating displays on the page
    const ratingDisplays = document.querySelectorAll('[data-rating-display]');
    ratingDisplays.forEach(display => {
        const ratingText = display.querySelector('.rating-text');
        const countText = display.querySelector('.count-text');
        
        if (ratingText) {
            ratingText.textContent = `${parseFloat(newRating).toFixed(1)}/5`;
        }
        
        if (countText) {
            countText.textContent = `(${newCount} đánh giá)`;
        }
    });
}

function showRatingError(container, message) {
    // Use global SweetAlert2 notification if available
    if (window.showNotification && message) {
        window.showNotification('error', message);
    }
    
    // Also show inline error in container for better UX
    const errorDiv = document.createElement('div');
    errorDiv.className = 'text-center py-4';
    errorDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle text-2xl text-red-500 mb-2"></i>
        <p class="text-sm text-red-500">${message}</p>
    `;

    container.innerHTML = '';
    container.appendChild(errorDiv);

    // Auto-hide error after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            // Restore original interface
            handleRatingError(container, '');
        }
    }, 5000);
}

// Define MovieRating object
window.MovieRating = {
    initialize: function() { 
        return initializeRatingSystem(); 
    },
    submitRating: function(movieSlug, ratingUrl, rating, container) {
        return submitRating(movieSlug, ratingUrl, rating, container);
    },
    updateDisplay: function(newRating, newCount) {
        return updateMovieRatingDisplay(newRating, newCount);
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (window.MovieRating) {
        window.MovieRating.initialize();
    }
});

// Backward compatibility - expose functions globally
window.submitRating = function(movieSlug, ratingUrl, rating, container) {
    return window.MovieRating.submitRating(movieSlug, ratingUrl, rating, container);
};

window.updateMovieRatingDisplay = function(newRating, newCount) {
    return window.MovieRating.updateDisplay(newRating, newCount);
};

console.log('✅ Movie Rating System loaded');
