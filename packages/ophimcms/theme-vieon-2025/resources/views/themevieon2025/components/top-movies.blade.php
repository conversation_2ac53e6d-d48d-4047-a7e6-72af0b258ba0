@if(isset($movies) && $movies->count() > 0)
    <div class="top-movies-section my-8">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-white">{{ $title ?? '[Section Title]' }}</h2>
            @if(isset($link) && $link != '#')
                <a href="{{ $link }}" class="text-blue-400 hover:text-blue-300 text-sm font-medium">
                    Xem thêm <i class="fas fa-chevron-right ml-1"></i>
                </a>
            @endif
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            @foreach($movies as $index => $movie)
                <div
                    class="top-movie-card bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-colors duration-200 relative">
                    {{-- Ranking badge --}}
                    <div class="absolute top-2 left-2 z-10">
                    <span
                        class="bg-gradient-to-r from-yellow-400 to-yellow-600 text-black text-lg font-bold px-3 py-1 rounded-full shadow-lg">
                        #{{ $index + 1 }}
                    </span>
                    </div>

                    <a href="{{ route('movie.show', $movie->slug) }}" class="block">
                        <div class="relative">
                            @if($movie->thumb_url)
                                <img src="{{ $movie->thumb_url }}"
                                     alt="{{ $movie->name }}"
                                     class="w-full h-64 object-cover"
                                     loading="lazy">
                            @else
                                <div class="w-full h-64 bg-gray-700 flex items-center justify-center">
                                    <i class="fas fa-film text-gray-500 text-3xl"></i>
                                </div>
                            @endif

                            {{-- Badge cho loại phim --}}
                            <div class="absolute top-2 right-2">
                                @if($movie->type == 'series')
                                    <span class="bg-red-500 text-white text-xs px-2 py-1 rounded">Bộ</span>
                                @else
                                    <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded">Lẻ</span>
                                @endif
                            </div>

                            {{-- Rating --}}
                            @if($movie->rating_star > 0)
                                <div class="absolute bottom-2 right-2">
                                <span class="bg-yellow-500 text-black text-sm px-2 py-1 rounded font-bold">
                                    {{ number_format($movie->rating_star, 1) }}
                                </span>
                                </div>
                            @endif

                            {{-- Episode count --}}
                            @if($movie->episode_count > 0)
                                <div class="absolute bottom-2 left-2">
                                <span class="bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                                    {{ $movie->episode_count }} tập
                                </span>
                                </div>
                            @endif
                        </div>

                        <div class="p-4">
                            <h3 class="text-base font-semibold text-white truncate mb-2" title="{{ $movie->name }}">
                                {{ $movie->name }}
                            </h3>

                            @if($movie->origin_name && $movie->origin_name != $movie->name)
                                <p class="text-sm text-gray-400 truncate mb-3" title="{{ $movie->origin_name }}">
                                    {{ $movie->origin_name }}
                                </p>
                            @endif

                            <div class="flex items-center justify-between text-sm text-gray-400">
                                @if($movie->publish_year)
                                    <span>{{ $movie->publish_year }}</span>
                                @endif
                                @if($movie->view_total > 0)
                                    <span class="flex items-center">
                                    <i class="fas fa-eye mr-1"></i>
                                    {{ number_format($movie->view_total) }}
                                </span>
                                @endif
                            </div>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
@endif
