<!DOCTYPE html>
<!--
Copyright (c) 2007-2019, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or https://ckeditor.com/sales/license/ckfinder
-->
<html>
<head>
	<meta charset="utf-8">
	<title>CKFinder 3 Samples</title>
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<!--[if lt IE 9]>
	<script src="js/html5shiv.min.js"></script>
	<![endif]-->
	<link href="css/sample.css" rel="stylesheet">
</head>
<body>
<header class="header-a">
	<div class="grid-container">
		<h1 class="header-a-logo grid-width-30">
			<a href="index.html"><img src="img/logo.png" alt="CKFinder Logo"></a>
		</h1>
		<nav class="navigation-b grid-width-70">
			<ul>
				<li><a href="https://ckeditor.com/docs/ckfinder/ckfinder3/" class="button-a">Documentation</a></li>
			</ul>
		</nav>
	</div>
</header>
<main class="grid-container">
	<nav class="tree-a tree-a-layout grid-width-30">
		<h1>CKFinder Samples</h1>
		<h2>Website Integration</h2>
		<ul>
			<li><a href="widget.html">Widget</a></li>
			<li><a href="popups.html">Popup</a></li>
			<li><a href="modals.html">Modal</a></li>
			<li><a href="full-page.html">Full Page</a></li>
		</ul>
		<h2 class="tree-a-no-sub"><a href="ckeditor.html">CKEditor Integration</a></h2>
		<h2>Skins</h2>
		<ul>
			<li><a href="skins-neko.html">Neko</a></li>
			<li><a href="skins-moono.html">Moono</a></li>
			<li><a href="skins-jquery-mobile.html">jQuery Mobile</a></li>
		</ul>
		<h2>User Interface</h2>
		<ul>
			<li><a href="user-interface-default.html">Default</a></li>
			<li><a href="user-interface-compact.html">Compact</a></li>
			<li><a href="user-interface-mobile.html">Mobile</a></li>
			<li><a href="user-interface-listview.html">List View</a></li>
		</ul>
		<h2 class="tree-a-no-sub"><a href="localization.html">Localization</a></h2>
		<h2 class="tree-a-active">Other</h2>
		<ul>
			<li class="tree-a-active"><a href="other-read-only.html">Read-only Mode</a></li>
			<li><a href="other-custom-configuration.html">Custom Configuration</a></li>
		</ul>
		<h2 class="tree-a-no-sub"><a href="plugin-examples.html">Plugin Examples</a></h2>
	</nav>
	<section class="content grid-width-70">
		<h1>Read-only Mode</h1>
		<p>Read-only mode can be enabled in CKFinder with the <a href="https://ckeditor.com/docs/ckfinder/ckfinder3/#!/api/CKFinder.Config-cfg-readOnly"><code>readOnly</code></a>
			configuration option. The user will be able to browse the files but will not be able to introduce any changes. Thanks to this setting you will be able to use
			CKFinder as an online gallery.</p>
		<p>Note: This will only disable certain UI elements. In order to successfully block file uploads and modifications, or to set read-only permissions for particular
			folders, you will need to adjust <a href="https://ckeditor.com/docs/ckfinder/ckfinder3-php/configuration.html#configuration_options_accessControl">ACL settings</a>
			accordingly in the server-side configuration file.</p>

<pre class="prettyprint"><code>CKFinder.widget( 'ckfinder-widget', {
	readOnly: true,
	width: '100%',
	height: 500
} );</code></pre>

		<div id="ckfinder-widget"></div>
		<h2>Simple Gallery</h2>
		<p>With a little bit of imagination it is possible to turn CKFinder into a very simple gallery. Here CKFinder is configured to
			open a file on double click, run without a toolbar and without the folders panel.</p>
		<div id="ckfinder-widget2"></div>
		<p>The code behind this setup is quite simple:</p>
		<pre class="prettyprint"><code>CKFinder.widget( 'ckfinder-widget2', {
	displayFoldersPanel: false,
	height: 500,
	id: 'gallery',
	readOnly: true,
	readOnlyExclude: 'Toolbars',
	thumbnailDefaultSize: 143,
	width: '100%'
} );
		</code></pre>
	</section>
</main>
<footer class="footer-a grid-container">
	<div class="grid-container">
		<p class="grid-width-100">
			CKFinder 3 &ndash; <a href="https://ckeditor.com/ckfinder/">https://ckeditor.com/ckfinder/</a>
		</p>
		<p class="grid-width-100">
			Copyright &copy; 2003-2019, <a class="samples" href="http://cksource.com/">CKSource</a> &ndash; Frederico
			Knabben. <a href="https://ckeditor.com/sales/license/ckfinder">All rights reserved</a>.
		</p>
	</div>
</footer>
<nav class="navigation-a">
	<div class="grid-container">
		<ul class="navigation-a-left grid-width-70">
			<li><a href="https://ckeditor.com/ckfinder/">Project Homepage</a></li>
			<li class="global-is-mobile-hidden"><a href="https://github.com/ckfinder/ckfinder/issues">I found a bug in CKFinder</a></li>
			<li class="global-is-mobile-hidden"><a class="icon-pos-right icon-navigation-a-github" href="https://github.com/ckfinder/ckfinder-docs-samples">Sample Plugins on GitHub</a></li>
		</ul>
	</div>
</nav>

<script src="js/sf.js"></script>
<script src="js/tree-a.js"></script>
<script src="../ckfinder.js"></script>
<script>
	CKFinder.widget( 'ckfinder-widget', {
		readOnly: true,
		width: '100%',
		height: 500
	} );
</script>
<script>
	CKFinder.widget( 'ckfinder-widget2', {
		displayFoldersPanel: false,
		height: 500,
		// The main reason why ID is set here is "thumbnailDefaultSize" specified below.
		// Without setting the ID, CKFinder would use remembered user setting from previously used instance.
		id: 'gallery',
		readOnly: true,
		readOnlyExclude: 'Toolbars',
		thumbnailDefaultSize: 143,
		width: '100%'
	} );
</script>
<script src="//cdn.rawgit.com/google/code-prettify/master/loader/run_prettify.js" type="text/javascript"></script>

</body>
</html>
