<!-- Footer Component -->
@php
    // Get footer data from theme options
    use Ophim\Core\Models\Category;
    $footer_content = get_theme_option('footer', '');
    $site_name = setting('site_homepage_title');
    $site_description = setting('site_description', 'Nền tảng xem phim trực tuyến hàng đầu Vi<PERSON> Nam');
    $current_year = date('Y');

    // Check if we have custom footer content
    $has_custom_footer = !empty(trim($footer_content));

    // Get footer style from theme options
    $footer_style = get_theme_option('footer_style', 'compact');
@endphp

<footer class="footer-section bg-theme-footer text-theme-primary border-t border-theme-footer">
    @if($has_custom_footer)
        <!-- Custom Footer Content -->
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div class="prose prose-invert max-w-none">
                {!! $footer_content !!}
            </div>
        </div>
    @else
        <!-- Default Footer Content -->
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="space-y-4">
                    <div class="flex items-center">
                        @include('themevieon2025::components.logo', ['class' => 'h-8 w-auto'])
                    </div>
                    <p class="text-theme-secondary text-sm leading-relaxed">
                        {{ $site_description }}
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-theme-tertiary hover:text-main transition-colors">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-theme-tertiary hover:text-main transition-colors">
                            <i class="fab fa-youtube text-xl"></i>
                        </a>
                        <a href="#" class="text-theme-tertiary hover:text-main transition-colors">
                            <i class="fab fa-telegram text-xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-theme-primary">Liên kết nhanh</h3>
                    <div class="space-y-2">
                        <a href="{{ route('home') }}"
                           class="block text-theme-secondary hover:text-main transition-colors">
                            Trang chủ
                        </a>
                        <a href="{{ route('movies.index') }}"
                           class="block text-theme-secondary hover:text-main transition-colors">
                            Phim mới
                        </a>
                        <a href="{{ route('movies.index', ['type' => 'series']) }}"
                           class="block text-theme-secondary hover:text-main transition-colors">
                            Phim bộ
                        </a>
                        <a href="{{ route('movies.index', ['type' => 'single']) }}"
                           class="block text-theme-secondary hover:text-main transition-colors">
                            Phim lẻ
                        </a>
                    </div>
                </div>

                <!-- Categories -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-theme-primary">Thể loại</h3>
                    <div class="space-y-2">
                        @php
                            $categories = Category::take(6)->get();
                        @endphp
                        @foreach($categories as $category)
                            <a href="{{ $category->getUrl() }}"
                               class="block text-theme-secondary hover:text-main transition-colors">
                                {{ $category->name }}
                            </a>
                        @endforeach
                    </div>
                </div>

                <!-- Support -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-theme-primary">Hỗ trợ</h3>
                    <div class="space-y-2">
                        <a href="#" class="block text-theme-secondary hover:text-main transition-colors">
                            Hướng dẫn sử dụng
                        </a>
                        <a href="#" class="block text-theme-secondary hover:text-main transition-colors">
                            Báo lỗi
                        </a>
                        <a href="#" class="block text-theme-secondary hover:text-main transition-colors">
                            Liên hệ
                        </a>
                        <a href="#" class="block text-theme-secondary hover:text-main transition-colors">
                            Điều khoản sử dụng
                        </a>
                    </div>
                </div>
            </div>

            <!-- Bottom Footer -->
            <div class="mt-8 pt-8 border-t border-theme-footer">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div class="text-theme-tertiary text-sm">
                        © {{ $current_year }} {{ $site_name }}. Tất cả quyền được bảo lưu.
                    </div>
                    <div class="flex items-center space-x-6 text-theme-tertiary text-sm">
                        <a href="#" class="hover:text-main transition-colors">Chính sách bảo mật</a>
                        <a href="#" class="hover:text-main transition-colors">Điều khoản dịch vụ</a>
                        <a href="#" class="hover:text-main transition-colors">Sitemap</a>
                    </div>
                </div>
            </div>
        </div>
    @endif
</footer>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const backToTopBtn = document.getElementById('back-to-top');

        // Only proceed if back-to-top button exists
        if (backToTopBtn) {
            // Show/hide back to top button
            window.addEventListener('scroll', function () {
                if (window.pageYOffset > 300) {
                    backToTopBtn.classList.remove('opacity-0', 'invisible');
                    backToTopBtn.classList.add('opacity-100', 'visible');
                } else {
                    backToTopBtn.classList.add('opacity-0', 'invisible');
                    backToTopBtn.classList.remove('opacity-100', 'visible');
                }
            });

            // Smooth scroll to top
            backToTopBtn.addEventListener('click', function () {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    });
</script>
