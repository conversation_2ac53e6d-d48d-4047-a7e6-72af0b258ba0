<!-- Modal Login/Register -->
<div x-data="{
    showLoginModal: false,
    loginTab: 'login',
    init() {
        window.addEventListener('show-login-modal', () => {
            this.showLoginModal = true;
        });
        window.addEventListener('close-login-modal', () => {
            this.showLoginModal = false;
        });
    }
}"
     x-show="showLoginModal"
     style="display: none"
     class="fixed inset-0 z-[9999] bg-black/70 backdrop-blur">
    <div @click.away="showLoginModal = false"
         class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-theme-card rounded-2xl shadow-2xl flex flex-col md:flex-row overflow-hidden relative">
        <button @click="showLoginModal = false"
                class="absolute top-4 right-4 z-10 text-2xl text-theme-muted hover:text-white focus:outline-none transition-colors duration-200">
            &times;
        </button>
        <!-- Cột trái: Logo, slogan, overlay ảnh nền -->
        <div class="md:w-1/2 w-full flex flex-col items-center justify-center p-8 bg-theme-secondary relative"
             style="background: linear-gradient(135deg, rgba(30,30,30,0.8) 0%, rgba(30,30,30,0.6) 100%), url('{{ asset('/themes/vieon-2025/images/auth-bg.jpg') }}') center/cover no-repeat;">
            <div class="absolute inset-0 bg-black/40"></div>
            <div class="relative z-10 flex flex-col items-center">
                @include('themevieon2025::components.logo', ['class' => 'h-8 w-auto mb-4'])
                <div class="text-xl font-bold text-white mb-2">{{ config('app.name') }}</div>
                <div class="text-theme-muted text-center text-sm">{{ setting('site_homepage_title') }}</div>
            </div>
        </div>
        <!-- Cột phải: Form -->
        <div class="md:w-1/2 w-full p-8 flex flex-col justify-center bg-theme-card">
            <template x-if="loginTab === 'login'">
                <div>
                    <div class="text-2xl font-bold mb-2 text-theme-primary">Đăng nhập</div>
                    <div class="text-sm mb-6 text-theme-muted">Nếu bạn chưa có tài khoản, <a href="#"
                                                                                             class="text-main font-semibold hover:underline"
                                                                                             @click.prevent="loginTab = 'register'">đăng
                            ký ngay</a></div>
                    <form method="POST" action="{{ route('api.login') }}" class="ajax-form space-y-4">
                        @csrf
                        <div>
                            <label for="login-email" class="block text-sm font-medium mb-1">Email</label>
                            <input id="login-email" name="email" type="email" required autofocus autocomplete="email"
                                   class="w-full px-3 py-2 rounded-lg border border-theme-input bg-theme-secondary text-theme-primary">
                        </div>
                        <div>
                            <label for="login-password" class="block text-sm font-medium mb-1">Mật khẩu</label>
                            <input id="login-password" name="password" type="password" required
                                   autocomplete="current-password"
                                   class="w-full px-3 py-2 rounded-lg border border-theme-input bg-theme-secondary text-theme-primary">
                        </div>
                        <div class="flex items-center mb-2">
                            <input id="remember" name="remember" type="checkbox" class="mr-2">
                            <label for="remember" class="text-sm">Ghi nhớ đăng nhập</label>
                        </div>
                        <button type="submit"
                                class="w-full py-2 rounded-lg bg-main text-white font-bold hover:bg-opacity-90 transition">
                            Đăng nhập
                        </button>
                    </form>
                </div>
            </template>
            <template x-if="loginTab === 'register'">
                <div>
                    <div class="text-2xl font-bold mb-2 text-theme-primary">Tạo tài khoản mới</div>
                    <div class="text-sm mb-6 text-theme-muted">Nếu bạn đã có tài khoản, <a href="#"
                                                                                           class="text-main font-semibold hover:underline"
                                                                                           @click.prevent="loginTab = 'login'">đăng
                            nhập</a></div>
                    <form method="POST" action="{{ route('api.register') }}" class="ajax-form space-y-4">
                        @csrf
                        <div>
                            <label for="register-name" class="block text-sm font-medium mb-1">Tên hiển thị</label>
                            <input id="register-name" name="name" type="text" required autocomplete="name"
                                   class="w-full px-3 py-2 rounded-lg border border-theme-input bg-theme-secondary text-theme-primary">
                        </div>
                        <div>
                            <label for="register-email" class="block text-sm font-medium mb-1">Email</label>
                            <input id="register-email" name="email" type="email" required autocomplete="email"
                                   class="w-full px-3 py-2 rounded-lg border border-theme-input bg-theme-secondary text-theme-primary">
                        </div>
                        <div>
                            <label for="register-password" class="block text-sm font-medium mb-1">Mật khẩu</label>
                            <input id="register-password" name="password" type="password" required
                                   autocomplete="new-password"
                                   class="w-full px-3 py-2 rounded-lg border border-theme-input bg-theme-secondary text-theme-primary">
                        </div>
                        <div>
                            <label for="register-password_confirmation" class="block text-sm font-medium mb-1">Nhập lại
                                mật khẩu</label>
                            <input id="register-password_confirmation" name="password_confirmation" type="password"
                                   required autocomplete="new-password"
                                   class="w-full px-3 py-2 rounded-lg border border-theme-input bg-theme-secondary text-theme-primary">
                        </div>
                        <button type="submit"
                                class="w-full py-2 rounded-lg bg-main text-white font-bold hover:bg-opacity-90 transition">
                            Đăng ký
                        </button>
                    </form>
                </div>
            </template>
        </div>
    </div>
</div>


