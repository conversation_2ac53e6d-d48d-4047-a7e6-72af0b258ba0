@php
    $logo = setting('site_logo', '');
    $brand = setting('site_brand', '');
    $defaultLogo = asset('themes/vieon-2025/images/logo.png');
    $class = $class ?? '';

    // Function to sanitize HTML and remove width/height attributes
    if (!function_exists('sanitizeLogoHtml')) {
        function sanitizeLogoHtml($html) {
            // Remove width and height attributes from img tags
            $html = preg_replace('/\s*width\s*=\s*["\'][^"\']*["\']/', '', $html);
            $html = preg_replace('/\s*height\s*=\s*["\'][^"\']*["\']/', '', $html);

            // Add CSS classes to img tags to ensure proper sizing
            $html = preg_replace('/<img([^>]*)>/', '<img$1 class="max-h-full w-auto object-contain" style="max-width: 100%; height: auto !important;">', $html);

            return $html;
        }
    }
@endphp

<div class="logo-container {{ $class }}">
    @if ($logo)
        @if (Str::startsWith($logo, '<'))
            {!! sanitizeLogoHtml($logo) !!}
        @elseif (filter_var($logo, FILTER_VALIDATE_URL) || Str::startsWith($logo, '/'))
            <img src="{{ $logo }}" alt="Logo" class="max-h-full w-auto object-contain"
                 style="max-width: 100%; height: auto !important;">
        @else
            <span class="font-bold text-main">{{ $logo }}</span>
        @endif
    @elseif ($brand)
        @if (Str::startsWith($brand, '<'))
            {!! sanitizeLogoHtml($brand) !!}
        @else
            <span class="font-bold text-main">{{ $brand }}</span>
        @endif
    @else
        <img src="{{ $defaultLogo }}" alt="Logo" class="max-h-full w-auto object-contain"
             style="max-width: 100%; height: auto !important;">
    @endif
</div>
