@if ($paginator->hasPages())
    <nav class="pagination-wrapper" role="navigation" aria-label="Pagination Navigation">
        <div class="flex items-center justify-between">
            <!-- Mobile Pagination Info -->
            <div class="flex justify-between flex-1 sm:hidden">
                @if ($paginator->onFirstPage())
                    <span
                        class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-theme-tertiary bg-theme-dropdown border border-theme-primary cursor-default leading-5 rounded-md">
                        Trước
                    </span>
                @else
                    <a href="{{ $paginator->previousPageUrl() }}"
                       class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-theme-primary bg-theme-button hover:bg-theme-button-hover border border-theme-primary leading-5 rounded-md transition-colors">
                        Trước
                    </a>
                @endif

                @if ($paginator->hasMorePages())
                    <a href="{{ $paginator->nextPageUrl() }}"
                       class="relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-theme-primary bg-theme-button hover:bg-theme-button-hover border border-theme-primary leading-5 rounded-md transition-colors">
                        Tiếp
                    </a>
                @else
                    <span
                        class="relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-theme-tertiary bg-theme-dropdown border border-theme-primary cursor-default leading-5 rounded-md">
                        Tiếp
                    </span>
                @endif
            </div>

            <!-- Desktop Pagination -->
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <!-- Pagination Info -->
                <div>
                    <p class="text-sm text-theme-tertiary leading-5">
                        Hiển thị
                        <span class="font-medium text-theme-primary">{{ $paginator->firstItem() }}</span>
                        đến
                        <span class="font-medium text-theme-primary">{{ $paginator->lastItem() }}</span>
                        trong tổng số
                        <span class="font-medium text-theme-primary">{{ $paginator->total() }}</span>
                        kết quả
                    </p>
                </div>

                <!-- Pagination Links -->
                <div>
                    <span class="relative z-0 inline-flex shadow-sm rounded-md">
                        {{-- Previous Page Link --}}
                        @if ($paginator->onFirstPage())
                            <span aria-disabled="true" aria-label="Trang trước">
                                <span
                                    class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-theme-tertiary bg-theme-dropdown border border-theme-primary cursor-default rounded-l-md leading-5"
                                    aria-hidden="true">
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            </span>
                        @else
                            <a href="{{ $paginator->previousPageUrl() }}"
                               rel="prev"
                               class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-theme-primary bg-theme-button hover:bg-theme-button-hover border border-theme-primary rounded-l-md leading-5 transition-colors"
                               aria-label="Trang trước">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        @endif

                        {{-- Pagination Elements --}}
                        @foreach ($elements as $element)
                            {{-- "Three Dots" Separator --}}
                            @if (is_string($element))
                                <span aria-disabled="true">
                                    <span
                                        class="relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-theme-tertiary bg-theme-dropdown border border-theme-primary cursor-default leading-5">{{ $element }}</span>
                                </span>
                            @endif

                            {{-- Array Of Links --}}
                            @if (is_array($element))
                                @foreach ($element as $page => $url)
                                    @if ($page == $paginator->currentPage())
                                        <span aria-current="page">
                                            <span
                                                class="relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-white bg-main border border-main cursor-default leading-5">{{ $page }}</span>
                                        </span>
                                    @else
                                        <a href="{{ $url }}"
                                           class="relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-theme-primary bg-theme-button hover:bg-theme-button-hover border border-theme-primary leading-5 transition-colors"
                                           aria-label="Trang {{ $page }}">
                                            {{ $page }}
                                        </a>
                                    @endif
                                @endforeach
                            @endif
                        @endforeach

                        {{-- Next Page Link --}}
                        @if ($paginator->hasMorePages())
                            <a href="{{ $paginator->nextPageUrl() }}"
                               rel="next"
                               class="relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-theme-primary bg-theme-button hover:bg-theme-button-hover border border-theme-primary rounded-r-md leading-5 transition-colors"
                               aria-label="Trang tiếp">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        @else
                            <span aria-disabled="true" aria-label="Trang tiếp">
                                <span
                                    class="relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-theme-tertiary bg-theme-dropdown border border-theme-primary cursor-default rounded-r-md leading-5"
                                    aria-hidden="true">
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            </span>
                        @endif
                    </span>
                </div>
            </div>
        </div>

        <!-- Page Size Selector -->
        <div class="mt-4 flex items-center justify-center">
            <div class="flex items-center gap-2 text-sm">
                <span class="text-theme-tertiary">Hiển thị:</span>
                <select onchange="changePageSize(this.value)"
                        class="px-2 py-1 bg-theme-dropdown border border-theme-primary rounded text-theme-primary text-sm focus:outline-none focus:ring-2 focus:ring-main">
                    <option value="12" {{ request('per_page', 24) == 12 ? 'selected' : '' }}>12</option>
                    <option value="24" {{ request('per_page', 24) == 24 ? 'selected' : '' }}>24</option>
                    <option value="48" {{ request('per_page', 24) == 48 ? 'selected' : '' }}>48</option>
                    <option value="96" {{ request('per_page', 24) == 96 ? 'selected' : '' }}>96</option>
                </select>
                <span class="text-theme-tertiary">phim/trang</span>
            </div>
        </div>
    </nav>

    <script>
        function changePageSize(perPage) {
            const url = new URL(window.location);
            url.searchParams.set('per_page', perPage);
            url.searchParams.set('page', 1); // Reset to first page
            window.location.href = url.toString();
        }
    </script>
@endif
