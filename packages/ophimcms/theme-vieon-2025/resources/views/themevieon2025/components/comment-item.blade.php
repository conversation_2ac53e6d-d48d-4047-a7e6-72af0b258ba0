{{-- Comment Item Component --}}
@php
    // Set default values for props
    $level = $level ?? 0;

    $maxLevel = 3; // Maximum nesting level
    $canReply = $level < $maxLevel;
    $indentClass = $level > 0 ? 'ml-' . ($level * 8) : '';
@endphp

<div class="comment-item comment-item-{{ $comment->id }} {{ $indentClass }}" data-comment-id="{{ $comment->id }}">
    <div class="flex items-start space-x-4 p-4 bg-theme-primary rounded-lg">
        {{-- User Avatar --}}
        <div class="flex-shrink-0">
            <img src="{{ $comment->user->getAvatar() }}"
                 alt="{{ $comment->user->name }}"
                 class="w-10 h-10 rounded-full object-cover">
        </div>

        {{-- Comment Content --}}
        <div class="flex-1 min-w-0">
            {{-- Comment Header --}}
            <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                    <span class="font-semibold text-theme-primary">{{ $comment->user->name }}</span>
                    <span class="text-xs text-theme-tertiary">{{ $comment->created_at->diffForHumans() }}</span>

                    {{-- Episode context indicator --}}
                    @if($comment->episode)
                        <span class="px-2 py-1 bg-main text-white text-xs rounded-full">
                            {{ $comment->episode->name }}
                        </span>
                    @endif
                </div>

                {{-- Comment Actions --}}
                <div class="flex items-center space-x-2">
                    @auth
                        @if($canReply)
                            <button class="reply-btn text-theme-tertiary hover:text-main text-sm transition-colors"
                                    data-comment-id="{{ $comment->id }}">
                                <i class="fas fa-reply mr-1"></i>
                                Trả lời
                            </button>
                        @endif

                        @if(Auth::id() === $comment->user_id)
                            <button class="delete-btn text-red-400 hover:text-red-300 text-sm transition-colors"
                                    data-comment-id="{{ $comment->id }}">
                                <i class="fas fa-trash mr-1"></i>
                                Xóa
                            </button>
                        @endif
                    @endauth
                </div>
            </div>

            {{-- Comment Text --}}
            <div class="text-theme-secondary leading-relaxed mb-3">
                {{ $comment->content }}
            </div>

            {{-- Reply Form (Hidden by default) --}}
            @auth
                @if($canReply)
                    <div id="reply-form-{{ $comment->id }}" class="reply-form hidden mt-4">
                        <form class="reply-comment-form space-y-3"
                              data-parent-id="{{ $comment->id }}"
                              data-movie-id="{{ $comment->movie_id }}"
                              @if($comment->episode_id) data-episode-id="{{ $comment->episode_id }}" @endif>
                            @csrf
                            <div>
                                <textarea name="content"
                                          rows="3"
                                          class="w-full px-3 py-2 bg-theme-secondary border border-theme-tertiary rounded-lg text-theme-primary placeholder-theme-tertiary focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent resize-none text-sm"
                                          placeholder="Viết phản hồi..."
                                          required></textarea>
                            </div>
                            <div class="flex items-center justify-end space-x-2">
                                <button type="button"
                                        class="cancel-reply-btn px-3 py-1 text-theme-tertiary hover:text-theme-primary text-sm transition-colors"
                                        data-comment-id="{{ $comment->id }}">
                                    Hủy
                                </button>
                                <button type="submit"
                                        class="px-4 py-1 bg-main hover:bg-green-600 text-white text-sm rounded transition-colors">
                                    Gửi
                                </button>
                            </div>
                        </form>
                    </div>
                @endif
            @endauth
        </div>
    </div>

    {{-- Child Comments --}}
    @if($comment->children && $comment->children->count() > 0)
        <div class="mt-4 space-y-4">
            @foreach($comment->children as $childComment)
                @include('themevieon2025::components.comment-item', [
                    'comment' => $childComment,
                    'level' => $level + 1
                ])
            @endforeach
        </div>
    @endif
</div>

{{-- All event handling is now managed by CommentManager in comment-section.blade.php --}}
