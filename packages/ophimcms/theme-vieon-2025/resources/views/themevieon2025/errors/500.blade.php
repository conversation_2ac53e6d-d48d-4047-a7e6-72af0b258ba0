@extends('themevieon2025::layouts.app')

@section('title', 'Lỗi máy chủ - 500')
@section('description', '<PERSON><PERSON> xảy ra lỗi máy chủ nội bộ. Chúng tôi đang khắc phục sự cố này. Vui lòng thử lại sau ít phút.')

@section('content')
    <div class="min-h-screen bg-theme-primary flex items-center justify-center px-4">
        <div class="max-w-4xl mx-auto text-center">
            <!-- Error Illustration -->
            <div class="mb-8">
                <div class="relative inline-block">
                    <!-- 500 Number -->
                    <div class="text-8xl md:text-9xl font-bold text-red-500 opacity-20 select-none">
                        500
                    </div>

                    <!-- Server Icon Overlay -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="bg-theme-secondary rounded-full p-6 shadow-2xl">
                            <i class="fas fa-server text-4xl md:text-5xl text-red-500"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <div class="mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-theme-primary mb-4">
                    Oops! Có lỗi xảy ra
                </h1>
                <p class="text-lg text-theme-tertiary mb-6 max-w-2xl mx-auto">
                    Máy chủ đang gặp sự cố tạm thời. Đội ngũ kỹ thuật của chúng tôi đã được thông báo
                    và đang khắc phục vấn đề này. Vui lòng thử lại sau ít phút.
                </p>
            </div>

            <!-- Status Indicator -->
            <div class="mb-8">
                <div class="inline-flex items-center px-4 py-2 bg-red-100 text-red-800 rounded-lg">
                    <div class="w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></div>
                    <span class="text-sm font-medium">Đang khắc phục sự cố</span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <button onclick="window.location.reload()"
                        class="inline-flex items-center px-6 py-3 bg-main hover:bg-green-600 text-white rounded-lg transition-colors font-medium">
                    <i class="fas fa-redo mr-2"></i>
                    Thử lại
                </button>

                <a href="{{ route('home') }}"
                   class="inline-flex items-center px-6 py-3 bg-theme-button hover:bg-theme-button-hover text-theme-primary rounded-lg transition-colors font-medium border border-theme-primary">
                    <i class="fas fa-home mr-2"></i>
                    Về trang chủ
                </a>
            </div>

            <!-- What You Can Do -->
            <div class="mb-12">
                <h3 class="text-lg font-semibold text-theme-primary mb-6">
                    Trong lúc chờ đợi, bạn có thể:
                </h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
                    <div class="p-6 bg-theme-secondary rounded-lg">
                        <i class="fas fa-clock text-2xl text-blue-500 mb-3"></i>
                        <h4 class="font-medium text-theme-primary mb-2">Chờ một chút</h4>
                        <p class="text-sm text-theme-tertiary">Thử lại sau 2-3 phút khi hệ thống đã ổn định</p>
                    </div>

                    <div class="p-6 bg-theme-secondary rounded-lg">
                        <i class="fas fa-home text-2xl text-green-500 mb-3"></i>
                        <h4 class="font-medium text-theme-primary mb-2">Về trang chủ</h4>
                        <p class="text-sm text-theme-tertiary">Khám phá những bộ phim mới nhất và hot nhất</p>
                    </div>

                    <div class="p-6 bg-theme-secondary rounded-lg">
                        <i class="fas fa-envelope text-2xl text-purple-500 mb-3"></i>
                        <h4 class="font-medium text-theme-primary mb-2">Báo cáo lỗi</h4>
                        <p class="text-sm text-theme-tertiary">Liên hệ với chúng tôi nếu lỗi vẫn tiếp tục</p>
                    </div>
                </div>
            </div>

            <!-- Error Details (for development) -->
            @if(config('app.debug') && isset($exception))
                <div class="mb-8">
                    <details class="text-left max-w-2xl mx-auto">
                        <summary class="cursor-pointer text-theme-tertiary hover:text-theme-primary mb-2">
                            Chi tiết lỗi (chỉ hiển thị trong môi trường phát triển)
                        </summary>
                        <div
                            class="bg-theme-secondary p-4 rounded-lg text-sm font-mono text-theme-tertiary overflow-auto">
                            <strong>Error:</strong> {{ $exception->getMessage() }}<br>
                            <strong>File:</strong> {{ $exception->getFile() }}<br>
                            <strong>Line:</strong> {{ $exception->getLine() }}
                        </div>
                    </details>
                </div>
            @endif

            <!-- Contact Info -->
            <div class="text-sm text-theme-tertiary">
                <p>Nếu vấn đề vẫn tiếp tục, vui lòng
                    <a href="mailto:<EMAIL>" class="text-main hover:underline">liên hệ hỗ trợ</a>
                    hoặc báo cáo qua
                    <a href="#" class="text-main hover:underline">Facebook</a>
                </p>
                <p class="mt-2 text-xs">
                    Mã lỗi: 500 | Thời gian: {{ now()->format('d/m/Y H:i:s') }}
                </p>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Auto-retry after 30 seconds
        let retryTimer = 30;
        let retryInterval;

        document.addEventListener('DOMContentLoaded', function () {
            // Show retry countdown
            const retryButton = document.querySelector('button[onclick="window.location.reload()"]');
            if (retryButton) {
                retryInterval = setInterval(() => {
                    retryTimer--;
                    if (retryTimer > 0) {
                        retryButton.innerHTML = `<i class="fas fa-redo mr-2"></i>Thử lại (${retryTimer}s)`;
                    } else {
                        retryButton.innerHTML = '<i class="fas fa-redo mr-2"></i>Thử lại';
                        clearInterval(retryInterval);
                        // Auto-reload
                        window.location.reload();
                    }
                }, 1000);
            }
        });

        // Clear interval when user manually clicks retry
        function manualRetry() {
            if (retryInterval) {
                clearInterval(retryInterval);
            }
            window.location.reload();
        }
    </script>
@endpush
