<?php

namespace Ophim\ThemeVieon2025\Models;

use Illuminate\Database\Eloquent\Model;

class Watchlist extends Model
{
    protected $table = 'watchlists';
    protected $fillable = [
        'user_id', 'movie_id',
    ];

    public function user()
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    public function movie()
    {
        return $this->belongsTo(\Ophim\Core\Models\Movie::class);
    }
}
