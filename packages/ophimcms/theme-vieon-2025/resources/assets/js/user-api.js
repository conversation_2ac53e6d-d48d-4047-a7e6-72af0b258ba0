// Prevent multiple declarations
if (typeof window.UserAPI !== 'undefined') {
    console.warn('UserAPI already exists, skipping redeclaration');
} else {

// Define UserAPI class
class UserAPI {
    constructor() {
        this.baseUrl = '/api';
        const tokenMeta = document.querySelector('meta[name="csrf-token"]');
        this.token = tokenMeta ? tokenMeta.getAttribute('content') : null;
        this.setupDefaults();
    }

    setupDefaults() {
        // Setup default headers for all requests
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (this.token) {
            this.defaultHeaders['X-CSRF-TOKEN'] = this.token;
        }
    }

    async request(url, options = {}) {
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            credentials: 'same-origin',
            ...options
        };

        try {
            // Use full URL if it starts with http, otherwise prepend baseUrl
            const fullUrl = url.startsWith('http') ? url : this.baseUrl + url;
            const response = await fetch(fullUrl, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Request failed');
            }

            return data;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }

    // Dashboard
    async getDashboard() {
        return this.request(window.route('api.user.dashboard'));
    }

    // Favorites
    async getFavorites(simple = false) {
        const url = window.route('api.user.favorites') + (simple ? '?simple=1' : '');
        return this.request(url);
    }

    async addToFavorites(movieId) {
        return this.request(window.route('api.user.favorites.add'), {
            method: 'POST',
            body: JSON.stringify({ movie_id: movieId })
        });
    }

    async removeFromFavorites(movieId) {
        return this.request(window.route('api.user.favorites.remove', { movieId }), {
            method: 'DELETE'
        });
    }

    async toggleFavorite(movieId) {
        // Use the new toggle API endpoint - more efficient
        try {
            return await this.request(window.route('api.user.favorites.toggle', { movieId }), {
                method: 'POST'
            });
        } catch (error) {
            console.error('Toggle favorite error:', error);
            throw error;
        }
    }

    // Watchlist
    async getWatchlist() {
        return this.request(window.route('api.user.watchlist'));
    }

    async addToWatchlist(movieId) {
        return this.request(window.route('api.user.watchlist.add'), {
            method: 'POST',
            body: JSON.stringify({ movie_id: movieId })
        });
    }

    async removeFromWatchlist(movieId) {
        return this.request(window.route('api.user.watchlist.remove', { movieId }), {
            method: 'DELETE'
        });
    }

    async toggleWatchlist(movieId) {
        // Use the new toggle API endpoint - more efficient
        try {
            return await this.request(window.route('api.user.watchlist.toggle', { movieId }), {
                method: 'POST'
            });
        } catch (error) {
            console.error('Toggle watchlist error:', error);
            throw error;
        }
    }

    // History
    async getHistory() {
        return this.request(window.route('api.user.history'));
    }

    // Recommendations
    async getRecommendations() {
        return this.request(window.route('api.user.recommendations'));
    }

    // Profile
    async getProfile() {
        return this.request(window.route('api.profile.show'));
    }

    async updateProfile(formData) {
        return this.request(window.route('api.profile.update'), {
            method: 'POST',
            body: formData,
            headers: {} // Let browser set Content-Type for FormData
        });
    }

    async deleteAccount(password) {
        return this.request(window.route('api.profile.destroy'), {
            method: 'DELETE',
            body: JSON.stringify({ password })
        });
    }

    // Settings
    async getSettings() {
        return this.request(window.route('api.settings.show'));
    }

    async updateSettings(settings) {
        return this.request(window.route('api.settings.update'), {
            method: 'POST',
            body: JSON.stringify(settings)
        });
    }

    // Password
    async changePassword(passwordData) {
        return this.request(window.route('api.password.change'), {
            method: 'POST',
            body: JSON.stringify(passwordData)
        });
    }

    // Rating
    async rateMovie(movieId, rating) {
        return this.request(window.route('api.user.rate', { movieId }), {
            method: 'POST',
            body: JSON.stringify({ rating })
        });
    }

    // Comments
    async getComments(movieId, page = 1) {
        return this.request(window.route('api.comments.index', { movieId }) + `?page=${page}`);
    }

    async getEpisodeComments(movieId, episodeId, page = 1) {
        return this.request(window.route('api.episodes.comments.index', { movieId, episodeId }) + `?page=${page}`);
    }

    async addComment(movieId, content, parentId = null, episodeId = null) {
        const data = { content, parent_id: parentId };
        if (episodeId) {
            data.episode_id = episodeId;
        }

        return this.request(window.route('api.comments.store', { movieId }), {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async addEpisodeComment(movieId, episodeId, content, parentId = null) {
        const data = { content, parent_id: parentId };

        return this.request(window.route('api.episodes.comments.store', { movieId, episodeId }), {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async deleteComment(commentId) {
        return this.request(window.route('api.comments.destroy', { id: commentId }), {
            method: 'DELETE'
        });
    }

    // Episode Comments
    async getEpisodeComments(movieId, episodeId, page = 1) {
        return this.request(window.route('api.episodes.comments.index', { movieId, episodeId }) + `?page=${page}`);
    }

    async addEpisodeComment(movieId, episodeId, content, parentId = null) {
        return this.request(window.route('api.episodes.comments.store', { movieId, episodeId }), {
            method: 'POST',
            body: JSON.stringify({ content, parent_id: parentId })
        });
    }

    // Movie Ratings
    async getMovieRatings(movieId, page = 1) {
        return this.request(window.route('api.ratings.index', { movieId }) + `?page=${page}`);
    }

    async addMovieRating(movieId, score, review = null) {
        return this.request(window.route('api.ratings.store', { movieId }), {
            method: 'POST',
            body: JSON.stringify({ score, review })
        });
    }

    async deleteMovieRating(movieId) {
        return this.request(window.route('api.ratings.destroy', { movieId }), {
            method: 'DELETE'
        });
    }
}

// Expose UserAPI to global scope
window.UserAPI = UserAPI;

// UI Helper functions
class UserUI {
    constructor(api) {
        this.api = api;
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Favorites buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="add-favorite"]')) {
                this.handleAddFavorite(e);
            }
            if (e.target.matches('[data-action="remove-favorite"]')) {
                this.handleRemoveFavorite(e);
            }
        });

        // Watchlist buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="add-watchlist"]')) {
                this.handleAddWatchlist(e);
            }
            if (e.target.matches('[data-action="remove-watchlist"]')) {
                this.handleRemoveWatchlist(e);
            }
        });

        // Rating buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="rate-movie"]')) {
                this.handleRateMovie(e);
            }
        });
    }

    async handleAddFavorite(e) {
        e.preventDefault();
        const movieId = e.target.dataset.movieId;

        try {
            const result = await this.api.addToFavorites(movieId);
            this.showMessage(result.message, 'success');
            this.updateFavoriteButton(e.target, true);
        } catch (error) {
            this.showMessage('Có lỗi xảy ra', 'error');
        }
    }

    async handleRemoveFavorite(e) {
        e.preventDefault();
        const movieId = e.target.dataset.movieId;

        try {
            const result = await this.api.removeFromFavorites(movieId);
            this.showMessage(result.message, 'success');
            this.updateFavoriteButton(e.target, false);
        } catch (error) {
            this.showMessage('Có lỗi xảy ra', 'error');
        }
    }

    async handleAddWatchlist(e) {
        e.preventDefault();
        const movieId = e.target.dataset.movieId;

        try {
            const result = await this.api.addToWatchlist(movieId);
            this.showMessage(result.message, 'success');
            this.updateWatchlistButton(e.target, true);
        } catch (error) {
            this.showMessage('Có lỗi xảy ra', 'error');
        }
    }

    async handleRemoveWatchlist(e) {
        e.preventDefault();
        const movieId = e.target.dataset.movieId;

        try {
            const result = await this.api.removeFromWatchlist(movieId);
            this.showMessage(result.message, 'success');
            this.updateWatchlistButton(e.target, false);
        } catch (error) {
            this.showMessage('Có lỗi xảy ra', 'error');
        }
    }

    async handleRateMovie(e) {
        e.preventDefault();
        const movieId = e.target.dataset.movieId;
        const rating = e.target.dataset.rating;

        try {
            const result = await this.api.rateMovie(movieId, rating);
            this.showMessage(result.message, 'success');
            this.updateRatingDisplay(movieId, rating);
        } catch (error) {
            this.showMessage('Có lỗi xảy ra', 'error');
        }
    }

    updateFavoriteButton(button, isFavorite) {
        if (isFavorite) {
            button.dataset.action = 'remove-favorite';
            button.textContent = 'Bỏ yêu thích';
            button.classList.add('favorited');
        } else {
            button.dataset.action = 'add-favorite';
            button.textContent = 'Yêu thích';
            button.classList.remove('favorited');
        }
    }

    updateWatchlistButton(button, isInWatchlist) {
        if (isInWatchlist) {
            button.dataset.action = 'remove-watchlist';
            button.textContent = 'Bỏ khỏi danh sách';
            button.classList.add('in-watchlist');
        } else {
            button.dataset.action = 'add-watchlist';
            button.textContent = 'Thêm vào danh sách';
            button.classList.remove('in-watchlist');
        }
    }

    updateRatingDisplay(movieId, rating) {
        const ratingContainer = document.querySelector(`[data-movie-rating="${movieId}"]`);
        if (ratingContainer) {
            ratingContainer.dataset.userRating = rating;
            // Update visual rating display
        }
    }

    showMessage(message, type = 'info') {
        // Implement your notification system here
        console.log(`${type.toUpperCase()}: ${message}`);
    }
}

// Expose UserUI to global scope
window.UserUI = UserUI;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    if (typeof window.UserAPI !== 'undefined') {
        console.log('UserAPI class found, creating instances...');
        window.userAPI = new UserAPI();
        window.userUI = new UserUI(window.userAPI);
        console.log('UserAPI and UserUI initialized successfully');
    } else {
        console.error('UserAPI class not found!');
    }
});

} // End of UserAPI check
