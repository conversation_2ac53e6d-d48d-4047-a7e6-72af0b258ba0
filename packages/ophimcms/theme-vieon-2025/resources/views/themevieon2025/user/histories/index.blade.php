@extends('themevieon2025::layouts.user')

@section('title', '<PERSON><PERSON>ch sử xem phim - ' . config('app.name') . ' 2025')

@section('user-content')
    <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-white mb-2"><PERSON><PERSON>ch sử xem phim</h2>
        <p class="text-gray-400"><PERSON>h sách phim bạn đã xem gần đây</p>
    </div>

    @if($histories->count())
        <div class="user-content-grid">
            @foreach($histories as $history)
                <div class="user-card group">
                    <a href="{{ route('movies.show', $history->movie->slug) }}" class="block">
                        <div class="relative overflow-hidden">
                            <img src="{{ $history->movie->poster_url }}"
                                 class="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105">
                            <div
                                class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <div class="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                                {{ gmdate('H:i:s', $history->position) }}
                            </div>
                            <div
                                class="absolute bottom-0 left-0 right-0 p-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                                <div class="text-white">
                                    <div class="font-semibold text-lg mb-1">{{ $history->movie->name }}</div>
                                    @if($history->episode)
                                        <div class="text-sm text-gray-300">Tập: {{ $history->episode }}</div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="p-4">
                            <div
                                class="font-semibold text-white text-lg mb-2 line-clamp-2">{{ $history->movie->name }}</div>
                            <div class="flex items-center justify-between mb-2">
                                @if($history->episode)
                                    <div class="text-sm text-gray-400">Tập: {{ $history->episode }}</div>
                                @endif
                                <div class="text-xs text-gray-500">
                                    {{ $history->updated_at->diffForHumans() }}
                                </div>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2 mb-2">
                                @php
                                    $progress = $history->movie->duration ? ($history->position / $history->movie->duration) * 100 : 0;
                                @endphp
                                <div class="bg-main h-2 rounded-full transition-all duration-300"
                                     style="width: {{ min($progress, 100) }}%"></div>
                            </div>
                            <div class="text-xs text-gray-500">
                                {{ gmdate('H:i:s', $history->position) }}
                                / {{ $history->movie->duration ? gmdate('H:i:s', $history->movie->duration) : 'N/A' }}
                            </div>
                        </div>
                    </a>
                    <div class="p-4 pt-0">
                        <form method="POST" action="{{ route('histories.destroy', $history->id) }}"
                              class="text-center ajax-form">
                            @csrf @method('DELETE')
                            <button type="submit"
                                    class="w-full text-red-400 hover:text-red-300 text-sm py-2 px-3 rounded-lg hover:bg-red-500/10 transition-all duration-200 flex items-center justify-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                Xóa khỏi lịch sử
                            </button>
                        </form>
                    </div>
                </div>
            @endforeach
        </div>

        @if($histories->hasPages())
            <div class="mt-8 flex justify-center">
                <div class="bg-gray-800/50 rounded-lg p-4">
                    {{ $histories->links() }}
                </div>
            </div>
        @endif
    @else
        <div class="text-center py-12">
            <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-gray-800/50 mb-6">
                <svg class="h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-white mb-2">Chưa có lịch sử xem phim</h3>
            <p class="text-gray-400 mb-6">Bạn chưa xem phim nào hoặc lịch sử đã được xóa.</p>
            <a href="{{ route('home') }}"
               class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-black bg-main hover:bg-main/90 transition-all duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                </svg>
                Khám phá phim mới
            </a>
        </div>
    @endif
@endsection
