<?php

namespace Ophim\ThemeVieon2025\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Ophim\Core\Models\Category;
use App\Models\Movie;
use Illuminate\Support\Facades\Cache;

class CategoryController extends Controller
{
    public function show($categorySlug, Request $request)
    {
        // Get category from cache
        $category = Cache::remember("category_{$categorySlug}", setting('site_cache_ttl', 5 * 60), function () use ($categorySlug) {
            return Category::where('slug', $categorySlug)->first();
        });

        // Handle category not found
        if (!$category) {
            abort(404, "Thể loại '{$categorySlug}' không tồn tại.");
        }

        // Generate SEO tags
        $category->generateSeoTags();

        // Get sorting parameters
        $sort = $request->get('sort', 'created_at');
        $order = $request->get('order', 'desc');

        // Build query with caching for popular sorts
        $cacheKey = "category_movies_{$category->id}_{$sort}_{$order}_" . request('page', 1);

        if (in_array($sort, ['created_at', 'view_total']) && $order === 'desc') {
            $movies = Cache::remember($cacheKey, setting('site_cache_ttl', 5 * 60), function () use ($category, $sort, $order) {
                return $this->getMoviesQuery($category, $sort, $order)->paginate(24);
            });
        } else {
            $movies = $this->getMoviesQuery($category, $sort, $order)->paginate(24);
        }

        return view('themevieon2025::pages.category', [
            'category' => $category,
            'movies' => $movies,
            'currentSort' => $sort,
            'currentOrder' => $order
        ]);
    }

    /**
     * Get movies query for category
     */
    private function getMoviesQuery($category, $sort = 'created_at', $order = 'desc')
    {
        $query = Movie::with(['categories', 'regions'])
            ->whereHas('categories', function ($q) use ($category) {
                $q->where('id', $category->id);
            })
            ->published();

        // Apply sorting
        switch ($sort) {
            case 'name':
                $query->orderBy('name', $order);
                break;
            case 'year':
                $query->orderBy('publish_year', $order);
                break;
            case 'view':
                $query->orderBy('view_total', $order);
                break;
            case 'rating':
                $query->orderBy('rating_star', $order);
                break;
            default:
                $query->orderBy('created_at', $order);
        }

        return $query;
    }
}
