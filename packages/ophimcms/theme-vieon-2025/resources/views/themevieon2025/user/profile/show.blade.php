@extends('themevieon2025::layouts.user')

@section('title', 'Thông tin cá nhân - ' . config('app.name') . ' 2025')

@section('user-content')
    <div class="max-w-2xl mx-auto">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-white mb-2">Thông tin cá nhân</h2>
            <p class="text-gray-400">C<PERSON><PERSON> nh<PERSON>t thông tin cá nhân của bạn</p>
        </div>

        {{-- Loading Overlay --}}
        <div id="profile-loading"
             class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
            <div class="bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-main"></div>
                <span class="text-white"><PERSON><PERSON> cập nhật...</span>
            </div>
        </div>

        {{-- Message Containers --}}
        <div id="profile-error"
             class="hidden mb-6 bg-red-500 bg-opacity-20 border border-red-500 text-red-200 px-4 py-3 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                          clip-rule="evenodd"></path>
                </svg>
                <span id="profile-error-message"></span>
            </div>
        </div>

        <div id="profile-success"
             class="hidden mb-6 bg-green-500 bg-opacity-20 border border-green-500 text-green-200 px-4 py-3 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clip-rule="evenodd"></path>
                </svg>
                <span id="profile-success-message"></span>
            </div>
        </div>

        <form id="profile-form" enctype="multipart/form-data" class="space-y-6">
            @csrf
            <div class="flex flex-col items-center space-y-4">
                <div class="relative">
                    <img src="{{ Auth::user()->getAvatar() }}"
                         class="w-24 h-24 rounded-full object-cover border-4 border-main/20">
                    <div class="absolute -bottom-2 -right-2 bg-main text-black rounded-full p-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="w-full">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Ảnh đại diện</label>
                    <input type="file" name="avatar"
                           class="block w-full text-sm text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-main file:text-black hover:file:bg-main/90 transition-all cursor-pointer"
                           accept="image/*">
                    @error('avatar')
                    <div class="text-red-400 text-sm mt-1">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Tên hiển thị</label>
                <input type="text" name="name"
                       class="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all"
                       value="{{ old('name', $user->name) }}" required>
                @error('name')
                <div class="text-red-400 text-sm mt-1">{{ $message }}</div>
                @enderror
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                <input type="email" value="{{ $user->email }}"
                       class="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-gray-400 cursor-not-allowed"
                       disabled readonly>
                <p class="text-gray-500 text-sm mt-1">Email không thể thay đổi</p>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Ngày sinh</label>
                <input type="date" name="birthday"
                       class="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all"
                       value="{{ old('birthday', $user->birthday) }}">
                @error('birthday')
                <div class="text-red-400 text-sm mt-1">{{ $message }}</div>
                @enderror
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Giới tính</label>
                <select name="gender"
                        class="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all">
                    <option value="">Chọn giới tính</option>
                    <option value="male" {{ old('gender', $user->gender) == 'male' ? 'selected' : '' }}>Nam</option>
                    <option value="female" {{ old('gender', $user->gender) == 'female' ? 'selected' : '' }}>Nữ</option>
                    <option value="other" {{ old('gender', $user->gender) == 'other' ? 'selected' : '' }}>Khác</option>
                </select>
                @error('gender')
                <div class="text-red-400 text-sm mt-1">{{ $message }}</div>
                @enderror
            </div>

            <button type="submit"
                    class="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-black bg-main hover:bg-main/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
                <span class="submit-text">Cập nhật thông tin</span>
                <span class="loading-text hidden">Đang cập nhật...</span>
            </button>
        </form>

        <!-- Navigation Links -->
        <div class="mt-8 pt-8 border-t border-gray-700">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{{ route('user.password.show') }}"
                   class="flex items-center justify-center px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-gray-300 hover:bg-gray-700/50 hover:text-white transition-all">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                    </svg>
                    Đổi mật khẩu
                </a>
                <a href="{{ route('user.settings.show') }}"
                   class="flex items-center justify-center px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-gray-300 hover:bg-gray-700/50 hover:text-white transition-all">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Cài đặt
                </a>
                <a href="{{ route('user.account.show') }}"
                   class="flex items-center justify-center px-4 py-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 hover:bg-red-500/20 hover:text-red-300 transition-all">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Xóa tài khoản
                </a>
            </div>
        </div>
    </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('profile-form');
            const avatarImg = form.querySelector('img');
            const avatarInput = form.querySelector('input[name="avatar"]');

            // Preview avatar on file select
            avatarInput.addEventListener('change', function (e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        avatarImg.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Handle form submission
            form.addEventListener('submit', async function (e) {
                e.preventDefault();

                const submitBtn = form.querySelector('button[type="submit"]');
                const submitText = submitBtn.querySelector('.submit-text');
                const loadingText = submitBtn.querySelector('.loading-text');

                try {
                    // Show loading state
                    showLoading(true);
                    hideMessage();
                    submitBtn.disabled = true;
                    submitText.classList.add('hidden');
                    loadingText.classList.remove('hidden');

                    // Prepare form data
                    const formData = new FormData(form);

                    // Call API
                    const result = await window.userAPI.updateProfile(formData);

                    if (result.status) {
                        showSuccess(result.message);

                        // Update avatar if changed
                        if (result.data && result.data.avatar_url) {
                            avatarImg.src = result.data.avatar_url;
                        }
                    } else {
                        showError(result.message || 'Có lỗi xảy ra');
                    }

                } catch (error) {
                    console.error('Error updating profile:', error);

                    if (error.response && error.response.status === 422) {
                        // Validation errors
                        const errors = error.response.data.errors;
                        let errorMessage = 'Dữ liệu không hợp lệ:\n';
                        for (const field in errors) {
                            errorMessage += `- ${errors[field].join(', ')}\n`;
                        }
                        showError(errorMessage);
                    } else {
                        showError('Có lỗi xảy ra khi cập nhật thông tin');
                    }
                } finally {
                    // Reset button state
                    showLoading(false);
                    submitBtn.disabled = false;
                    submitText.classList.remove('hidden');
                    loadingText.classList.add('hidden');
                }
            });
        });

        function showLoading(show) {
            const loadingEl = document.getElementById('profile-loading');
            if (show) {
                loadingEl.classList.remove('hidden');
            } else {
                loadingEl.classList.add('hidden');
            }
        }

        function showError(message) {
            const errorEl = document.getElementById('profile-error');
            const messageEl = document.getElementById('profile-error-message');
            messageEl.textContent = message;
            errorEl.classList.remove('hidden');

            // Auto hide after 5 seconds
            setTimeout(() => {
                errorEl.classList.add('hidden');
            }, 5000);
        }

        function showSuccess(message) {
            const successEl = document.getElementById('profile-success');
            const messageEl = document.getElementById('profile-success-message');
            messageEl.textContent = message;
            successEl.classList.remove('hidden');

            // Auto hide after 3 seconds
            setTimeout(() => {
                successEl.classList.add('hidden');
            }, 3000);
        }

        function hideMessage() {
            document.getElementById('profile-error').classList.add('hidden');
            document.getElementById('profile-success').classList.add('hidden');
        }
    </script>
@endpush
