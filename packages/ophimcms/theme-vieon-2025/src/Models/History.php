<?php

namespace Ophim\ThemeVieon2025\Models;

use Illuminate\Database\Eloquent\Model;

class History extends Model
{
    protected $table = 'histories';
    protected $fillable = [
        'user_id', 'movie_id', 'episode_id', 'progress',
    ];
    public function user()
    {
        return $this->belongsTo(\App\Models\User::class);
    }
    public function movie()
    {
        return $this->belongsTo(\Ophim\Core\Models\Movie::class);
    }
}
