<?php

namespace Ophim\ThemeVieon2025\Models;

use Illuminate\Database\Eloquent\Model;

class Comment extends Model
{
    protected $table = 'comments';
    protected $fillable = [
        'user_id', 'movie_id', 'episode_id', 'content', 'parent_id',
    ];

    public function user()
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    public function movie()
    {
        return $this->belongsTo(\Ophim\Core\Models\Movie::class);
    }

    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    public function episode()
    {
        return $this->belongsTo(\Ophim\Core\Models\Episode::class);
    }

    /**
     * Scope for movie-level comments (episode_id is null)
     */
    public function scopeMovieLevel($query)
    {
        return $query->whereNull('episode_id');
    }

    /**
     * Scope for episode-level comments
     */
    public function scopeEpisodeLevel($query, $episodeId = null)
    {
        if ($episodeId) {
            return $query->where('episode_id', $episodeId);
        }
        return $query->whereNotNull('episode_id');
    }

    /**
     * Scope for comments of a specific movie and episode context
     */
    public function scopeForContext($query, $movieId, $episodeId = null)
    {
        $query->where('movie_id', $movieId);

        if ($episodeId) {
            return $query->where('episode_id', $episodeId);
        }

        return $query->whereNull('episode_id');
    }

    /**
     * Check if comment is movie-level
     */
    public function isMovieLevel()
    {
        return is_null($this->episode_id);
    }

    /**
     * Check if comment is episode-level
     */
    public function isEpisodeLevel()
    {
        return !is_null($this->episode_id);
    }
}
