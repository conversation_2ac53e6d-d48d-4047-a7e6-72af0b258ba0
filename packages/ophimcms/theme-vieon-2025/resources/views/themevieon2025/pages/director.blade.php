@extends('themevieon2025::layouts.app')

@section('title', '<PERSON>m của đạo diễn ' . $director->name . ' - Trang ' . ($movies->currentPage() ?? 1))
@section('description', '<PERSON>em tất cả phim của đạo diễn ' . $director->name . '. <PERSON><PERSON> sách phim hay nhất do ' . $director->name . ' đ<PERSON><PERSON> diễn chất l<PERSON> HD miễn phí.')

@section('content')
    <div class="min-h-screen bg-theme-primary">
        {{-- Breadcrumb --}}
        @include('themevieon2025::components.breadcrumb', [
            'items' => [
                [
                    'label' => 'Đạo diễn',
                    'url' => '#',
                    'icon' => 'fas fa-video'
                ],
                [
                    'label' => $director->name,
                    'icon' => 'fas fa-award'
                ]
            ]
        ])

        <!-- Page Header -->
        <div class="bg-theme-secondary border-b border-theme-primary">
            <div class="container mx-auto px-4 py-6">

                <!-- Director Profile -->
                <div class="flex flex-col md:flex-row gap-6">
                    <!-- Director <PERSON><PERSON> -->
                    <div class="flex-shrink-0">
                        <div class="w-32 h-32 md:w-40 md:h-40 mx-auto md:mx-0">
                            @if($director->avatar)
                                <img src="{{ $director->avatar }}"
                                     alt="{{ $director->name }}"
                                     class="w-full h-full object-cover rounded-full border-4 border-main">
                            @else
                                <div
                                    class="w-full h-full bg-theme-dropdown rounded-full border-4 border-main flex items-center justify-center">
                                    <i class="fas fa-video text-4xl text-theme-tertiary"></i>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Director Info -->
                    <div class="flex-1 text-center md:text-left">
                        <h1 class="text-2xl md:text-3xl font-bold text-theme-primary mb-2">
                            <i class="fas fa-video mr-2 text-main"></i>
                            {{ $director->name }}
                        </h1>

                        @if($director->origin_name && $director->origin_name !== $director->name)
                            <p class="text-lg text-theme-secondary mb-3">{{ $director->origin_name }}</p>
                        @endif

                        <div
                            class="flex flex-wrap justify-center md:justify-start gap-4 text-sm text-theme-tertiary mb-4">
                            @if($director->birthday)
                                <div class="flex items-center gap-1">
                                    <i class="fas fa-birthday-cake"></i>
                                    <span>{{ date('d/m/Y', strtotime($director->birthday)) }}</span>
                                </div>
                            @endif

                            @if($director->nationality)
                                <div class="flex items-center gap-1">
                                    <i class="fas fa-flag"></i>
                                    <span>{{ $director->nationality }}</span>
                                </div>
                            @endif

                            <div class="flex items-center gap-1">
                                <i class="fas fa-film"></i>
                                <span>{{ $movies->total() }} phim</span>
                            </div>

                            @if($director->awards)
                                <div class="flex items-center gap-1">
                                    <i class="fas fa-trophy"></i>
                                    <span>{{ $director->awards }} giải thưởng</span>
                                </div>
                            @endif
                        </div>

                        @if($director->biography)
                            <div class="bg-theme-dropdown rounded-lg p-4">
                                <h3 class="font-semibold text-theme-primary mb-2">Tiểu sử</h3>
                                <p class="text-theme-secondary text-sm leading-relaxed line-clamp-3" id="biography">
                                    {{ $director->biography }}
                                </p>
                                @if(strlen($director->biography) > 200)
                                    <button onclick="toggleBiography()"
                                            class="text-main text-sm mt-2 hover:underline"
                                            id="biography-toggle">
                                        Xem thêm
                                    </button>
                                @endif
                            </div>
                        @endif
                    </div>

                    <!-- Stats -->
                    <div class="flex-shrink-0">
                        <div class="bg-theme-dropdown rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-main">{{ number_format($movies->total()) }}</div>
                            <div class="text-sm text-theme-tertiary">Tổng phim</div>

                            @if($movies->count() > 0)
                                @php
                                    $avgRating = $movies->avg('rating_star');
                                    $totalViews = $movies->sum('view_total');
                                @endphp

                                @if($avgRating > 0)
                                    <div class="mt-3 pt-3 border-t border-theme-primary">
                                        <div
                                            class="text-lg font-bold text-yellow-500">{{ number_format($avgRating, 1) }}</div>
                                        <div class="text-xs text-theme-tertiary">Đánh giá TB</div>
                                    </div>
                                @endif

                                @if($totalViews > 0)
                                    <div class="mt-3 pt-3 border-t border-theme-primary">
                                        <div
                                            class="text-lg font-bold text-blue-500">{{ number_format($totalViews) }}</div>
                                        <div class="text-xs text-theme-tertiary">Tổng lượt xem</div>
                                    </div>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container mx-auto px-4 py-6">
            <!-- Filter & Sort Bar -->
            <div class="bg-theme-secondary rounded-lg p-4 mb-6">
                <div class="flex flex-wrap items-center justify-between gap-4">
                    <!-- View Toggle -->
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-theme-tertiary">Hiển thị:</span>
                        <div class="flex bg-theme-dropdown rounded-lg p-1">
                            <button onclick="setViewMode('grid')"
                                    class="px-3 py-1 text-sm rounded transition-colors view-toggle {{ request('view', 'grid') === 'grid' ? 'bg-main text-white' : 'text-theme-primary hover:bg-theme-button-hover' }}">
                                <i class="fas fa-th mr-1"></i>
                                Lưới
                            </button>
                            <button onclick="setViewMode('list')"
                                    class="px-3 py-1 text-sm rounded transition-colors view-toggle {{ request('view') === 'list' ? 'bg-main text-white' : 'text-theme-primary hover:bg-theme-button-hover' }}">
                                <i class="fas fa-list mr-1"></i>
                                Danh sách
                            </button>
                        </div>
                    </div>

                    <!-- Sort Options -->
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-theme-tertiary">Sắp xếp:</span>
                        <div class="flex flex-wrap gap-2">
                            @php
                                $sortOptions = [
                                    'created_at' => 'Mới nhất',
                                    'view' => 'Lượt xem',
                                    'rating' => 'Đánh giá',
                                    'year' => 'Năm sản xuất',
                                    'name' => 'Tên phim'
                                ];
                            @endphp

                            @foreach($sortOptions as $sortKey => $sortLabel)
                                <a href="{{ request()->fullUrlWithQuery(['sort' => $sortKey, 'order' => (($currentSort ?? 'created_at') === $sortKey && ($currentOrder ?? 'desc') === 'desc') ? 'asc' : 'desc']) }}"
                                   class="px-3 py-1 text-xs rounded-lg transition-colors {{ ($currentSort ?? 'created_at') === $sortKey ? 'bg-main text-white' : 'bg-theme-button hover:bg-theme-button-hover text-theme-primary' }}">
                                    {{ $sortLabel }}
                                    @if(($currentSort ?? 'created_at') === $sortKey)
                                        <i class="fas fa-chevron-{{ ($currentOrder ?? 'desc') === 'desc' ? 'down' : 'up' }} ml-1"></i>
                                    @endif
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Movies Grid/List -->
            @if($movies->count() > 0)
                <div id="movies-container"
                     class="{{ request('view', 'grid') === 'list' ? 'space-y-4' : 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4' }} mb-6">
                    @foreach($movies as $movie)
                        @include('themevieon2025::components.movie-card', [
                            'movie' => $movie,
                            'layout' => request('view', 'grid'),
                            'showRating' => true,
                            'showActions' => true
                        ])
                    @endforeach
                </div>

                <!-- Pagination -->
                @if($movies->hasPages())
                    <div class="bg-theme-secondary rounded-lg p-4">
                        {{ $movies->appends(request()->query())->links('themevieon2025::components.pagination-ajax') }}
                    </div>
                @endif
            @else
                <!-- No Movies -->
                <div class="bg-theme-secondary rounded-lg p-8 text-center">
                    <div class="max-w-md mx-auto">
                        <i class="fas fa-video text-6xl text-theme-tertiary mb-4"></i>
                        <h3 class="text-xl font-semibold text-theme-primary mb-2">
                            Chưa có phim nào
                        </h3>
                        <p class="text-theme-tertiary mb-6">
                            Hiện tại chưa có phim nào do {{ $director->name }} đạo diễn. Hãy quay lại sau hoặc khám phá
                            các đạo diễn khác.
                        </p>
                        <a href="{{ route('home') }}"
                           class="inline-block px-6 py-2 bg-main hover:bg-green-600 text-white rounded-lg transition-colors">
                            Về trang chủ
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // View mode toggle
        function setViewMode(mode) {
            const url = new URL(window.location);
            url.searchParams.set('view', mode);
            window.location.href = url.toString();
        }

        // Biography toggle
        function toggleBiography() {
            const biography = document.getElementById('biography');
            const toggle = document.getElementById('biography-toggle');

            if (biography.classList.contains('line-clamp-3')) {
                biography.classList.remove('line-clamp-3');
                toggle.textContent = 'Thu gọn';
            } else {
                biography.classList.add('line-clamp-3');
                toggle.textContent = 'Xem thêm';
            }
        }

        // AJAX pagination
        document.addEventListener('DOMContentLoaded', function () {
            // Handle pagination clicks
            document.addEventListener('click', function (e) {
                if (e.target.matches('.pagination a') || e.target.closest('.pagination a')) {
                    e.preventDefault();
                    const link = e.target.matches('a') ? e.target : e.target.closest('a');
                    loadPage(link.href);
                }
            });

            function loadPage(url) {
                // Show loading state
                const container = document.getElementById('movies-container');
                container.style.opacity = '0.5';

                fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'text/html'
                    }
                })
                    .then(response => response.text())
                    .then(html => {
                        // Parse the response and update content
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(html, 'text/html');

                        // Update movies container
                        const newContainer = doc.getElementById('movies-container');
                        if (newContainer) {
                            container.innerHTML = newContainer.innerHTML;
                        }

                        // Update pagination
                        const pagination = document.querySelector('.pagination');
                        const newPagination = doc.querySelector('.pagination');
                        if (pagination && newPagination) {
                            pagination.innerHTML = newPagination.innerHTML;
                        }

                        // Update URL
                        window.history.pushState({}, '', url);

                        // Scroll to top
                        window.scrollTo({top: 0, behavior: 'smooth'});
                    })
                    .catch(error => {
                        console.error('AJAX pagination error:', error);
                    })
                    .finally(() => {
                        container.style.opacity = '1';
                    });
            }
        });
    </script>
@endpush
