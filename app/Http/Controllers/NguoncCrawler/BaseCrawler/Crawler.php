<?php

namespace App\Http\Controllers\NguoncCrawler\BaseCrawler;

use App\Http\Controllers\NguoncCrawler\Contracts\BaseCrawler;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Ophim\Core\Models\Movie;
use Illuminate\Support\Str;
use Ophim\Core\Models\Actor;
use Ophim\Core\Models\Category;
use Ophim\Core\Models\Director;
use Ophim\Core\Models\Episode;
use Ophim\Core\Models\Region;
use Ophim\Core\Models\Tag;

class Crawler extends BaseCrawler
{
    public function handle()
    {
        ini_set('default_socket_timeout', 900);
        $payload = json_decode($body = file_get_contents($this->link), true);

        $movie = Movie::where('slug', $payload['movie']['slug'])
            ->orWhere('name', $payload['movie']['name'])
            ->first();

        if (!$this->hasChange($movie, md5($body)) && $this->forceUpdate == false) {
            return false;
        }

        $isAddNew = false;

        if ($movie) {
            $year = $movie->year ?? $movie->publish_year;
            $this->checkIsInExcludedListHasMovie($payload, $year);
            $movie->updated_at = now();
            $info = (new CollectorIfExist($payload, $movie))->get();
            $movie->update(collect($info)->only($this->fields)->merge(['update_checksum' => md5($body)])->toArray());
        } else {
            $this->checkIsInExcludedList($payload);
            $info = (new Collector($payload, $this->fields, $this->forceUpdate))->get();
            $movie = Movie::create(array_merge($info, [
                'update_handler' => static::class,
                'update_identity' => $payload['movie']['_id'],
                'update_checksum' => md5($body)
            ]));
            $isAddNew = true;
        }

        $this->updateEpisodes($movie, $payload);

        if ($isAddNew) {
            $this->syncActors($movie, $payload);
            $this->syncDirectors($movie, $payload);
            $this->syncCategories($movie, $payload);
            $this->syncRegions($movie, $payload);
            $this->syncTags($movie, $payload);
            $this->syncStudios($movie, $payload);
            $keyApiChatGPT = $this->getAllKeysFromCSV();
            $this->updateContent($movie, $keyApiChatGPT);
        }
    }

    protected function hasChange(?Movie $movie, $checksum)
    {
        return is_null($movie) || ($movie->update_checksum != $checksum);
    }

    protected function checkIsInExcludedList($payload)
    {
        $newType = $payload['movie']['type'];
        if (in_array($newType, $this->excludedType)) {
            throw new \Exception("Thuộc định dạng đã loại trừ");
        }

        $newYear = $payload['movie']['year'];
        if (!is_array($this->allowYear)) {
            $listAllowYear = (!empty($this->allowYear) || $this->allowYear !== "") ? array_filter(explode("|", $this->allowYear)) : "";
            if (!empty($listAllowYear) && !in_array($newYear, $listAllowYear)) {
                throw new \Exception("Thuộc năm đã loại trừ: " . $newYear);
            }
        }

        $newCategories = collect($payload['movie']['category'])->pluck('name')->toArray();
        if (array_intersect($newCategories, $this->excludedCategories)) {
            throw new \Exception("Thuộc thể loại đã loại trừ");
        }

        $newRegions = collect($payload['movie']['country'])->pluck('name')->toArray();
        if (array_intersect($newRegions, $this->excludedRegions)) {
            throw new \Exception("Thuộc quốc gia đã loại trừ");
        }
    }
    
    protected function checkIsInExcludedListHasMovie($payload, $movie_year)
    {
        $newType = $payload['movie']['type'];
        if (in_array($newType, $this->excludedType)) {
            throw new \Exception("Thuộc định dạng đã loại trừ");
        }

        $newYear = $payload['movie']['year'] > 0 ? $payload['movie']['year'] : $movie_year;
        if (!is_array($this->allowYear)) {
            $listAllowYear = (!empty($this->allowYear) || $this->allowYear !== "") ? array_filter(explode("|", $this->allowYear)) : "";
            if (!empty($listAllowYear) && !in_array($newYear, $listAllowYear)) {
                throw new \Exception("Thuộc năm đã loại trừ: " . $newYear);
            }
        }

        $newCategories = collect($payload['movie']['category'])->pluck('name')->toArray();
        if (array_intersect($newCategories, $this->excludedCategories)) {
            throw new \Exception("Thuộc thể loại đã loại trừ");
        }

        $newRegions = collect($payload['movie']['country'])->pluck('name')->toArray();
        if (array_intersect($newRegions, $this->excludedRegions)) {
            throw new \Exception("Thuộc quốc gia đã loại trừ");
        }
    }

    protected function syncActors($movie, array $payload)
    {
        if (!in_array('actors', $this->fields)) return;
        $actors = [];
        foreach ($payload['movie']['actor'] as $actor) {
            if (!trim($actor)) continue;
            $actors[] = Actor::firstOrCreate(['name' => trim($actor)])->id;
        }
        $movie->actors()->sync($actors);
    }

    protected function syncDirectors($movie, array $payload)
    {
        if (!in_array('directors', $this->fields)) return;

        $directors = [];
        foreach ($payload['movie']['director'] as $director) {
            if (!trim($director)) continue;
            $directors[] = Director::firstOrCreate(['name' => trim($director)])->id;
        }
        $movie->directors()->sync($directors);
    }

    protected function syncCategories($movie, array $payload)
    {
        if (!in_array('categories', $this->fields)) return;
        $categories = [];
        foreach ($payload['movie']['category'] as $category) {
            if (!trim($category['name'])) continue;
            $categories[] = Category::firstOrCreate(['name' => trim($category['name'])])->id;
        }
        if ($payload['movie']['type'] === 'hoathinh') $categories[] = Category::firstOrCreate(['name' => 'Hoạt Hình'])->id;
        if ($payload['movie']['type'] === 'tvshows') $categories[] = Category::firstOrCreate(['name' => 'TV Shows'])->id;
        $movie->categories()->sync($categories);
    }

    protected function syncRegions($movie, array $payload)
    {
        if (!in_array('regions', $this->fields)) return;

        $regions = [];
        foreach ($payload['movie']['country'] as $region) {
            if (!trim($region['name'])) continue;
            $regions[] = Region::firstOrCreate(['name' => trim($region['name'])])->id;
        }
        $movie->regions()->sync($regions);
    }

    protected function syncTags($movie, array $payload)
    {
        if (!in_array('tags', $this->fields)) return;

        $tags = [];
        $tags[] = Tag::firstOrCreate(['name' => trim($movie->name)])->id;
        $tags[] = Tag::firstOrCreate(['name' => trim($movie->origin_name)])->id;

        $movie->tags()->sync($tags);
    }

    protected function syncStudios($movie, array $payload)
    {
        if (!in_array('studios', $this->fields)) return;
    }

    protected function updateEpisodes($movie, $payload)
    {
        if (!in_array('episodes', $this->fields)) return;
        $flag = 0;
        foreach ($payload['episodes'] as $server) {
        //    $newServer = 'Vietsub';        
            $server['server_name'] = 'Vietsub';    //Đặt mặc định server là Vietsub
            $movieOfServer = $movie->episodesWithServer($server['server_name'])->get();
            foreach ($server['server_data'] as $episode) {
                
                if (isset($episode['link_embed'])) {
                    // if (!isset($movieOfServer[$flag]->id)) {    Fix lỗi ko cào được
                        Episode::updateOrCreate([
                            'link' => $episode['link_embed'],
                            'slug' => 'tap-' . Str::slug($episode['name']),
                            'type' => 'embed'
                        ], [
                            'name' => $episode['name'],
                            'movie_id' => $movie->id,
                            'server' => $server['server_name']
                        ]);
                    // }
                    $flag++;
                } 
          /*      if ($episode['link_m3u8']) {
         //           if (!isset($movieOfServer[$flag]->id)) {
                        Episode::create([
                            'name' => $episode['name'],
                            'movie_id' => $movie->id,
                            'server' => $newServer,
                            'type' => 'm3u8',
                            'link' => $episode['link_m3u8'],
                            'slug' => 'tap-' . Str::slug($episode['name'])
                        ]);
         //           }
                    $flag++;
                } */
            }
        }
        for ($i = 0; $i < count($movieOfServer); $i++) {
            if (isset($movieOfServer[$i]->name) && strpos(strtolower($movieOfServer[$i]->name), 'Preview')) {
                $movieOfServer[$i]->delete();
           }
        }
    }  
    
 /*   protected function updateEpisodes($movie, $payload)
    {
        if (!in_array('episodes', $this->fields)) return;
        $flag = 0;

        foreach ($payload['episodes'] as $server) {
            $newServer = 'Vietsub';
            $movieOfServer = $movie->episodesWithServer($newServer)->get();

            foreach ($server['server_data'] as $episode) {
                if ($episode['link_m3u8']) {
                    if (!isset($movieOfServer[$flag]->id)) {
                            Episode::create([
                            'name' => $episode['name'],
                            'movie_id' => $movie->id,
                            'server' => $newServer,
                            'type' => 'm3u8',
                            'link' => $episode['link_m3u8'],
                            'slug' => 'tap-' . Str::slug($episode['name'])
                        ]);
                    }
                    $flag++;
                }
            }
        }

        for ($i = 0; $i < count($movieOfServer); $i++) {
            if (isset($movieOfServer[$i]->name) && strpos(strtolower($movieOfServer[$i]->name), 'preview')) {
                $movieOfServer[$i]->delete();
            }
        }
    } */


    protected function updateContent($movie, $keyApiChatGPT)
    {
        $content = $movie['content'];
        $name = $movie['name'];
        $origin_name = $movie['origin_name'];
        $year = $movie['publish_year'];
        $status = $movie['status'];
        $episode_time = $movie['episode_time'];
        $episode_total = $movie['episode_total'];

        $categories = null;
        $actors = null;
        $directors = null;

        // Kiểm tra độ dài của nội dung
        $contentLength = mb_strlen($content, 'UTF-8');

        if ($contentLength < 200) {
            if (count($movie->categories)) {
                $categories = "Thuộc thể loại " . $movie->categories->take(1)->map(function ($category) {
                    return $category->name;
                })->implode(', ');
            }

        if (count($movie->directors)) {
            $directors = "Được đạo diễn bởi " . $movie->directors->take(1)->map(function ($director) {
                return $director->name;
            })->implode(', ');
        }

        if (count($movie->actors)) {
            $actors = "với sự tham gia của các diễn viên " . $movie->actors->take(5)->map(function ($actor) {
                return $actor->name;
            })->implode(', ');
        }
        } else {
        // Đặt các biến là null nếu nội dung dài hơn 200 ký tự
            $categories = null;
            $actors = null;
            $directors = null;
        }

        if (!is_null('content')) {
            $prompt = " Dựa theo nội dung sau đây viết một đoạn giới thiệu phim :{$name} theo phong cách kể chuyện '$content'.";
            $prompt = strip_tags($prompt);
        } else {
            // bắt đầu prompt
            $prompt = "Dựa vào các thông tin sau, hãy viết cho tôi 1 đoạn giới thiệu bộ phim {$name}, tên khác {$origin_name}";
            // nếu có đạo diễn thì thêm đoạn dưới
            if ($directors) {
                $prompt .= " được đạo diễn bởi $directors";
            }
            // nếu có diễn viên thì thêm đoạn dưới
            if ($actors) {
                $prompt .= " với sự tham gia của các diễn viên $actors";
            }
            // nối tiếp với đoạn dưới
            $prompt .= " và có nội dung khái quát {$content} bằng tiếng Việt";
        }

        $dataContent = $this->callToChatGPT($prompt, $keyApiChatGPT);
        if ($dataContent) {
            $movie->update([
                'content' => $dataContent,
                'prompt' => $prompt
            ]);
        } else {
            $movie->update([
                'content' => $content,
                'prompt' => null
            ]);
        }
    }

     public function callToChatGPT($prompt, $keyApiChatGPT)
    {
        $openKey = $this->getRandomKeyValueFromCSV($keyApiChatGPT) ?? env('OPENAI_API_KEY');

        $data = Http::timeout(120)->withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $openKey,
        ])->post("https://api.openai.com/v1/chat/completions", [
            "model" => "gpt-3.5-turbo",
            'messages' => [
                [
                    "role" => "system",
                    "content" => "Dựa vào nội dung được cung cấp hãy viết 1 đoạn văn bắt đầu bằng tên phim và sử dụng các cụm từ ' xoay quanh' kể về ' ở câu đầu tiên , không sử dụng cụm từ ' là một bộ phim 'ở câu đầu tiên, không sử dụng quá 2 lần cho 1 cụm từ trong đoạn văn để cải thiện xếp hạng trên các công cụ tìm kiếm."
                ],
                [
                    "role" => "user",
                    "content" => $prompt
                ]
            ],
            'temperature' => 0.5,
            'n' => 1,
            "max_tokens" => 2000,
            "top_p" => 1.0,
            "frequency_penalty" => 0.52,
            "presence_penalty" => 0.5,
            "stop" => ["11."],
        ])->json();

        $content = null;

        if (!empty($data) && isset($data['choices'])) {
            $content = $this->cutStringToMaxlengthWithEllipsis(str_replace('"', '', $data['choices'][0]['message']['content']));
        }

        if (($data && isset($data['error'])) || !$data) {
            Log::info("Call ChatGPT Key [{$openKey}] Error vendor/hacoidev/ophim-crawler/src/Crawler.php:283 || " . $data['error']['message']);
        }

        return $content;
    }

    function cutStringToMaxlengthWithEllipsis($inputString, $maxLength = 2200)
    {
        if (mb_strlen($inputString) <= $maxLength) {
            return $inputString;
        }

        $truncatedString = mb_substr($inputString, 0, $maxLength);
        $lastDotIndex = mb_strrpos($truncatedString, '.');

        if ($lastDotIndex !== false && $lastDotIndex > $maxLength - 30) {
            $truncatedString = mb_substr($truncatedString, 0, $lastDotIndex + 1);
        }

        return $truncatedString . '...';
    }

    function getRandomKeyValueFromCSV($keys)
    {
        if (!empty($keys)) {
            // Lấy ngẫu nhiên một phần tử từ mảng $keys
            return $keys[array_rand($keys)];
        }
        return null;
    }

    function getAllKeysFromCSV()
    {
        $csvPath = config_path('chatGPT/key.csv'); // Đường dẫn mới
        if (File::exists($csvPath)) {
            $csvContent = File::get($csvPath);
            $keys = explode("\n", $csvContent);

            // Loại bỏ khoảng trắng và các phần tử trống trong mảng
            $keys = array_map('trim', $keys);
            $keys = array_filter($keys);

            return $keys;
        }

        return [];
    }
}
