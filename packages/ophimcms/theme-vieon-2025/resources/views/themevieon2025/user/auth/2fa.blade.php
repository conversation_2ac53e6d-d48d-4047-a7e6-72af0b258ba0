@extends('themevieon2025::layouts.app')
@section('content')
    <div class="max-w-md mx-auto mt-20 bg-gray-900 p-8 rounded-lg shadow-lg text-center">
        <h2 class="text-2xl font-bold mb-6"><PERSON><PERSON><PERSON> thực 2 lớp (2FA)</h2>
        <form method="POST" action="{{ route('api.2fa.verify') }}" class="ajax-form">
            @csrf
            <div class="mb-4">
                <label class="block mb-1">Nhập mã xác thực đã gửi tới email</label>
                <input type="text" name="code" maxlength="6"
                       class="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white text-center text-lg tracking-widest"
                       required autofocus>
                @error('code')
                <div class="text-red-500 text-sm mt-1">{{ $message }}</div>@enderror
            </div>
            <button type="submit"
                    class="w-full bg-main hover:bg-yellow-500 text-black font-bold py-2 rounded transition"><PERSON><PERSON><PERSON> thực
            </button>
        </form>
        <form method="POST" action="{{ route('2fa.send') }}" class="mt-4 ajax-form">
            @csrf
            <button type="submit" class="text-main hover:underline">Gửi lại mã xác thực</button>
        </form>
        <div class="mt-4">
            <a href="/" class="text-main hover:underline">Về trang chủ</a>
        </div>
    </div>
@endsection
