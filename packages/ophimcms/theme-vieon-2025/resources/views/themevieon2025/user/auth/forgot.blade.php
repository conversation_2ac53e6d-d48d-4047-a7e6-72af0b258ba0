@extends('themevieon2025::layouts.app')
@section('content')
    <div class="max-w-md mx-auto mt-20 bg-gray-900 p-8 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold mb-6 text-center"><PERSON><PERSON><PERSON><PERSON> mật khẩu</h2>
        <form method="POST" action="{{ route('api.password.email') }}" class="ajax-form">
            @csrf
            <div class="mb-4">
                <label class="block mb-1">Email</label>
                <input type="email" name="email"
                       class="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" required autofocus>
                @error('email')
                <div class="text-red-500 text-sm mt-1">{{ $message }}</div>@enderror
            </div>
            <button type="submit"
                    class="w-full bg-main hover:bg-yellow-500 text-black font-bold py-2 rounded transition">Gửi link đặt
                lại mật khẩu
            </button>
        </form>
        <div class="mt-4 text-center">
            <a href="{{ route('login') }}" class="text-main hover:underline">Quay lại đăng nhập</a>
        </div>
    </div>
@endsection
