<?php

namespace App\Jobs;

use App\Models\LinkIndex;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GoogleIndexingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The URL to update.
     *
     * @var string
     */
    protected $url;

    /**
     * Create a new job instance.
     *
     * @param  string  $url
     * @return void
     */
    public function __construct($url)
    {
        $this->url = $url;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $detail = [
                "slug" => $this->url,
                "status" => 0
            ];
            $linkIndex = LinkIndex::firstOrCreate($detail);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
    }
}
