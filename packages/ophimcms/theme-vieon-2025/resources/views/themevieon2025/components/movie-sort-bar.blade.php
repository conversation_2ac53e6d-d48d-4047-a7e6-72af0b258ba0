<!-- Movie Sort & View Toggle Bar -->
<div class="bg-theme-secondary rounded-lg p-4 mb-6">
    <div class="flex flex-wrap items-center justify-between gap-4">
        <!-- View Toggle -->
        <div class="flex items-center gap-2">
            <span class="text-sm text-theme-tertiary">Hiển thị:</span>
            <div class="flex bg-theme-dropdown rounded-lg p-1">
                <button onclick="setViewMode('grid')"
                        class="px-3 py-1 text-sm rounded transition-colors view-toggle {{ request('view', 'grid') === 'grid' ? 'bg-main text-white' : 'text-theme-primary hover:bg-theme-button-hover' }}">
                    <i class="fas fa-th mr-1"></i>
                    Lưới
                </button>
                <button onclick="setViewMode('list')"
                        class="px-3 py-1 text-sm rounded transition-colors view-toggle {{ request('view') === 'list' ? 'bg-main text-white' : 'text-theme-primary hover:bg-theme-button-hover' }}">
                    <i class="fas fa-list mr-1"></i>
                    Danh sách
                </button>
            </div>
        </div>

        <!-- Sort Options -->
        <div class="flex items-center gap-2">
            <span class="text-sm text-theme-tertiary">Sắp xếp:</span>
            <div class="flex flex-wrap gap-2">
                @php
                    $sortOptions = [
                        'created_at' => 'Mới nhất',
                        'view' => 'Lượt xem',
                        'rating' => 'Đánh giá',
                        'year' => 'Năm sản xuất',
                        'name' => 'Tên phim'
                    ];
                @endphp

                @foreach($sortOptions as $sortKey => $sortLabel)
                    <a href="{{ request()->fullUrlWithQuery(['sort' => $sortKey, 'order' => ($currentSort === $sortKey && $currentOrder === 'desc') ? 'asc' : 'desc']) }}"
                       class="px-3 py-1 text-xs rounded-lg transition-colors {{ $currentSort === $sortKey ? 'bg-main text-white' : 'bg-theme-button hover:bg-theme-button-hover text-theme-primary' }}">
                        {{ $sortLabel }}
                        @if($currentSort === $sortKey)
                            <i class="fas fa-chevron-{{ $currentOrder === 'desc' ? 'down' : 'up' }} ml-1"></i>
                        @endif
                    </a>
                @endforeach
            </div>
        </div>
    </div>
</div>
