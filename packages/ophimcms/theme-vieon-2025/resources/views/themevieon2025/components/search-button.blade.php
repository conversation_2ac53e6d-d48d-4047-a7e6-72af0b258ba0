<!-- Desktop Search -->
<div class="hidden lg:block">
    <div class="relative" data-advanced-search data-api-url="{{ route('api.search') }}"
         data-popular-url="{{ route('api.search.popular') }}">
        <form method="GET" action="{{ route('search') }}" class="flex items-center">
            <div class="relative">
                <input type="text"
                       name="keyword"
                       value="{{ request('keyword', '') }}"
                       placeholder="Tìm kiếm phim, diễn viên..."
                       class="w-64 bg-white/10 text-white placeholder-white/70 rounded-lg px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-main focus:bg-white/20 border border-white/20"
                       autocomplete="off">
                <button type="submit"
                        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-main transition-colors">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>

        <!-- Search Results Dropdown -->
        <div
            class="suggestions-dropdown absolute top-full left-0 right-0 bg-theme-dropdown border border-theme-primary rounded-b-lg shadow-lg z-50 hidden max-h-96 overflow-y-auto">
            <!-- Results will be populated by JavaScript -->
        </div>
    </div>
</div>

<!-- Mobile Search Button -->
<div class="lg:hidden">
    <button onclick="openMobileSearch()"
            class="text-white hover:text-main text-xl p-2 hover:bg-white/10 rounded-lg transition-all duration-300"
            title="Tìm kiếm">
        <i class="fas fa-search"></i>
    </button>
</div>

<!-- Mobile Search Overlay -->
<div id="mobileSearchOverlay" class="fixed inset-0 bg-black/80 z-[9999] lg:hidden hidden">
    <div class="flex flex-col h-full">
        <!-- Header -->
        <div class="flex items-center justify-between p-4 bg-theme-secondary border-b border-theme-primary">
            <h3 class="text-lg font-semibold text-theme-primary">Tìm kiếm phim</h3>
            <button onclick="closeMobileSearch()" class="p-2 text-theme-tertiary hover:text-theme-primary">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Search Input -->
        <div class="p-4 bg-theme-secondary border-b border-theme-primary">
            <div class="relative" data-advanced-search data-api-url="{{ route('api.search') }}"
                 data-popular-url="{{ route('api.search.popular') }}">
                <form method="GET" action="{{ route('search') }}" class="flex items-center">
                    <div class="relative flex-1">
                        <input type="text"
                               name="keyword"
                               placeholder="Nhập tên phim bạn muốn tìm..."
                               class="w-full bg-theme-dropdown text-theme-primary placeholder-theme-tertiary rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-main border border-theme-primary"
                               autocomplete="off">
                        <button type="submit"
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-theme-tertiary hover:text-main transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>

                <!-- Mobile Search Results Dropdown -->
                <div
                    class="suggestions-dropdown absolute top-full left-0 right-0 bg-theme-dropdown border border-theme-primary rounded-b-lg shadow-lg z-50 hidden max-h-80 overflow-y-auto">
                    <!-- Results will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Search Results Area -->
        <div class="flex-1 overflow-hidden bg-theme-primary">
            <!-- Additional results area for mobile -->
        </div>
    </div>
</div>

<script>
    // Mobile search functions
    function openMobileSearch() {
        const overlay = document.getElementById('mobileSearchOverlay');
        if (overlay) {
            overlay.classList.remove('hidden');
            // Focus on input after animation
            setTimeout(() => {
                const input = overlay.querySelector('input[name="keyword"]');
                if (input) input.focus();
            }, 100);

            // Prevent body scroll
            document.body.style.overflow = 'hidden';
        }
    }

    function closeMobileSearch() {
        const overlay = document.getElementById('mobileSearchOverlay');
        if (overlay) {
            overlay.classList.add('hidden');
            document.body.style.overflow = '';
        }
    }

    // Close mobile search on escape key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            closeMobileSearch();
        }
    });

    // Close mobile search when clicking overlay
    document.getElementById('mobileSearchOverlay')?.addEventListener('click', function (e) {
        if (e.target === this) {
            closeMobileSearch();
        }
    });

    // Advanced search is now loaded via layout, no need to load script here
    // Just ensure initialization happens
    document.addEventListener('DOMContentLoaded', function () {
        // Advanced search script is loaded in layout, just ensure initialization
        if (typeof AdvancedSearch !== 'undefined' && !window.advancedSearchInstance) {
            window.advancedSearchInstance = new AdvancedSearch();
        }
    });
</script>

<style>
    /* Advanced Search Dropdown Styles */
    .suggestions-dropdown {
        max-width: 100%;
        min-width: 300px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        border-top: none;
        animation: slideDown 0.2s ease-out;
        backdrop-filter: blur(8px);
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .suggestion-item {
        transition: all 0.2s ease;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .suggestion-item:hover {
        transform: translateX(2px);
        background: rgba(0, 255, 102, 0.1);
    }

    .suggestion-item:last-child {
        border-bottom: none;
    }

    .suggestion-item img {
        transition: transform 0.2s ease;
        border-radius: 4px;
    }

    .suggestion-item:hover img {
        transform: scale(1.05);
    }

    /* Loading indicator */
    .search-loading {
        z-index: 10;
    }

    /* Mobile search overlay */
    #mobileSearchOverlay {
        backdrop-filter: blur(4px);
        animation: fadeIn 0.3s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    /* Section headers */
    .section-header {
        position: sticky;
        top: 0;
        z-index: 10;
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(8px);
    }

    /* Scrollbar styling for dropdown */
    .suggestions-dropdown::-webkit-scrollbar {
        width: 6px;
    }

    .suggestions-dropdown::-webkit-scrollbar-track {
        background: transparent;
    }

    .suggestions-dropdown::-webkit-scrollbar-thumb {
        background: rgba(156, 163, 175, 0.5);
        border-radius: 3px;
    }

    .suggestions-dropdown::-webkit-scrollbar-thumb:hover {
        background: rgba(156, 163, 175, 0.7);
    }

    /* Dark mode adjustments */
    @media (prefers-color-scheme: dark) {
        .suggestions-dropdown {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
    }

    /* Mobile responsive adjustments */
    @media (max-width: 768px) {
        .suggestions-dropdown {
            min-width: 280px;
            max-height: 70vh;
        }

        .suggestion-item {
            padding: 0.75rem;
        }

        .suggestion-item img {
            width: 2.5rem;
            height: 3.5rem;
        }
    }

    /* Focus states */
    input[name="keyword"]:focus + .suggestions-dropdown {
        border-color: var(--main-color, #00ff66);
    }

    /* High contrast mode support */
    @media (prefers-contrast: more) {
        .suggestions-dropdown {
            border-width: 2px;
        }

        .suggestion-item:hover {
            outline: 2px solid currentColor;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .suggestions-dropdown,
        .suggestion-item,
        .suggestion-item img {
            animation: none;
            transition: none;
        }

        .suggestion-item:hover {
            transform: none;
        }

        .suggestion-item:hover img {
            transform: none;
        }
    }

    /* Desktop specific styles */
    @media (min-width: 1024px) {
        .suggestions-dropdown {
            width: 400px;
            max-height: 500px;
        }
    }

    /* Ensure dropdown appears above other elements */
    .suggestions-dropdown {
        z-index: 9999;
    }

    /* Style for no results message */
    .no-results {
        padding: 1rem;
        text-align: center;
        color: rgba(255, 255, 255, 0.7);
        font-style: italic;
    }

    /* Style for loading state */
    .loading-state {
        padding: 1rem;
        text-align: center;
        color: rgba(255, 255, 255, 0.7);
    }

    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #00ff66;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
</style>
