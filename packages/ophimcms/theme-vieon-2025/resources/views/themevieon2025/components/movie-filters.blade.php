<!-- Movie Filters Sidebar -->
<div class="space-y-6">
    <!-- Filter Header -->
    <div class="bg-theme-secondary rounded-lg p-4">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-theme-primary">
                <i class="fas fa-filter mr-2"></i>
                Bộ lọc
            </h3>
            @if(request()->hasAny(['category', 'region', 'year', 'type', 'sort']))
                <a href="{{ route('search', ['keyword' => request('keyword')]) }}"
                   class="text-sm text-red-500 hover:text-red-600 transition-colors">
                    <i class="fas fa-times mr-1"></i>
                    Xóa bộ lọc
                </a>
            @endif
        </div>

        <!-- Active Filters -->
        @if(request()->hasAny(['category', 'region', 'year', 'type']))
            <div class="flex flex-wrap gap-2 mb-4">
                @if(request('category'))
                    <span class="inline-flex items-center gap-1 px-2 py-1 bg-main text-white text-xs rounded-lg">
                        Thể loại: {{ $categories->where('slug', request('category'))->first()->name ?? request('category') }}
                        <a href="{{ request()->fullUrlWithQuery(['category' => null]) }}" class="hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </a>
                    </span>
                @endif

                @if(request('region'))
                    <span class="inline-flex items-center gap-1 px-2 py-1 bg-main text-white text-xs rounded-lg">
                        Quốc gia: {{ $regions->where('slug', request('region'))->first()->name ?? request('region') }}
                        <a href="{{ request()->fullUrlWithQuery(['region' => null]) }}" class="hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </a>
                    </span>
                @endif

                @if(request('year'))
                    <span class="inline-flex items-center gap-1 px-2 py-1 bg-main text-white text-xs rounded-lg">
                        Năm: {{ request('year') }}
                        <a href="{{ request()->fullUrlWithQuery(['year' => null]) }}" class="hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </a>
                    </span>
                @endif

                @if(request('type'))
                    <span class="inline-flex items-center gap-1 px-2 py-1 bg-main text-white text-xs rounded-lg">
                        Loại: {{ request('type') === 'series' ? 'Phim bộ' : 'Phim lẻ' }}
                        <a href="{{ request()->fullUrlWithQuery(['type' => null]) }}" class="hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </a>
                    </span>
                @endif
            </div>
        @endif
    </div>

    <!-- Category Filter -->
    @if($categories && $categories->count() > 0)
        <div class="bg-theme-secondary rounded-lg p-4">
            <h4 class="font-semibold text-theme-primary mb-3">
                <i class="fas fa-tags mr-2"></i>
                Thể loại
            </h4>
            <div class="space-y-2 max-h-48 overflow-y-auto">
                @foreach($categories->take(20) as $category)
                    <a href="{{ request()->fullUrlWithQuery(['category' => $category->slug, 'page' => null]) }}"
                       class="block px-3 py-2 text-sm rounded-lg transition-colors {{ request('category') === $category->slug ? 'bg-main text-white' : 'text-theme-primary hover:bg-theme-button-hover' }}">
                        {{ $category->name }}
                    </a>
                @endforeach

                @if($categories->count() > 20)
                    <button onclick="toggleMoreCategories()"
                            class="w-full px-3 py-2 text-sm text-theme-tertiary hover:text-theme-primary transition-colors">
                        <i class="fas fa-chevron-down mr-1"></i>
                        Xem thêm
                    </button>

                    <div id="more-categories" class="hidden space-y-2">
                        @foreach($categories->skip(20) as $category)
                            <a href="{{ request()->fullUrlWithQuery(['category' => $category->slug, 'page' => null]) }}"
                               class="block px-3 py-2 text-sm rounded-lg transition-colors {{ request('category') === $category->slug ? 'bg-main text-white' : 'text-theme-primary hover:bg-theme-button-hover' }}">
                                {{ $category->name }}
                            </a>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    @endif

    <!-- Region Filter -->
    @if($regions && $regions->count() > 0)
        <div class="bg-theme-secondary rounded-lg p-4">
            <h4 class="font-semibold text-theme-primary mb-3">
                <i class="fas fa-globe mr-2"></i>
                Quốc gia
            </h4>
            <div class="space-y-2 max-h-48 overflow-y-auto">
                @foreach($regions->take(15) as $region)
                    <a href="{{ request()->fullUrlWithQuery(['region' => $region->slug, 'page' => null]) }}"
                       class="block px-3 py-2 text-sm rounded-lg transition-colors {{ request('region') === $region->slug ? 'bg-main text-white' : 'text-theme-primary hover:bg-theme-button-hover' }}">
                        {{ $region->name }}
                    </a>
                @endforeach

                @if($regions->count() > 15)
                    <button onclick="toggleMoreRegions()"
                            class="w-full px-3 py-2 text-sm text-theme-tertiary hover:text-theme-primary transition-colors">
                        <i class="fas fa-chevron-down mr-1"></i>
                        Xem thêm
                    </button>

                    <div id="more-regions" class="hidden space-y-2">
                        @foreach($regions->skip(15) as $region)
                            <a href="{{ request()->fullUrlWithQuery(['region' => $region->slug, 'page' => null]) }}"
                               class="block px-3 py-2 text-sm rounded-lg transition-colors {{ request('region') === $region->slug ? 'bg-main text-white' : 'text-theme-primary hover:bg-theme-button-hover' }}">
                                {{ $region->name }}
                            </a>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    @endif

    <!-- Year Filter -->
    @if($years && $years->count() > 0)
        <div class="bg-theme-secondary rounded-lg p-4">
            <h4 class="font-semibold text-theme-primary mb-3">
                <i class="fas fa-calendar mr-2"></i>
                Năm sản xuất
            </h4>
            <div class="grid grid-cols-3 gap-2">
                @foreach($years->take(18) as $year)
                    <a href="{{ request()->fullUrlWithQuery(['year' => $year, 'page' => null]) }}"
                       class="px-2 py-1 text-xs text-center rounded-lg transition-colors {{ request('year') == $year ? 'bg-main text-white' : 'bg-theme-button hover:bg-theme-button-hover text-theme-primary' }}">
                        {{ $year }}
                    </a>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Type Filter -->
    <div class="bg-theme-secondary rounded-lg p-4">
        <h4 class="font-semibold text-theme-primary mb-3">
            <i class="fas fa-film mr-2"></i>
            Loại phim
        </h4>
        <div class="space-y-2">
            <a href="{{ request()->fullUrlWithQuery(['type' => 'series', 'page' => null]) }}"
               class="block px-3 py-2 text-sm rounded-lg transition-colors {{ request('type') === 'series' ? 'bg-main text-white' : 'text-theme-primary hover:bg-theme-button-hover' }}">
                <i class="fas fa-tv mr-2"></i>
                Phim bộ
            </a>
            <a href="{{ request()->fullUrlWithQuery(['type' => 'single', 'page' => null]) }}"
               class="block px-3 py-2 text-sm rounded-lg transition-colors {{ request('type') === 'single' ? 'bg-main text-white' : 'text-theme-primary hover:bg-theme-button-hover' }}">
                <i class="fas fa-video mr-2"></i>
                Phim lẻ
            </a>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="bg-theme-secondary rounded-lg p-4">
        <h4 class="font-semibold text-theme-primary mb-3">
            <i class="fas fa-star mr-2"></i>
            Phim nổi bật
        </h4>
        <div class="space-y-2">
            <a href="{{ route('search', ['sort' => 'view', 'order' => 'desc']) }}"
               class="block px-3 py-2 text-sm text-theme-primary hover:bg-theme-button-hover rounded-lg transition-colors">
                <i class="fas fa-fire mr-2 text-orange-500"></i>
                Phim hot nhất
            </a>
            <a href="{{ route('search', ['sort' => 'rating', 'order' => 'desc']) }}"
               class="block px-3 py-2 text-sm text-theme-primary hover:bg-theme-button-hover rounded-lg transition-colors">
                <i class="fas fa-star mr-2 text-yellow-500"></i>
                Đánh giá cao
            </a>
            <a href="{{ route('search', ['sort' => 'created_at', 'order' => 'desc']) }}"
               class="block px-3 py-2 text-sm text-theme-primary hover:bg-theme-button-hover rounded-lg transition-colors">
                <i class="fas fa-clock mr-2 text-blue-500"></i>
                Mới cập nhật
            </a>
            <a href="{{ route('search', ['year' => date('Y')]) }}"
               class="block px-3 py-2 text-sm text-theme-primary hover:bg-theme-button-hover rounded-lg transition-colors">
                <i class="fas fa-calendar mr-2 text-green-500"></i>
                Phim {{ date('Y') }}
            </a>
        </div>
    </div>
</div>

<script>
    function toggleMoreCategories() {
        const moreCategories = document.getElementById('more-categories');
        const button = event.target;

        if (moreCategories.classList.contains('hidden')) {
            moreCategories.classList.remove('hidden');
            button.innerHTML = '<i class="fas fa-chevron-up mr-1"></i>Thu gọn';
        } else {
            moreCategories.classList.add('hidden');
            button.innerHTML = '<i class="fas fa-chevron-down mr-1"></i>Xem thêm';
        }
    }

    function toggleMoreRegions() {
        const moreRegions = document.getElementById('more-regions');
        const button = event.target;

        if (moreRegions.classList.contains('hidden')) {
            moreRegions.classList.remove('hidden');
            button.innerHTML = '<i class="fas fa-chevron-up mr-1"></i>Thu gọn';
        } else {
            moreRegions.classList.add('hidden');
            button.innerHTML = '<i class="fas fa-chevron-down mr-1"></i>Xem thêm';
        }
    }
</script>
