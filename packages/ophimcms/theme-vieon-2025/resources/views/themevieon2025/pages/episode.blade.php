@extends('themevieon2025::layouts.app')

@section('title', $episode->name . ' - ' . $movie->name)
@section('description', 'Xem ' . $episode->name . ' của phim ' . $movie->name . ' - ' . $movie->origin_name . ' ch<PERSON><PERSON> l<PERSON> HD miễn phí.')

@section('head')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('content')
    <div class="min-h-screen bg-theme-primary">
        {{-- Breadcrumb --}}
        @include('themevieon2025::components.breadcrumb', [
            'items' => [
                [
                    'label' => $movie->name,
                    'url' => route('movies.show', $movie->slug),
                    'icon' => 'fas fa-film'
                ],
                [
                    'label' => $episode->name,
                    'icon' => 'fas fa-play-circle'
                ]
            ]
        ])

        <div class="container mx-auto px-4 py-6">
            <!-- Movie Header -->
            <div class="mb-6">
                <h1 class="text-2xl md:text-3xl font-bold text-theme-primary mb-2">{{ $movie->name }}</h1>
                <p class="text-theme-secondary text-lg">{{ $movie->origin_name }}</p>
                <div class="flex items-center gap-4 mt-2 text-sm text-theme-tertiary">
                    <span>{{ $movie->publish_year }}</span>
                    <span>•</span>
                    <span>{{ $movie->quality }}</span>
                    <span>•</span>
                    <span>{{ $episode->name }}</span>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Main Content -->
                <div class="lg:col-span-3">
                    <!-- Video Player -->
                    <div class="bg-theme-secondary rounded-lg overflow-hidden mb-6">
                        <div class="relative aspect-video bg-black">
                            <iframe
                                src="{{ $episode->link }}"
                                frameborder="0"
                                allowfullscreen
                                class="w-full h-full"
                                data-video-player>
                            </iframe>

                            <!-- Loading Overlay -->
                            <div
                                class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center loading-overlay hidden">
                                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-main"></div>
                            </div>
                        </div>

                        <!-- Player Controls -->
                        <div class="p-4 bg-theme-dropdown">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-4">
                                    <button
                                        onclick="toggleLights()"
                                        class="flex items-center gap-2 px-3 py-2 bg-theme-button hover:bg-theme-button-hover rounded-lg transition-colors">
                                        <i class="fas fa-lightbulb"></i>
                                        <span>Tắt đèn</span>
                                    </button>

                                    <button
                                        onclick="EpisodeReporting.openModal()"
                                        class="flex items-center gap-2 px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                                        <i class="fas fa-flag"></i>
                                        <span>Báo lỗi</span>
                                    </button>
                                </div>

                                <div class="flex items-center gap-2 text-sm text-theme-tertiary">
                                    <i class="fas fa-eye"></i>
                                    <span>{{ number_format($movie->view_total) }} lượt xem</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Episode Navigation -->
                    <div class="bg-theme-secondary rounded-lg p-4 mb-6">
                        <div class="flex items-center justify-between">
                            @if($prevEpisode)
                                <a href="{{ route('episodes.show', [$movie->slug, $prevEpisode->slug]) }}"
                                   class="flex items-center gap-2 px-4 py-2 bg-theme-button hover:bg-theme-button-hover rounded-lg transition-colors"
                                   data-episode-link>
                                    <i class="fas fa-chevron-left"></i>
                                    <span>{{ $prevEpisode->name }}</span>
                                </a>
                            @else
                                <div></div>
                            @endif

                            <div class="text-center">
                                <h2 class="text-lg font-semibold text-theme-primary">{{ $episode->name }}</h2>
                                <p class="text-sm text-theme-tertiary">{{ $movie->name }}</p>
                            </div>

                            @if($nextEpisode)
                                <a href="{{ route('episodes.show', [$movie->slug, $nextEpisode->slug]) }}"
                                   class="flex items-center gap-2 px-4 py-2 bg-main hover:bg-green-600 text-white rounded-lg transition-colors"
                                   data-episode-link>
                                    <span>{{ $nextEpisode->name }}</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            @else
                                <div></div>
                            @endif
                        </div>
                    </div>

                    <!-- Episode List -->
                    <div class="bg-theme-secondary rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-theme-primary mb-4">Danh sách tập</h3>
                        <div class="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-10 gap-2">
                            @foreach($movie->episodes->sortBy('order') as $ep)
                                <a href="{{ route('episodes.show', [$movie->slug, $ep->slug]) }}"
                                   class="flex items-center justify-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 {{ $ep->id === $episode->id ? 'bg-main text-white' : 'bg-theme-button hover:bg-theme-button-hover text-theme-primary' }}"
                                   data-episode-link>
                                    {{ $ep->name }}
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Movie Info -->
                    <div class="bg-theme-secondary rounded-lg p-4 mb-6">
                        <img src="{{ $movie->poster_url }}" alt="{{ $movie->name }}" class="w-full rounded-lg mb-4">

                        <h3 class="font-semibold text-theme-primary mb-2">{{ $movie->name }}</h3>
                        <p class="text-sm text-theme-tertiary mb-3">{{ $movie->origin_name }}</p>

                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-theme-tertiary">Năm:</span>
                                <span class="text-theme-primary">{{ $movie->publish_year }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-theme-tertiary">Chất lượng:</span>
                                <span class="text-theme-primary">{{ $movie->quality }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-theme-tertiary">Thời lượng:</span>
                                <span class="text-theme-primary">{{ $movie->time ?? 'N/A' }}</span>
                            </div>
                            @if($movie->rating_star > 0)
                                <div class="flex justify-between">
                                    <span class="text-theme-tertiary">Đánh giá:</span>
                                    <div class="flex items-center gap-1">
                                        <div class="flex text-yellow-400 text-xs">
                                            @for($i = 1; $i <= 5; $i++)
                                                @if($i <= $movie->rating_star)
                                                    <i class="fas fa-star"></i>
                                                @else
                                                    <i class="far fa-star"></i>
                                                @endif
                                            @endfor
                                        </div>
                                        <span
                                            class="text-theme-primary text-xs">{{ number_format($movie->rating_star, 1) }}</span>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div class="mt-4 pt-4 border-t border-theme-primary">
                            <a href="{{ route('movies.show', $movie->slug) }}"
                               class="block w-full text-center px-4 py-2 bg-main hover:bg-green-600 text-white rounded-lg transition-colors">
                                Xem thông tin phim
                            </a>
                        </div>
                    </div>

                    <!-- Categories -->
                    @if($movie->categories->count() > 0)
                        <div class="bg-theme-secondary rounded-lg p-4 mb-6">
                            <h3 class="font-semibold text-theme-primary mb-3">Thể loại</h3>
                            <div class="flex flex-wrap gap-2">
                                @foreach($movie->categories as $category)
                                    <a href="{{ route('categories.movies.index', $category->slug) }}"
                                       class="px-2 py-1 bg-theme-button hover:bg-theme-button-hover text-xs rounded-lg transition-colors">
                                        {{ $category->name }}
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        {{-- Comments Section --}}
        <div id="comments" class="mt-8">
            @include('themevieon2025::components.comment-section', [
                'movie' => $movie,
                'episode' => $episode,
                'comments' => $comments
            ])
        </div>
    </div>

    <!-- Report Modal -->
    @include('themevieon2025::components.report-modal', ['movie' => $movie, 'episode' => $episode])

    <!-- Light Box Overlay -->
    <div id="lightbox-overlay" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden"></div>

    <!-- Include Report Modal -->
    @include('themevieon2025::components.report-modal-vanilla', ['movie' => $movie, 'episode' => $episode])

@endsection

@push('scripts')
    <script src="{{ asset('themes/vieon-2025/js/episode-reporting.js') }}"></script>
    <script>
        // Toggle lights functionality
        function toggleLights() {
            const overlay = document.getElementById('lightbox-overlay');
            const button = event.target.closest('button');
            const icon = button.querySelector('i');
            const text = button.querySelector('span');

            if (overlay.classList.contains('hidden')) {
                overlay.classList.remove('hidden');
                icon.className = 'fas fa-lightbulb-on';
                text.textContent = 'Bật đèn';
            } else {
                overlay.classList.add('hidden');
                icon.className = 'fas fa-lightbulb';
                text.textContent = 'Tắt đèn';
            }
        }

        // Show report modal (backward compatibility)
        function showReportModal() {
            if (window.EpisodeReporting) {
                window.EpisodeReporting.openModal();
            } else {
                console.warn('EpisodeReporting not loaded');
            }
        }

        // Auto-play next episode (optional)
        document.addEventListener('DOMContentLoaded', function () {
            const videoPlayer = document.querySelector('[data-video-player]');
            if (videoPlayer) {
                // Add any video player event listeners here
            }
        });
    </script>
@endpush
