@if ($paginator->hasPages())
    <nav class="pagination-wrapper" role="navigation" aria-label="Pagination Navigation">
        <div class="flex items-center justify-between">
            <!-- Previous Page Link -->
            @if ($paginator->onFirstPage())
                <span class="pagination-btn pagination-btn-disabled">
                    <i class="fas fa-chevron-left"></i>
                    <span class="hidden sm:inline ml-1">Trước</span>
                </span>
            @else
                <a href="{{ $paginator->previousPageUrl() }}" class="pagination-btn pagination-btn-active" rel="prev">
                    <i class="fas fa-chevron-left"></i>
                    <span class="hidden sm:inline ml-1">Trước</span>
                </a>
            @endif

            <!-- Pagination Elements -->
            <div class="flex items-center space-x-1">
                @foreach ($elements as $element)
                    {{-- "Three Dots" Separator --}}
                    @if (is_string($element))
                        <span class="pagination-dots">{{ $element }}</span>
                    @endif

                    {{-- Array Of Links --}}
                    @if (is_array($element))
                        @foreach ($element as $page => $url)
                            @if ($page == $paginator->currentPage())
                                <span class="pagination-number pagination-number-active"
                                      aria-current="page">{{ $page }}</span>
                            @else
                                <a href="{{ $url }}" class="pagination-number">{{ $page }}</a>
                            @endif
                        @endforeach
                    @endif
                @endforeach
            </div>

            <!-- Next Page Link -->
            @if ($paginator->hasMorePages())
                <a href="{{ $paginator->nextPageUrl() }}" class="pagination-btn pagination-btn-active" rel="next">
                    <span class="hidden sm:inline mr-1">Sau</span>
                    <i class="fas fa-chevron-right"></i>
                </a>
            @else
                <span class="pagination-btn pagination-btn-disabled">
                    <span class="hidden sm:inline mr-1">Sau</span>
                    <i class="fas fa-chevron-right"></i>
                </span>
            @endif
        </div>

        <!-- Page Info -->
        <div class="pagination-info mt-3 text-center">
            <p class="text-sm text-theme-tertiary">
                Hiển thị {{ $paginator->firstItem() ?? 0 }} đến {{ $paginator->lastItem() ?? 0 }}
                trong tổng số {{ $paginator->total() }} kết quả
            </p>
        </div>
    </nav>

    <style>
        .pagination-wrapper {
            @apply mt-6 mb-4;
        }

        .pagination-btn {
            @apply inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200;
        }

        .pagination-btn-active {
            @apply bg-theme-dropdown text-theme-primary border border-theme-primary hover:bg-main hover:text-white hover:border-main;
        }

        .pagination-btn-disabled {
            @apply bg-theme-secondary text-theme-tertiary border border-theme-primary cursor-not-allowed opacity-50;
        }

        .pagination-number {
            @apply inline-flex items-center justify-center w-10 h-10 text-sm font-medium rounded-lg transition-all duration-200;
            @apply bg-theme-dropdown text-theme-secondary border border-theme-primary hover:bg-main hover:text-white hover:border-main;
        }

        .pagination-number-active {
            @apply inline-flex items-center justify-center w-10 h-10 text-sm font-medium rounded-lg;
            @apply bg-main text-white border border-main;
        }

        .pagination-dots {
            @apply inline-flex items-center justify-center w-10 h-10 text-sm font-medium text-theme-tertiary;
        }

        .pagination-info {
            @apply text-center mt-3;
        }

        /* Dark mode adjustments */
        @media (prefers-color-scheme: dark) {
            .pagination-btn-active {
                @apply hover:bg-main/90;
            }

            .pagination-number {
                @apply hover:bg-main/90;
            }
        }

        /* Mobile responsive */
        @media (max-width: 640px) {
            .pagination-number {
                @apply w-8 h-8 text-xs;
            }

            .pagination-btn {
                @apply px-2 py-1 text-xs;
            }
        }
    </style>
@endif
