/* Comment System Styles */

/* Comment Section */
.comment-section {
    background: var(--bg-secondary);
    border-radius: 0.5rem;
    padding: 1.5rem;
}

/* Comment Form */
.comment-form {
    margin-bottom: 2rem;
}

.comment-form textarea {
    background: var(--bg-primary);
    border: 1px solid var(--border-input);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.comment-form textarea:focus {
    outline: none;
    border-color: var(--main-color);
    box-shadow: 0 0 0 2px rgba(0, 255, 102, 0.2);
}

.comment-form textarea::placeholder {
    color: var(--text-tertiary);
}

/* Comment Items */
.comment-item {
    transition: all 0.3s ease;
}

.comment-item:hover {
    transform: translateY(-1px);
}

.comment-item .comment-content {
    background: var(--bg-primary);
    border-radius: 0.5rem;
    padding: 1rem;
    position: relative;
}

.comment-item .comment-content::before {
    content: '';
    position: absolute;
    top: 1rem;
    left: -0.5rem;
    width: 0;
    height: 0;
    border-top: 0.5rem solid transparent;
    border-bottom: 0.5rem solid transparent;
    border-right: 0.5rem solid var(--bg-primary);
}

/* Comment Actions */
.comment-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 0.5rem;
}

.comment-actions button {
    background: none;
    border: none;
    color: var(--text-tertiary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.comment-actions button:hover {
    color: var(--main-color);
}

.comment-actions .delete-btn:hover {
    color: #ef4444;
}

/* Reply Form */
.reply-form {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 0.5rem;
    border: 1px solid var(--border-input);
}

.reply-form textarea {
    background: var(--bg-primary);
    border: 1px solid var(--border-input);
    color: var(--text-primary);
    font-size: 0.875rem;
}

.reply-form textarea:focus {
    outline: none;
    border-color: var(--main-color);
    box-shadow: 0 0 0 2px rgba(0, 255, 102, 0.2);
}

/* Comment Nesting */
.comment-item.ml-8 {
    margin-left: 2rem;
}

.comment-item.ml-16 {
    margin-left: 4rem;
}

.comment-item.ml-24 {
    margin-left: 6rem;
}

/* Comment Meta */
.comment-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.comment-author {
    font-weight: 600;
    color: var(--text-primary);
}

.comment-time {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

.comment-episode-badge {
    background: var(--main-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
}

/* Comment Text */
.comment-text {
    color: var(--text-secondary);
    line-height: 1.6;
    word-wrap: break-word;
}

/* Avatar */
.comment-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

/* Empty State */
.comments-empty {
    text-align: center;
    padding: 3rem 1rem;
}

.comments-empty-icon {
    width: 6rem;
    height: 6rem;
    margin: 0 auto 1.5rem;
    background: var(--bg-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.comments-empty-icon svg {
    width: 3rem;
    height: 3rem;
    color: var(--text-tertiary);
}

.comments-empty h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.comments-empty p {
    color: var(--text-tertiary);
}

/* Loading States */
.comment-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.comment-loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--text-tertiary);
    border-top: 2px solid var(--main-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Cross Navigation */
.comment-cross-nav {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.875rem;
}

.comment-cross-nav a {
    color: var(--text-tertiary);
    text-decoration: none;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.comment-cross-nav a:hover {
    color: var(--main-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .comment-item.ml-8,
    .comment-item.ml-16,
    .comment-item.ml-24 {
        margin-left: 1rem;
    }

    .comment-meta {
        flex-wrap: wrap;
    }

    .comment-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .comment-cross-nav {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

/* Animation */
.comment-item {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(1rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* AJAX Loading States */
.comments-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Comment Container Transitions */
#comments-container {
    transition: opacity 0.3s ease;
}

#comments-container.loading {
    opacity: 0.6;
}

/* New Comment Highlight */
.comment-item.new-comment {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    animation: highlightNew 2s ease-out;
}

@keyframes highlightNew {
    0% {
        background: rgba(34, 197, 94, 0.2);
        border-color: rgba(34, 197, 94, 0.5);
    }
    100% {
        background: rgba(34, 197, 94, 0.1);
        border-color: rgba(34, 197, 94, 0.3);
    }
}

/* Smooth Pagination */
.pagination-container {
    animation: fadeInUp 0.3s ease-out;
}

.pagination-container button {
    transition: all 0.3s ease;
}

.pagination-container button:hover {
    transform: translateY(-1px);
    background: rgba(34, 197, 94, 0.1);
}

/* Focus States */
.comment-form textarea:focus,
.reply-form textarea:focus {
    outline: none;
    border-color: var(--main-color);
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* Button Styles */
.comment-submit-btn {
    background: var(--main-color);
    color: white;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.comment-submit-btn:hover {
    background: #16a34a;
    transform: translateY(-1px);
}

.comment-submit-btn:active {
    transform: translateY(0);
}

/* Login Prompt */
.comment-login-prompt {
    background: var(--bg-primary);
    border: 1px solid var(--border-input);
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 2rem;
}

.comment-login-prompt p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.comment-login-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.comment-login-buttons a {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.comment-login-buttons .login-btn {
    background: var(--main-color);
    color: white;
}

.comment-login-buttons .login-btn:hover {
    background: #16a34a;
}

.comment-login-buttons .register-btn {
    background: var(--bg-button);
    color: var(--text-primary);
}

.comment-login-buttons .register-btn:hover {
    background: var(--bg-button-hover);
}
