<?php

namespace App\Http\Controllers\NguoncCrawler\BaseCrawler;

use Illuminate\Support\Facades\Log;
use Intervention\Image\ImageManagerStatic as Image;
use Illuminate\Support\Facades\Storage;

class CollectorIfExist
{
    protected $movie;
    protected $payload;

    public function __construct($payload, $movie)
    {
        $this->movie = $movie;
        $this->payload = $payload;
    }

    public function get(): array
    {
        $info = $this->movie;
        $newInfo = $this->payload['movie'] ?? [];

        $currentEpisodeNumber = intval(preg_replace('/\D/', '', $newInfo['episode_current']));
        $totalEpisodeNumber = intval(preg_replace('/\D/', '', $newInfo['episode_total']));
        $status = strtolower($newInfo['status']);
        if ($status == "ongoing") {
            if (is_numeric($currentEpisodeNumber) && is_numeric($totalEpisodeNumber) && $currentEpisodeNumber == $totalEpisodeNumber) {
                $status = "completed";
            } else {
                $status = "ongoing";
            }
        } elseif ($status == "trailer") {
            if (is_numeric($currentEpisodeNumber) && is_numeric($totalEpisodeNumber)) {
                if ($currentEpisodeNumber == $totalEpisodeNumber) {
                    $status = "completed";
                } elseif ($currentEpisodeNumber <= $totalEpisodeNumber) {
                    $status = "ongoing";
                }
            } else {
                $status == "trailer";
            }
        }

        $data = [
            'name' => $info['name'],
            'origin_name' => $info['origin_name'],
            'publish_year' => $info['year'] ?? $info['publish_year'] ?? null,
            'content' => $info['content'],
            'type' =>  $info['type'],
            'status' => $status,
            'thumb_url' => $info['thumb_url'],
            'poster_url' => $info['poster_url'],
            'is_copyright' => $info['is_copyright'],
            'trailer_url' => $info['trailer_url'] ?? "",
            'quality' => $info['quality'],
            'language' => $newInfo['lang'],
            'episode_time' => $info['time'],
            'episode_current' => $newInfo['episode_current'],
            'episode_total' => $info['episode_total'],
            'notify' => $info['notify'],
            'showtimes' => $info['showtimes'],
            'is_shown_in_theater' => $info['chieurap'] ?? 0,
        ];
        
        $thumb_url = $newInfo['thumb_url'];
        $poster_url = $newInfo['poster_url'];
        
        if (preg_match('/\.(jpg|jpeg|png|gif)$/i', $thumb_url)) {
            $data[] = [
                'thumb_url' => $this->getThumbImage($info['slug'], $thumb_url),
            ];
        }
        
        if (preg_match('/\.(jpg|jpeg|png|gif)$/i', $poster_url)) {
            $data[] = [
                'poster_url' => $this->getPosterImage($info['slug'], $poster_url)
            ];
        }

        return $data;
    }
    
     public function getThumbImage($slug, $url)
    {
        return $this->getImage(
            $slug,
            $url,
            Option::get('should_resize_thumb', false),
            Option::get('resize_thumb_width'),
            Option::get('resize_thumb_height')
        );
    }

    public function getPosterImage($slug, $url)
    {
        return $this->getImage(
            $slug,
            $url,
            Option::get('should_resize_poster', false),
            Option::get('resize_poster_width'),
            Option::get('resize_poster_height')
        );
    }
    
    protected function getImage($slug, string $url, $shouldResize = false, $width = null, $height = null): string
    {
        if (!Option::get('download_image', false) || empty($url)) {
            \Log::info("Slug: " . $url);
            return $url;
        }
        try {
            $url = strtok($url, '?');
            $filename = substr($url, strrpos($url, '/') + 1);
            $path = "images/{$slug}-{$filename}";

            if (Storage::disk('public')->exists($path)) {
                return Storage::url($path);
            }

            // Khởi tạo curl để tải về hình ảnh
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_HEADER, false);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_BINARYTRANSFER, 1);
            curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36");
            $image_data = curl_exec($ch);
            curl_close($ch);

            $img = Image::make($image_data);

            if ($shouldResize) {
                $img->resize($width, $height, function ($constraint) {
                    $constraint->aspectRatio();
                });
            }

            Storage::disk('public')->put($path, null);

            $img->save(storage_path("app/public/" . $path));

            return Storage::url($path);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return $url;
        }
    }
}
