{{-- Breadcrumb Component --}}
@php
    // Set default values for props
    $items = $items ?? [];
    $containerClass = $containerClass ?? 'bg-theme-secondary border-b border-theme-primary';
    $wrapperClass = $wrapperClass ?? 'container mx-auto px-4 py-3';
    $navClass = $navClass ?? 'flex text-sm text-theme-tertiary';
    $showHome = $showHome ?? true;

    // Ensure we always have home as first item if showHome is true
    $breadcrumbItems = [];

    if ($showHome) {
        $breadcrumbItems[] = [
            'label' => 'Trang chủ',
            'url' => route('home'),
            'icon' => 'fas fa-home'
        ];
    }

    // Add provided items
    foreach ($items as $item) {
        $breadcrumbItems[] = $item;
    }

    // Ensure last item is marked as active
    if (!empty($breadcrumbItems)) {
        $lastIndex = count($breadcrumbItems) - 1;
        $breadcrumbItems[$lastIndex]['active'] = true;
        // Remove URL from active item to prevent linking
        unset($breadcrumbItems[$lastIndex]['url']);
    }
@endphp

@if(!empty($breadcrumbItems))
    <div class="{{ $containerClass }}">
        <div class="{{ $wrapperClass }}">
            <nav class="{{ $navClass }}" aria-label="Breadcrumb">
                @foreach($breadcrumbItems as $index => $item)
                    @if($index > 0)
                        <span class="mx-2 text-theme-tertiary" aria-hidden="true">/</span>
                    @endif

                    @if(isset($item['active']) && $item['active'])
                        {{-- Active/Current item --}}
                        <span class="text-main font-medium" aria-current="page">
                            @if(isset($item['icon']))
                                <i class="{{ $item['icon'] }} mr-1" aria-hidden="true"></i>
                            @endif
                            {{ $item['label'] }}
                        </span>
                    @else
                        {{-- Linked item --}}
                        <a href="{{ $item['url'] ?? '#' }}"
                           class="hover:text-main transition-colors duration-200 flex items-center"
                           @if($index === 0) title="Về trang chủ" @endif>
                            @if(isset($item['icon']))
                                <i class="{{ $item['icon'] }} mr-1" aria-hidden="true"></i>
                            @endif
                            {{ $item['label'] }}
                        </a>
                    @endif
                @endforeach
            </nav>
        </div>
    </div>
@endif

{{-- Structured Data for SEO --}}
@if(!empty($breadcrumbItems))
    @push('head')
    <script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
        @foreach($breadcrumbItems as $index => $item)
        {
            "@type": "ListItem",
            "position": {{ $index + 1 }},
            "name": {{ json_encode($item['label']) }},
            @if(isset($item['url']))
            "item": {{ json_encode($item['url']) }}
            @else
            "item": {{ json_encode(request()->url()) }}
            @endif
        }@if($index < count($breadcrumbItems) - 1),@endif
        @endforeach
    ]
}
    </script>
    @endpush
@endif

{{-- CSS for enhanced styling --}}
@push('styles')
<style>
.breadcrumb-container {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.breadcrumb-nav a:hover {
    text-shadow: 0 0 8px var(--main-color);
}

.breadcrumb-separator {
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.breadcrumb-nav:hover .breadcrumb-separator {
    opacity: 1;
}

/* Mobile responsive */
@media (max-width: 640px) {
    .breadcrumb-nav {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .breadcrumb-nav .mx-2 {
        margin: 0 0.25rem;
    }
}

/* Accessibility improvements */
.breadcrumb-nav a:focus {
    outline: 2px solid var(--main-color);
    outline-offset: 2px;
    border-radius: 2px;
}

/* Animation for breadcrumb items */
.breadcrumb-nav > * {
    animation: breadcrumbFadeIn 0.3s ease-out;
}

@keyframes breadcrumbFadeIn {
    from {
        opacity: 0;
        transform: translateY(-4px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
@endpush
