@extends('themevieon2025::layouts.user')

@section('title', 'Cài đặt - ' . config('app.name') . ' 2025')

@section('user-content')
    <div class="max-w-2xl mx-auto">
        <div class="text-center mb-8">
            <div class="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-main/20 mb-4">
                <svg class="h-10 w-10 text-main" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </div>
            <h2 class="text-3xl font-bold text-white mb-2">Cài đặt tài khoản</h2>
            <p class="text-gray-400">Tùy chỉnh trải nghiệm xem phim của bạn</p>
        </div>

        {{-- Loading Overlay --}}
        <div id="settings-loading"
             class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
            <div class="bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-main"></div>
                <span class="text-white">Đang cập nhật...</span>
            </div>
        </div>

        {{-- Message Containers --}}
        <div id="settings-error"
             class="hidden mb-6 bg-red-500 bg-opacity-20 border border-red-500 text-red-200 px-4 py-3 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                          clip-rule="evenodd"></path>
                </svg>
                <span id="settings-error-message"></span>
            </div>
        </div>

        <div id="settings-success"
             class="hidden mb-6 bg-green-500 bg-opacity-20 border border-green-500 text-green-200 px-4 py-3 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clip-rule="evenodd"></path>
                </svg>
                <span id="settings-success-message"></span>
            </div>
        </div>

        <form id="settings-form" class="space-y-6">
            @csrf

            <!-- Language Settings -->
            <div>
                <label class="block text-sm font-medium text-theme-secondary mb-2">Ngôn ngữ giao diện</label>
                <select name="language"
                        class="w-full px-4 py-3 bg-theme-tertiary border border-theme-input rounded-lg text-theme-primary focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all">
                    <option value="vi" {{ old('language', $user->language) === 'vi' ? 'selected' : '' }}>Tiếng Việt
                    </option>
                    <option value="en" {{ old('language', $user->language) === 'en' ? 'selected' : '' }}>English
                    </option>
                </select>
            </div>

            <!-- Subtitle Settings -->
            <div>
                <label class="block text-sm font-medium text-theme-secondary mb-2">Phụ đề mặc định</label>
                <select name="subtitle"
                        class="w-full px-4 py-3 bg-theme-tertiary border border-theme-input rounded-lg text-theme-primary placeholder-theme-muted focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all">
                    <option value="vi" {{ old('subtitle', $user->subtitle) === 'vi' ? 'selected' : '' }}>Tiếng Việt
                    </option>
                    <option value="en" {{ old('subtitle', $user->subtitle) === 'en' ? 'selected' : '' }}>English
                    </option>
                    <option value="off" {{ old('subtitle', $user->subtitle) === 'off' ? 'selected' : '' }}>Tắt</option>
                </select>
            </div>

            <button type="submit"
                    class="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-theme-inverse bg-main hover:bg-main/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main transition-all duration-200 transform hover:scale-105">
                Lưu cài đặt
            </button>
        </form>

        <!-- Advanced Settings -->
        <div class="mt-8">
            <h3 class="text-lg font-semibold text-theme-primary mb-4">Cài đặt nâng cao</h3>

            <form id="advanced-settings-form" class="space-y-4">
                @csrf
                <input type="hidden" name="advanced_settings" value="1">
                <!-- Hidden fields to satisfy validation -->
                <input type="hidden" name="language" value="{{ old('language', $user->language ?? 'vi') }}">
                <input type="hidden" name="subtitle" value="{{ old('subtitle', $user->subtitle ?? 'vi') }}">

                <!-- Auto Play Next Episode -->
                <div class="flex items-center justify-between p-4 bg-theme-tertiary rounded-lg">
                    <div>
                        <div class="font-medium text-theme-primary">Tự động phát tập tiếp theo</div>
                        <div class="text-sm text-theme-tertiary">Tự động chuyển sang tập tiếp theo khi kết thúc</div>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="auto_play_next" value="1"
                               class="sr-only peer" {{ old('auto_play_next', $user->auto_play_next) ? 'checked' : '' }}>
                        <div
                            class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-main/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-main shadow-inner"></div>
                    </label>
                </div>

                <!-- Save Watch History -->
                <div class="flex items-center justify-between p-4 bg-theme-tertiary rounded-lg">
                    <div>
                        <div class="font-medium text-theme-primary">Lưu lịch sử xem phim</div>
                        <div class="text-sm text-theme-tertiary">Tự động lưu vị trí xem phim để tiếp tục sau</div>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="save_watch_history" value="1"
                               class="sr-only peer" {{ old('save_watch_history', $user->save_watch_history) ? 'checked' : '' }}>
                        <div
                            class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-main/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-main shadow-inner"></div>
                    </label>
                </div>

                <!-- Notifications -->
                <div class="flex items-center justify-between p-4 bg-theme-tertiary rounded-lg">
                    <div>
                        <div class="font-medium text-theme-primary">Thông báo phim mới</div>
                        <div class="text-sm text-theme-tertiary">Nhận thông báo khi có phim mới phát hành</div>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" name="notify_new_movie" value="1"
                               class="sr-only peer" {{ old('notify_new_movie', $user->notify_new_movie) ? 'checked' : '' }}>
                        <div
                            class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-main/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-main shadow-inner"></div>
                    </label>
                </div>

                <button type="submit"
                        class="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-theme-inverse bg-main hover:bg-main/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main transition-all duration-200 transform hover:scale-105">
                    Lưu cài đặt nâng cao
                </button>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('settings-form');
            const advancedForm = document.getElementById('advanced-settings-form');

            // Handle main settings form
            if (form) {
                form.addEventListener('submit', async function (e) {
                    e.preventDefault();
                    await handleFormSubmit(form, 'Cài đặt đã được cập nhật thành công');
                });

                // Sync language and subtitle changes to advanced form
                const languageSelect = form.querySelector('select[name="language"]');
                const subtitleSelect = form.querySelector('select[name="subtitle"]');

                if (languageSelect && advancedForm) {
                    languageSelect.addEventListener('change', function () {
                        const hiddenLanguage = advancedForm.querySelector('input[name="language"]');
                        if (hiddenLanguage) {
                            hiddenLanguage.value = this.value;
                        }
                    });
                }

                if (subtitleSelect && advancedForm) {
                    subtitleSelect.addEventListener('change', function () {
                        const hiddenSubtitle = advancedForm.querySelector('input[name="subtitle"]');
                        if (hiddenSubtitle) {
                            hiddenSubtitle.value = this.value;
                        }
                    });
                }
            }

            // Handle advanced settings form
            if (advancedForm) {
                advancedForm.addEventListener('submit', async function (e) {
                    e.preventDefault();
                    await handleFormSubmit(advancedForm, 'Cài đặt nâng cao đã được cập nhật thành công');
                });
            }
        });

        async function handleFormSubmit(form, successMessage) {
            // Hide previous messages
            hideMessage();

            // Show loading
            showLoading(true);

            // Get form data
            const formData = new FormData(form);
            const settings = {};

            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                if (form.querySelector(`input[name="${key}"][type="checkbox"]`)) {
                    // Handle checkboxes - only include if checked
                    settings[key] = value;
                } else {
                    settings[key] = value;
                }
            }

            // Handle unchecked checkboxes
            const checkboxes = form.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                if (!checkbox.checked) {
                    settings[checkbox.name] = '0';
                }
            });

            try {
                // Update settings via API
                const response = await window.userAPI.updateSettings(settings);

                // If we reach here, the request was successful (no exception thrown)
                console.log('Settings update response:', response);
                showSuccess(response.message || successMessage);

            } catch (error) {
                console.error('Error updating settings:', error);

                // Parse error message from response
                let errorMessage = 'Có lỗi xảy ra khi cập nhật cài đặt';

                if (error.message) {
                    try {
                        // Try to parse JSON error response
                        const errorData = JSON.parse(error.message);
                        if (errorData.errors) {
                            // Format validation errors
                            const errorList = [];
                            for (const field in errorData.errors) {
                                errorList.push(...errorData.errors[field]);
                            }
                            errorMessage = errorList.join(', ');
                        } else if (errorData.message) {
                            errorMessage = errorData.message;
                        }
                    } catch (parseError) {
                        // If not JSON, use the original message
                        errorMessage = error.message;
                    }
                }

                showError(errorMessage);
            } finally {
                showLoading(false);
            }
        }

        function showLoading(show) {
            const loadingEl = document.getElementById('settings-loading');
            if (show) {
                loadingEl.classList.remove('hidden');
            } else {
                loadingEl.classList.add('hidden');
            }
        }

        function showError(message) {
            console.log('Showing error message:', message);
            const errorEl = document.getElementById('settings-error');
            const messageEl = document.getElementById('settings-error-message');

            if (!errorEl || !messageEl) {
                console.error('Error elements not found');
                return;
            }

            messageEl.textContent = message;
            errorEl.classList.remove('hidden');

            // Auto hide after 5 seconds
            setTimeout(() => {
                errorEl.classList.add('hidden');
            }, 5000);
        }

        function showSuccess(message) {
            console.log('Showing success message:', message);
            const successEl = document.getElementById('settings-success');
            const messageEl = document.getElementById('settings-success-message');

            if (!successEl || !messageEl) {
                console.error('Success elements not found');
                return;
            }

            messageEl.textContent = message;
            successEl.classList.remove('hidden');

            // Auto hide after 3 seconds
            setTimeout(() => {
                successEl.classList.add('hidden');
            }, 3000);
        }

        function hideMessage() {
            document.getElementById('settings-error').classList.add('hidden');
            document.getElementById('settings-success').classList.add('hidden');
        }
    </script>
@endpush
