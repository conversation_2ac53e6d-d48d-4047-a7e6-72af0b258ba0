@extends('backpack::layout')

@section('title', config('app.name') . ' 2025 Dashboard')

@section('content')
    <div class="row">
        <div class="col-sm-6 col-lg-3">
            <div class="card text-white bg-primary mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-value-lg">{{ number_format($count_movies) }}</div>
                            <div class="text-uppercase text-muted small">Phim</div>
                        </div>
                        <div class="text-value-lg">
                            <i class="fas fa-film"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3">
            <div class="card text-white bg-success mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-value-lg">{{ number_format($count_episodes) }}</div>
                            <div class="text-uppercase text-muted small">Tập phim</div>
                        </div>
                        <div class="text-value-lg">
                            <i class="fas fa-play-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3">
            <div class="card text-white bg-warning mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-value-lg">{{ number_format($count_users) }}</div>
                            <div class="text-uppercase text-muted small">Người dùng</div>
                        </div>
                        <div class="text-value-lg">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3">
            <div class="card text-white bg-info mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-value-lg">{{ number_format($count_themes) }}</div>
                            <div class="text-uppercase text-muted small">Giao diện</div>
                        </div>
                        <div class="text-value-lg">
                            <i class="fas fa-palette"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-sm-6 col-lg-3">
            <div class="card text-white bg-secondary mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-value-lg">{{ number_format($count_categories) }}</div>
                            <div class="text-uppercase text-muted small">Thể loại</div>
                        </div>
                        <div class="text-value-lg">
                            <i class="fas fa-tags"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3">
            <div class="card text-white bg-dark mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-value-lg">{{ number_format($count_regions) }}</div>
                            <div class="text-uppercase text-muted small">Quốc gia</div>
                        </div>
                        <div class="text-value-lg">
                            <i class="fas fa-globe"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3">
            <div class="card text-white bg-danger mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-value-lg">{{ number_format($count_actors) }}</div>
                            <div class="text-uppercase text-muted small">Diễn viên</div>
                        </div>
                        <div class="text-value-lg">
                            <i class="fas fa-user-tie"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-lg-3">
            <div class="card text-white bg-primary mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="text-value-lg">{{ number_format($count_directors) }}</div>
                            <div class="text-uppercase text-muted small">Đạo diễn</div>
                        </div>
                        <div class="text-value-lg">
                            <i class="fas fa-video"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Phim xem nhiều nhất hôm nay</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                            <tr>
                                <th>Tên phim</th>
                                <th>Lượt xem</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($top_view_day as $movie)
                                <tr>
                                    <td>{{ $movie->name }}</td>
                                    <td>{{ number_format($movie->view_day) }}</td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Phim xem nhiều nhất tuần</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                            <tr>
                                <th>Tên phim</th>
                                <th>Lượt xem</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($top_view_week as $movie)
                                <tr>
                                    <td>{{ $movie->name }}</td>
                                    <td>{{ number_format($movie->view_week) }}</td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Phim mới nhất</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                            <tr>
                                <th>Tên phim</th>
                                <th>Ngày tạo</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($recent_movies as $movie)
                                <tr>
                                    <td>{{ $movie->name }}</td>
                                    <td>{{ $movie->created_at->format('d/m/Y') }}</td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Người dùng mới nhất</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                            <tr>
                                <th>Tên</th>
                                <th>Email</th>
                                <th>Ngày đăng ký</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($recent_users as $user)
                                <tr>
                                    <td>{{ $user->name }}</td>
                                    <td>{{ $user->email }}</td>
                                    <td>{{ $user->created_at->format('d/m/Y') }}</td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
