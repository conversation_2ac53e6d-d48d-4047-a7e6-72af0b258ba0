<?php

namespace Ophim\ThemeVieon2025\Controllers\User\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Ophim\ThemeVieon2025\Models\Comment;

class ApiCommentController extends Controller
{
    public function apiIndex($movieId)
    {
        $comments = Comment::where('movie_id', $movieId)
            ->whereNull('episode_id')
            ->whereNull('parent_id')
            ->with(['children.user', 'user', 'episode'])
            ->latest()
            ->paginate(10);

        return response()->json([
            'status' => true,
            'data' => $comments
        ]);
    }

    public function apiStore(Request $request, $movieId)
    {
        $data = $request->validate([
            'content' => 'required|string',
            'parent_id' => 'nullable|exists:comments,id',
            'episode_id' => 'nullable|exists:episodes,id',
        ]);

        $data['user_id'] = Auth::id();
        $data['movie_id'] = $movieId;

        $comment = Comment::create($data);

        return response()->json([
            'status' => true,
            'message' => 'Bình luận thành công!',
            'data' => $comment->load(['user', 'episode', 'children.user'])
        ]);
    }

    public function apiDestroy($id)
    {
        $comment = Comment::findOrFail($id);

        if ($comment->user_id !== Auth::id()) {
            return response()->json([
                'status' => false,
                'message' => 'Bạn không có quyền xoá bình luận này!'
            ], 403);
        }

        $comment->delete();

        return response()->json([
            'status' => true,
            'message' => 'Đã xoá bình luận!',
            'removeSelector' => '.comment-item-' . $id
        ]);
    }

    /**
     * Get episode-specific comments
     */
    public function apiEpisodeIndex($movieId, $episodeId)
    {
        $comments = Comment::where('movie_id', $movieId)
            ->where('episode_id', $episodeId)
            ->whereNull('parent_id')
            ->with(['children.user', 'user', 'episode'])
            ->latest()
            ->paginate(10);

        return response()->json([
            'status' => true,
            'data' => $comments
        ]);
    }

    /**
     * Store episode-specific comment
     */
    public function apiEpisodeStore(Request $request, $movieId, $episodeId)
    {
        $data = $request->validate([
            'content' => 'required|string',
            'parent_id' => 'nullable|exists:comments,id',
        ]);

        $data['user_id'] = Auth::id();
        $data['movie_id'] = $movieId;
        $data['episode_id'] = $episodeId;

        $comment = Comment::create($data);

        return response()->json([
            'status' => true,
            'message' => 'Bình luận thành công!',
            'data' => $comment->load(['user', 'episode', 'children.user'])
        ]);
    }
}
