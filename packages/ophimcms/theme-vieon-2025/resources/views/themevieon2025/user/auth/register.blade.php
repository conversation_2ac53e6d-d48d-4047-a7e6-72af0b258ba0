@extends('themevieon2025::layouts.app')

@section('title', 'Đăng ký - ' . config('app.name') . ' 2025')

@section('content')
    <div class="auth-page-container bg-gradient-to-br from-main/10 to-main/5">
        <div class="user-form-container">
            <div class="text-center mb-8">
                <div class="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-main/20 mb-4">
                    <svg class="h-10 w-10 text-main" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-extrabold text-theme-primary mb-2">
                    Đ<PERSON><PERSON> ký tài khoản {{ config('app.name') }}
                </h2>
                <p class="text-theme-tertiary">
                    Hoặc
                    <a href="{{ route('login') }}" class="font-medium text-main hover:text-main/80 transition-colors">
                        đăng nhập nếu đã có tài khoản
                    </a>
                </p>
            </div>

            <form class="space-y-6 ajax-form" action="{{ route('api.register') }}" method="POST">
                @csrf
                <div class="space-y-4">
                    <div>
                        <label for="name" class="block text-sm font-medium text-theme-secondary mb-2">Họ tên</label>
                        <input id="name" name="name" type="text" autocomplete="name" required
                               class="w-full px-4 py-3 bg-theme-tertiary border border-theme-input rounded-lg text-theme-primary placeholder-theme-muted focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all"
                               placeholder="Nhập họ tên của bạn" value="{{ old('name') }}">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-theme-secondary mb-2">Email</label>
                        <input id="email" name="email" type="email" autocomplete="email" required
                               class="w-full px-4 py-3 bg-theme-tertiary border border-theme-input rounded-lg text-theme-primary placeholder-theme-muted focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all"
                               placeholder="Nhập email của bạn" value="{{ old('email') }}">
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-theme-secondary mb-2">Mật
                            khẩu</label>
                        <input id="password" name="password" type="password" autocomplete="new-password" required
                               class="w-full px-4 py-3 bg-theme-tertiary border border-theme-input rounded-lg text-theme-primary placeholder-theme-muted focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all"
                               placeholder="Tạo mật khẩu mới">
                    </div>
                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-theme-secondary mb-2">Xác
                            nhận mật khẩu</label>
                        <input id="password_confirmation" name="password_confirmation" type="password"
                               autocomplete="new-password" required
                               class="w-full px-4 py-3 bg-theme-tertiary border border-theme-input rounded-lg text-theme-primary placeholder-theme-muted focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all"
                               placeholder="Nhập lại mật khẩu">
                    </div>
                </div>

                <button type="submit"
                        class="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-black bg-main hover:bg-main/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main transition-all duration-200 transform hover:scale-105">
                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    Đăng ký
                </button>

                <div class="text-center">
                    <p class="text-xs text-theme-muted">
                        Bằng cách đăng ký, bạn đồng ý với
                        <a href="#" class="text-main hover:text-main/80 transition-colors">Điều khoản sử dụng</a>
                        và
                        <a href="#" class="text-main hover:text-main/80 transition-colors">Chính sách bảo mật</a>
                    </p>
                </div>
            </form>
        </div>
    </div>
@endsection
