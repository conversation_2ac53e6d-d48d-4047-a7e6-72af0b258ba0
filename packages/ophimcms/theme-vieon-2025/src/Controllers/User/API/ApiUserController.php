<?php

namespace Ophim\ThemeVieon2025\Controllers\User\API;

use App\Http\Controllers\Controller;
use App\Models\Movie;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Ophim\ThemeVieon2025\Models\Favorite;
use Ophim\ThemeVieon2025\Models\History;
use Ophim\ThemeVieon2025\Models\Rating;

class ApiUserController extends Controller
{
    public function apiDashboard()
    {
        $user = Auth::user();

        // Get user statistics
        $favoritesCount = Favorite::where('user_id', $user->id)->count();
        $historyCount = History::where('user_id', $user->id)->count();
        $ratingsCount = Rating::where('user_id', $user->id)->count();

        // Get recent favorite movies (last 5)
        $favoriteMovies = Favorite::where('user_id', $user->id)
            ->with(['movie' => function ($query) {
                $query->select('id', 'name', 'slug', 'poster_url', 'thumb_url', 'publish_year', 'type');
            }])
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($favorite) {
                return $favorite->movie;
            });

        // Get recently viewed movies (last 5)
        $recentMovies = History::where('user_id', $user->id)
            ->with(['movie' => function ($query) {
                $query->select('id', 'name', 'slug', 'poster_url', 'thumb_url', 'publish_year', 'type');
            }])
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($history) {
                return $history->movie;
            });

        return response()->json([
            'status' => true,
            'data' => [
                'user' => $user->only(['id', 'name', 'email', 'avatar']),
                'statistics' => [
                    'favorites_count' => $favoritesCount,
                    'history_count' => $historyCount,
                    'ratings_count' => $ratingsCount,
                ],
                'favoriteMovies' => $favoriteMovies,
                'recentMovies' => $recentMovies
            ]
        ]);
    }

    public function apiFavorites(Request $request)
    {
        $user = Auth::user();
        $perPage = $request->get('per_page', 12);
        $simple = $request->get('simple', false); // For simple array response

        if ($simple) {
            // Return simple array for JavaScript compatibility
            $favorites = Favorite::where('user_id', $user->id)
                ->with(['movie' => function ($query) {
                    $query->select('id', 'name', 'slug', 'poster_url', 'thumb_url', 'publish_year', 'type', 'view_total', 'rating_star');
                }])
                ->latest()
                ->get();

            return response()->json([
                'status' => true,
                'data' => $favorites
            ]);
        }

        // Return paginated data for UI listing
        $favorites = Favorite::where('user_id', $user->id)
            ->with(['movie' => function ($query) {
                $query->select('id', 'name', 'slug', 'poster_url', 'thumb_url', 'publish_year', 'type', 'view_total', 'rating_star');
            }])
            ->latest()
            ->paginate($perPage);

        return response()->json([
            'status' => true,
            'data' => $favorites
        ]);
    }

    public function apiHistory(Request $request)
    {
        $user = Auth::user();
        $perPage = $request->get('per_page', 12);

        $histories = History::where('user_id', $user->id)
            ->with(['movie' => function ($query) {
                $query->select('id', 'name', 'slug', 'poster_url', 'thumb_url', 'publish_year', 'type', 'view_total', 'rating_star');
            }])
            ->latest()
            ->paginate($perPage);

        return response()->json([
            'status' => true,
            'data' => $histories
        ]);
    }

    public function apiWatchlist(Request $request)
    {
        $user = Auth::user();
        $perPage = $request->get('per_page', 12);

        // For now, use favorites as watchlist. You can create separate watchlist table later
        $watchlist = Favorite::where('user_id', $user->id)
            ->with(['movie' => function ($query) {
                $query->select('id', 'name', 'slug', 'poster_url', 'thumb_url', 'publish_year', 'type', 'view_total', 'rating_star');
            }])
            ->latest()
            ->paginate($perPage);

        return response()->json([
            'status' => true,
            'data' => $watchlist
        ]);
    }

    public function apiRecommendations()
    {
        $user = Auth::user();

        // Implement recommendation logic based on user's preferences
        $recommendations = Movie::published()
            ->orderBy('view_total', 'desc')
            ->limit(20)
            ->get();

        return response()->json([
            'status' => true,
            'data' => $recommendations
        ]);
    }

    public function apiAddToFavorites(Request $request)
    {
        $request->validate([
            'movie_id' => 'required|exists:movies,id'
        ]);

        $user = Auth::user();
        $movieId = $request->movie_id;

        // Check if already in favorites
        $existing = Favorite::where('user_id', $user->id)
            ->where('movie_id', $movieId)
            ->first();

        if ($existing) {
            return response()->json([
                'status' => false,
                'message' => 'Phim đã có trong danh sách yêu thích'
            ], 409);
        }

        // Add to favorites
        Favorite::create([
            'user_id' => $user->id,
            'movie_id' => $movieId
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Đã thêm vào yêu thích',
            'is_favorite' => true,
            'action' => 'added'
        ]);
    }

    public function apiRemoveFromFavorites($movieId)
    {
        $user = Auth::user();

        // Find and remove from favorites
        $favorite = Favorite::where('user_id', $user->id)
            ->where('movie_id', $movieId)
            ->first();

        if (!$favorite) {
            return response()->json([
                'status' => false,
                'message' => 'Phim không có trong danh sách yêu thích'
            ], 404);
        }

        $favorite->delete();

        return response()->json([
            'status' => true,
            'message' => 'Đã xóa khỏi yêu thích',
            'is_favorite' => false,
            'action' => 'removed'
        ]);
    }

    public function apiAddToWatchlist(Request $request)
    {
        $request->validate([
            'movie_id' => 'required|exists:movies,id'
        ]);

        $user = Auth::user();
        $movieId = $request->movie_id;

        // Implement your watchlist logic here
        // Example: $user->watchlist()->attach($movieId);

        return response()->json([
            'status' => true,
            'message' => 'Đã thêm vào danh sách xem'
        ]);
    }

    public function apiRemoveFromWatchlist($movieId)
    {
        $user = Auth::user();

        // Implement your watchlist logic here
        // Example: $user->watchlist()->detach($movieId);

        return response()->json([
            'status' => true,
            'message' => 'Đã xóa khỏi danh sách xem'
        ]);
    }

    public function apiRateMovie(Request $request, $movieId)
    {
        $request->validate([
            'rating' => 'required|integer|between:1,5',
            'review' => 'nullable|string|max:1000'
        ]);

        $user = Auth::user();

        // Check if movie exists
        $movie = Movie::find($movieId);
        if (!$movie) {
            return response()->json([
                'status' => false,
                'message' => 'Phim không tồn tại'
            ], 404);
        }

        // Create or update rating
        $rating = Rating::updateOrCreate(
            [
                'user_id' => $user->id,
                'movie_id' => $movieId
            ],
            [
                'rating' => $request->rating,
                'review' => $request->review
            ]
        );

        // Update movie's average rating
        $this->updateMovieRating($movieId);

        return response()->json([
            'status' => true,
            'message' => 'Đánh giá thành công',
            'data' => [
                'rating' => $rating->rating,
                'review' => $rating->review,
                'created_at' => $rating->created_at,
                'updated_at' => $rating->updated_at
            ]
        ]);
    }

    /**
     * Update movie's average rating and count
     */
    private function updateMovieRating($movieId)
    {
        $ratings = Rating::where('movie_id', $movieId)->get();
        $averageRating = $ratings->avg('rating');
        $ratingCount = $ratings->count();

        Movie::where('id', $movieId)->update([
            'rating_star' => round($averageRating, 1),
            'rating_count' => $ratingCount
        ]);
    }

    /**
     * Toggle favorite status for a movie (add if not exists, remove if exists)
     */
    public function apiToggleFavorite($movieId)
    {
        $user = Auth::user();

        // Check if movie exists
        $movie = Movie::find($movieId);
        if (!$movie) {
            return response()->json([
                'status' => false,
                'message' => 'Phim không tồn tại'
            ], 404);
        }

        // Check if already in favorites
        $favorite = Favorite::where('user_id', $user->id)
            ->where('movie_id', $movieId)
            ->first();

        if ($favorite) {
            // Remove from favorites
            $favorite->delete();
            return response()->json([
                'status' => true,
                'message' => 'Đã xóa khỏi yêu thích',
                'is_favorite' => false,
                'action' => 'removed'
            ]);
        } else {
            // Add to favorites
            Favorite::create([
                'user_id' => $user->id,
                'movie_id' => $movieId
            ]);
            return response()->json([
                'status' => true,
                'message' => 'Đã thêm vào yêu thích',
                'is_favorite' => true,
                'action' => 'added'
            ]);
        }
    }

    /**
     * Check if movie is in user's favorites
     */
    public function apiCheckFavorite($movieId)
    {
        $user = Auth::user();

        $isFavorite = Favorite::where('user_id', $user->id)
            ->where('movie_id', $movieId)
            ->exists();

        return response()->json([
            'status' => true,
            'is_favorite' => $isFavorite
        ]);
    }

    /**
     * Get user's rating for a specific movie
     */
    public function apiGetMovieRating($movieId)
    {
        $user = Auth::user();

        $rating = Rating::where('user_id', $user->id)
            ->where('movie_id', $movieId)
            ->first();

        return response()->json([
            'status' => true,
            'data' => $rating ? [
                'rating' => $rating->rating,
                'review' => $rating->review,
                'created_at' => $rating->created_at,
                'updated_at' => $rating->updated_at
            ] : null
        ]);
    }

    /**
     * Add or update viewing history
     */
    public function apiAddToHistory(Request $request)
    {
        $request->validate([
            'movie_id' => 'required|exists:movies,id',
            'episode_id' => 'nullable|integer',
            'progress' => 'nullable|integer|min:0'
        ]);

        $user = Auth::user();

        // Only save history if user has enabled this setting
        if (!$user->save_watch_history) {
            return response()->json([
                'status' => false,
                'message' => 'Lưu lịch sử xem đã bị tắt'
            ]);
        }

        $history = History::updateOrCreate(
            [
                'user_id' => $user->id,
                'movie_id' => $request->movie_id
            ],
            [
                'episode_id' => $request->episode_id,
                'progress' => $request->progress ?? 0
            ]
        );

        return response()->json([
            'status' => true,
            'message' => 'Đã cập nhật lịch sử xem',
            'data' => $history
        ]);
    }

    /**
     * Remove item from history
     */
    public function apiRemoveFromHistory($historyId)
    {
        $user = Auth::user();

        $history = History::where('user_id', $user->id)
            ->where('id', $historyId)
            ->first();

        if (!$history) {
            return response()->json([
                'status' => false,
                'message' => 'Không tìm thấy lịch sử'
            ], 404);
        }

        $history->delete();

        return response()->json([
            'status' => true,
            'message' => 'Đã xóa khỏi lịch sử'
        ]);
    }

    /**
     * Clear all user history
     */
    public function apiClearHistory()
    {
        $user = Auth::user();

        $deletedCount = History::where('user_id', $user->id)->delete();

        return response()->json([
            'status' => true,
            'message' => "Đã xóa {$deletedCount} mục khỏi lịch sử"
        ]);
    }
}
