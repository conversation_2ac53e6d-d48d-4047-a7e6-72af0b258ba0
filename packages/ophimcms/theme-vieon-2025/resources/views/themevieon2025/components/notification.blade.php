<script>
    // Queue for notifications before SweetAlert2 is ready
    window.notificationQueue = window.notificationQueue || [];

    // Simple notification function
    window.showNotification = function (type, message, options = {}) {
        console.log('showNotification called with:', type, message);

        // Check if SweetAlert2 is available - simple check only
        if (typeof Swal === 'undefined') {
            console.warn('SweetAlert2 not available! Using fallback notification');
            window.showFallbackNotification(type, message);
            return;
        }

        const icons = {
            success: 'success',
            error: 'error',
            warning: 'warning',
            info: 'info',
        };

        try {
            Swal.fire({
                icon: icons[type] || 'info',
                title: message,
                timer: 3000,
                timerProgressBar: true,
                showConfirmButton: false,
                toast: true,
                position: 'top-end',
                theme: 'dark',
                ...options
            });
        } catch (error) {
            console.error('SweetAlert2 error, using fallback:', error);
            window.showFallbackNotification(type, message);
        }
    };

    // Fallback notification using DOM
    window.showFallbackNotification = function (type, message) {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                    type === 'warning' ? 'bg-yellow-500 text-white' :
                        'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    };

    // Process any queued notifications when DOM is ready
    function processNotificationQueue() {
        if (window.notificationQueue && window.notificationQueue.length > 0) {
            console.log(`Processing ${window.notificationQueue.length} queued notifications`);
            const queuedNotifications = [...window.notificationQueue];
            window.notificationQueue = []; // Clear queue

            queuedNotifications.forEach(({type, message, options}) => {
                window.showNotification(type, message, options);
            });
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function () {
            console.log('✅ Notification system initialized');
            processNotificationQueue();
        });
    } else {
        console.log('✅ Notification system initialized');
        processNotificationQueue();
    }


    // Session-based notifications - queue them for processing
    @if(session('success'))
    window.notificationQueue.push({
        type: 'success',
        message: @json(session('success'), JSON_THROW_ON_ERROR),
        options: {}
    });
    @endif
    @if(session('error'))
    window.notificationQueue.push({
        type: 'error',
        message: @json(session('error'), JSON_THROW_ON_ERROR),
        options: {}
    });
    @endif
    @if(session('warning'))
    window.notificationQueue.push({
        type: 'warning',
        message: @json(session('warning'), JSON_THROW_ON_ERROR),
        options: {}
    });
    @endif
    @if(session('info'))
    window.notificationQueue.push({
        type: 'info',
        message: @json(session('info'), JSON_THROW_ON_ERROR),
        options: {}
    });
    @endif
</script>
