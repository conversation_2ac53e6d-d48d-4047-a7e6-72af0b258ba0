<?php

namespace Ophim\ThemeVieon2025\Controllers\User\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Ophim\ThemeVieon2025\Models\Rating;

class ApiRatingController extends Controller
{
    // Middleware is applied at route level - see routes/api.php

    public function apiIndex($movieId)
    {
        $ratings = Rating::where('movie_id', $movieId)
            ->with('user')
            ->latest()
            ->paginate(10);

        return response()->json([
            'status' => true,
            'data' => $ratings
        ]);
    }

    public function apiStore(Request $request, $movieId)
    {
        $data = $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'review' => 'nullable|string|max:500',
        ]);

        $data['user_id'] = Auth::id();
        $data['movie_id'] = $movieId;

        $rating = Rating::updateOrCreate(
            [
                'user_id' => $data['user_id'],
                'movie_id' => $data['movie_id'],
            ],
            [
                'rating' => $data['rating'],
                'review' => $data['review'] ?? null,
            ]
        );

        return response()->json([
            'status' => true,
            'message' => 'Đánh giá thành công!',
            'data' => $rating->load('user')
        ]);
    }

    public function apiDestroy($movieId)
    {
        $rating = Rating::where('user_id', Auth::id())
            ->where('movie_id', $movieId)
            ->firstOrFail();

        $rating->delete();

        return response()->json([
            'status' => true,
            'message' => 'Đã xóa đánh giá!'
        ]);
    }
}
