@if(isset($currentMovie) && $currentMovie->getRatingStar())
    <div class="flex items-center gap-3 mb-6 flex-col sm:flex-row rating-box"
         id="rating-container"
         itemprop="aggregateRating"
         itemscope
         itemtype="https://schema.org/AggregateRating">

        <div class="flex text-yellow-400 cursor-pointer" id="star"
             data-score="{{ $currentMovie->getRatingStar() }}">
        </div>
        <span class="text-sm sm:text-lg text-white font-medium mt-2 sm:mt-0" id="average-container">
            <span id="hint"></span>
            (<span class="average" id="average"
                   itemprop="ratingValue">{{ $currentMovie->getRatingStar() }}</span>
            điểm /
            <span id="rate-count" itemprop="ratingCount">{{ $currentMovie->getRatingCount() }}</span>
            lượt)
        </span>
        <meta itemprop="bestRating" content="10"/>
    <div itemprop="itemReviewed" itemscope itemtype="https://schema.org/Movie">
        <meta itemprop="name" content="{{ $currentMovie->name }}">
        <meta itemprop="url" content="{{ $currentMovie->getUrl() }}">
        <meta itemprop="image" content="{{ $currentMovie->getThumbUrl() }}">
    </div>     
    </div>
@endif
