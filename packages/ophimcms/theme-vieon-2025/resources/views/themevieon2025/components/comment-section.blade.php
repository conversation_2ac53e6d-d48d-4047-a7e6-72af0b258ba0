{{-- Comment Section Component --}}
@php
    // Set default values for props
    $episode = $episode ?? null;
    $comments = $comments ?? null;
    $title = $title ?? null;

    $isEpisodeLevel = !is_null($episode);
    $contextTitle = $title ?? ($isEpisodeLevel ? "Bình luận tập {$episode->name}" : 'Bình luận phim');
    $commentContext = $isEpisodeLevel ? 'episode' : 'movie';
@endphp

<section class="py-8 lg:py-12">
    <div class="container mx-auto px-4">
        <div class="bg-theme-secondary rounded-lg p-6">
            {{-- Header --}}
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl lg:text-2xl font-bold text-theme-primary">
                    {{ $contextTitle }}
                </h2>

                {{-- Cross-navigation links --}}
                <div class="flex items-center gap-4 text-sm">
                    @if($isEpisodeLevel)
                        <a href="{{ route('movies.show', $movie->slug) }}#comments"
                           class="text-theme-tertiary hover:text-main transition-colors">
                            <i class="fas fa-film mr-1"></i>
                            Thảo luận tổng về phim
                        </a>
                    @else
                        @if($movie->type === 'series' && $movie->episodes->count() > 0)
                            <span class="text-theme-tertiary">
                                <i class="fas fa-list mr-1"></i>
                                Xem thảo luận từng tập trong trang xem phim
                            </span>
                        @endif
                    @endif
                </div>
            </div>

            {{-- Comment Form --}}
            @auth
                <div class="mb-8">
                    <form id="comment-form" class="space-y-4" data-context="{{ $commentContext }}"
                          data-movie-id="{{ $movie->id }}"
                          @if($isEpisodeLevel) data-episode-id="{{ $episode->id }}" @endif>
                        @csrf
                        <div>
                            <label for="comment-content" class="block text-sm font-medium text-theme-primary mb-2">
                                Viết bình luận
                            </label>
                            <textarea
                                id="comment-content"
                                name="content"
                                rows="4"
                                class="w-full px-4 py-3 bg-theme-primary border border-theme-tertiary rounded-lg text-theme-primary placeholder-theme-tertiary focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent resize-none"
                                placeholder="Chia sẻ suy nghĩ của bạn về {{ $isEpisodeLevel ? 'tập phim này' : 'bộ phim này' }}..."
                                required></textarea>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-theme-tertiary">
                                <i class="fas fa-info-circle mr-1"></i>
                                Hãy giữ bình luận lịch sự và tôn trọng
                            </div>
                            <button
                                type="submit"
                                class="px-6 py-2 bg-main hover:bg-green-600 text-white font-semibold rounded-lg transition-colors">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Gửi bình luận
                            </button>
                        </div>
                    </form>
                </div>
            @else
                <div class="mb-8 p-4 bg-theme-primary rounded-lg border border-theme-tertiary">
                    <div class="text-center">
                        <p class="text-theme-secondary mb-4">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Đăng nhập để tham gia thảo luận
                        </p>
                        <div class="flex items-center justify-center gap-4">
                            <a href="{{ route('login') }}"
                               class="px-4 py-2 bg-main hover:bg-green-600 text-white rounded-lg transition-colors">
                                Đăng nhập
                            </a>
                            <a href="{{ route('register') }}"
                               class="px-4 py-2 bg-theme-button hover:bg-theme-button-hover text-theme-primary rounded-lg transition-colors">
                                Đăng ký
                            </a>
                        </div>
                    </div>
                </div>
            @endauth

            {{-- Comments List --}}
            <div id="comments-container">
                @if($comments && $comments->count() > 0)
                    <div class="space-y-6">
                        @foreach($comments as $comment)
                            @include('themevieon2025::components.comment-item', ['comment' => $comment, 'level' => 0])
                        @endforeach
                    </div>

                    {{-- Pagination --}}
                    @if($comments->hasPages())
                        <div class="mt-8 flex justify-center">
                            <div class="bg-theme-primary rounded-lg p-4">
                                {{ $comments->links() }}
                            </div>
                        </div>
                    @endif
                @else
                    <div class="text-center py-12">
                        <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-theme-primary mb-6">
                            <svg class="h-12 w-12 text-theme-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-theme-primary mb-2">Chưa có bình luận nào</h3>
                        <p class="text-theme-tertiary">
                            {{ $isEpisodeLevel ? 'Hãy là người đầu tiên bình luận về tập phim này!' : 'Hãy là người đầu tiên bình luận về bộ phim này!' }}
                        </p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>

{{-- Loading Overlay --}}
<div id="comment-loading" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="flex items-center justify-center h-full">
        <div class="bg-theme-secondary rounded-lg p-6 flex items-center space-x-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-main"></div>
            <span class="text-theme-primary">Đang xử lý...</span>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const commentForm = document.getElementById('comment-form');
    const commentsContainer = document.getElementById('comments-container');
    const loadingOverlay = document.getElementById('comment-loading');

    // Set current user ID for delete button visibility
    @auth
        window.currentUserId = {{ Auth::id() }};
    @else
        window.currentUserId = null;
    @endauth

    // Comment management object
    const CommentManager = {
        currentPage: 1,
        isLoading: false,
        context: commentForm?.dataset.context || 'movie',
        movieId: commentForm?.dataset.movieId,
        episodeId: commentForm?.dataset.episodeId,

        // Load comments via AJAX
        async loadComments(page = 1, append = false) {
            if (this.isLoading) return;

            this.isLoading = true;
            this.showLoadingState();

            try {
                let response;
                if (this.context === 'episode' && this.episodeId) {
                    response = await window.userAPI.getEpisodeComments(this.movieId, this.episodeId, page);
                } else {
                    response = await window.userAPI.getComments(this.movieId, page);
                }

                if (response.status && response.data) {
                    this.renderComments(response.data, append);
                    this.currentPage = page;
                } else {
                    throw new Error('Không thể tải bình luận');
                }
            } catch (error) {
                console.error('Load comments error:', error);
                window.showNotification('error', 'Có lỗi xảy ra khi tải bình luận');
            } finally {
                this.isLoading = false;
                this.hideLoadingState();
            }
        },

        // Render comments to DOM
        renderComments(commentsData, append = false) {
            const comments = commentsData.data || commentsData;

            if (!append) {
                // Replace all comments
                if (comments.length > 0) {
                    const commentsHtml = comments.map(comment => this.renderCommentItem(comment)).join('');
                    commentsContainer.innerHTML = `
                        <div class="space-y-6">
                            ${commentsHtml}
                        </div>
                        ${this.renderPagination(commentsData)}
                    `;
                } else {
                    commentsContainer.innerHTML = this.renderEmptyState();
                }
            } else {
                // Append comments (for load more)
                const existingContainer = commentsContainer.querySelector('.space-y-6');
                if (existingContainer && comments.length > 0) {
                    const commentsHtml = comments.map(comment => this.renderCommentItem(comment)).join('');
                    existingContainer.insertAdjacentHTML('beforeend', commentsHtml);

                    // Update pagination
                    const paginationContainer = commentsContainer.querySelector('.pagination-container');
                    if (paginationContainer) {
                        paginationContainer.outerHTML = this.renderPagination(commentsData);
                    }
                }
            }

            // Setup event delegation for new content
            this.setupEventDelegation();
        },

        // Render single comment item
        renderCommentItem(comment) {
            const isEpisodeLevel = this.context === 'episode';
            const timeAgo = this.formatTimeAgo(comment.created_at);
            const repliesHtml = comment.children ? comment.children.map(reply => this.renderReplyItem(reply)).join('') : '';
            const currentUserId = window.currentUserId || null; // Assume this is set globally

            return `
                <div class="comment-item comment-item-${comment.id} bg-theme-primary rounded-lg p-4 border border-theme-tertiary">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-main rounded-full flex items-center justify-center">
                                <span class="text-white font-semibold text-sm">
                                    ${comment.user?.name?.charAt(0).toUpperCase() || 'U'}
                                </span>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-2">
                                    <h4 class="text-sm font-semibold text-theme-primary">
                                        ${comment.user?.name || 'Người dùng'}
                                    </h4>
                                    <span class="text-xs text-theme-tertiary">${timeAgo}</span>
                                </div>
                            </div>
                            <p class="text-theme-secondary text-sm leading-relaxed mb-3">
                                ${comment.content}
                            </p>
                            <div class="flex items-center space-x-4 text-xs">
                                <button class="reply-btn text-theme-tertiary hover:text-main transition-colors"
                                        data-comment-id="${comment.id}">
                                    <i class="fas fa-reply mr-1"></i>
                                    Phản hồi
                                </button>
                                ${currentUserId && currentUserId == comment.user_id ? `
                                    <button class="delete-btn text-red-400 hover:text-red-300 transition-colors"
                                            data-comment-id="${comment.id}">
                                        <i class="fas fa-trash mr-1"></i>
                                        Xóa
                                    </button>
                                ` : ''}
                            </div>

                            <!-- Reply Form -->
                            <div id="reply-form-${comment.id}" class="reply-form mt-4 hidden">
                                <form class="reply-comment-form space-y-3"
                                      data-parent-id="${comment.id}"
                                      data-movie-id="${this.movieId}"
                                      ${this.episodeId ? `data-episode-id="${this.episodeId}"` : ''}>
                                    <textarea name="content" rows="3"
                                              class="w-full px-3 py-2 bg-theme-secondary border border-theme-tertiary rounded text-theme-primary placeholder-theme-tertiary focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent resize-none text-sm"
                                              placeholder="Viết phản hồi..."></textarea>
                                    <div class="flex items-center justify-end space-x-2">
                                        <button type="button" class="cancel-reply-btn px-3 py-1 text-xs text-theme-tertiary hover:text-theme-primary transition-colors"
                                                data-comment-id="${comment.id}">
                                            Hủy
                                        </button>
                                        <button type="submit"
                                                class="px-4 py-1 bg-main hover:bg-green-600 text-white text-xs rounded transition-colors">
                                            Gửi
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Replies -->
                            ${repliesHtml ? `<div class="replies mt-4 space-y-3">${repliesHtml}</div>` : ''}
                        </div>
                    </div>
                </div>
            `;
        },

        // Render reply item
        renderReplyItem(reply) {
            const timeAgo = this.formatTimeAgo(reply.created_at);
            const currentUserId = window.currentUserId || null;

            return `
                <div class="comment-item comment-item-${reply.id} bg-theme-secondary rounded p-3 ml-6 border-l-2 border-main">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-main rounded-full flex items-center justify-center">
                                <span class="text-white font-semibold text-xs">
                                    ${reply.user?.name?.charAt(0).toUpperCase() || 'U'}
                                </span>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-1">
                                <div class="flex items-center space-x-2">
                                    <h5 class="text-xs font-semibold text-theme-primary">
                                        ${reply.user?.name || 'Người dùng'}
                                    </h5>
                                    <span class="text-xs text-theme-tertiary">${timeAgo}</span>
                                </div>
                                ${currentUserId && currentUserId == reply.user_id ? `
                                    <button class="delete-btn text-red-400 hover:text-red-300 text-xs transition-colors"
                                            data-comment-id="${reply.id}">
                                        <i class="fas fa-trash mr-1"></i>
                                        Xóa
                                    </button>
                                ` : ''}
                            </div>
                            <p class="text-theme-secondary text-xs leading-relaxed">
                                ${reply.content}
                            </p>
                        </div>
                    </div>
                </div>
            `;
        },

        // Render empty state
        renderEmptyState() {
            const isEpisodeLevel = this.context === 'episode';
            return `
                <div class="text-center py-12">
                    <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-theme-primary mb-6">
                        <svg class="h-12 w-12 text-theme-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-theme-primary mb-2">Chưa có bình luận nào</h3>
                    <p class="text-theme-tertiary">
                        ${isEpisodeLevel ? 'Hãy là người đầu tiên bình luận về tập phim này!' : 'Hãy là người đầu tiên bình luận về bộ phim này!'}
                    </p>
                </div>
            `;
        },

        // Render pagination
        renderPagination(commentsData) {
            if (!commentsData.last_page || commentsData.last_page <= 1) return '';

            const currentPage = commentsData.current_page;
            const lastPage = commentsData.last_page;

            return `
                <div class="pagination-container mt-8 flex justify-center">
                    <div class="bg-theme-primary rounded-lg p-4">
                        <nav class="flex items-center space-x-2">
                            ${currentPage > 1 ? `
                                <button class="pagination-btn px-3 py-1 text-sm text-theme-tertiary hover:text-main transition-colors"
                                        data-page="${currentPage - 1}">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                            ` : ''}

                            <span class="px-3 py-1 text-sm text-theme-primary">
                                Trang ${currentPage} / ${lastPage}
                            </span>

                            ${currentPage < lastPage ? `
                                <button class="pagination-btn px-3 py-1 text-sm text-theme-tertiary hover:text-main transition-colors"
                                        data-page="${currentPage + 1}">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            ` : ''}
                        </nav>
                    </div>
                </div>
            `;
        },

        // Format time ago
        formatTimeAgo(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'Vừa xong';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} phút trước`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} giờ trước`;
            return `${Math.floor(diffInSeconds / 86400)} ngày trước`;
        },

        // Show loading state
        showLoadingState() {
            if (!commentsContainer.querySelector('.comments-loading')) {
                commentsContainer.insertAdjacentHTML('afterbegin', `
                    <div class="comments-loading flex items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-main"></div>
                        <span class="ml-3 text-theme-tertiary">Đang tải bình luận...</span>
                    </div>
                `);
            }
        },

        // Hide loading state
        hideLoadingState() {
            const loadingElement = commentsContainer.querySelector('.comments-loading');
            if (loadingElement) {
                loadingElement.remove();
            }
        },



        // Toggle reply form
        toggleReplyForm(commentId) {
            const replyForm = document.getElementById(`reply-form-${commentId}`);
            if (replyForm) {
                replyForm.classList.toggle('hidden');
                if (!replyForm.classList.contains('hidden')) {
                    const textarea = replyForm.querySelector('textarea');
                    if (textarea) textarea.focus();
                }
            }
        },

        // Submit reply
        async submitReply(event, parentId) {
            event.preventDefault();

            const form = event.target;
            const content = form.content.value.trim();

            if (!content) {
                window.showNotification('error', 'Vui lòng nhập nội dung phản hồi');
                return;
            }

            try {
                this.showLoadingState();

                let response;
                if (this.context === 'episode' && this.episodeId) {
                    response = await window.userAPI.addEpisodeComment(this.movieId, this.episodeId, content, parentId);
                } else {
                    response = await window.userAPI.addComment(this.movieId, content, parentId);
                }

                if (response.status) {
                    form.reset();
                    this.toggleReplyForm(parentId);
                    window.showNotification('success', response.message || 'Phản hồi thành công!');

                    // Reload comments to show new reply
                    await this.loadComments(this.currentPage);
                } else {
                    throw new Error(response.message || 'Có lỗi xảy ra');
                }
            } catch (error) {
                console.error('Reply error:', error);
                window.showNotification('error', error.message || 'Có lỗi xảy ra khi gửi phản hồi');
            } finally {
                this.hideLoadingState();
            }
        },

        // Delete comment
        async deleteComment(commentId) {
            if (!confirm('Bạn có chắc chắn muốn xóa bình luận này?')) {
                return;
            }

            try {
                this.showLoadingState();
                const response = await window.userAPI.deleteComment(commentId);

                if (response.status) {
                    window.showNotification('success', response.message || 'Đã xóa bình luận');
                    // Reload comments to reflect deletion
                    await this.loadComments(this.currentPage);
                } else {
                    throw new Error(response.message || 'Có lỗi xảy ra');
                }
            } catch (error) {
                console.error('Delete comment error:', error);
                window.showNotification('error', error.message || 'Có lỗi xảy ra khi xóa bình luận');
            } finally {
                this.hideLoadingState();
            }
        },

        // Setup event delegation
        setupEventDelegation() {
            const container = document.getElementById('comments-container');
            if (!container) return;

            // Remove existing listeners to prevent duplicates
            container.removeEventListener('click', this.handleClick);
            container.removeEventListener('submit', this.handleSubmit);

            // Add event listeners with proper binding
            this.handleClick = this.handleClick.bind(this);
            this.handleSubmit = this.handleSubmit.bind(this);

            container.addEventListener('click', this.handleClick);
            container.addEventListener('submit', this.handleSubmit);
        },

        // Handle click events (reply buttons, delete buttons, cancel buttons, pagination)
        handleClick(event) {
            const target = event.target;

            // Reply button
            if (target.classList.contains('reply-btn') || target.closest('.reply-btn')) {
                event.preventDefault();
                const btn = target.classList.contains('reply-btn') ? target : target.closest('.reply-btn');
                const commentId = btn.dataset.commentId;
                this.toggleReplyForm(commentId);
                return;
            }

            // Delete button
            if (target.classList.contains('delete-btn') || target.closest('.delete-btn')) {
                event.preventDefault();
                const btn = target.classList.contains('delete-btn') ? target : target.closest('.delete-btn');
                const commentId = btn.dataset.commentId;
                this.deleteComment(commentId);
                return;
            }

            // Cancel reply button
            if (target.classList.contains('cancel-reply-btn') || target.closest('.cancel-reply-btn')) {
                event.preventDefault();
                const btn = target.classList.contains('cancel-reply-btn') ? target : target.closest('.cancel-reply-btn');
                const commentId = btn.dataset.commentId;
                this.toggleReplyForm(commentId);
                return;
            }

            // Pagination button
            if (target.classList.contains('pagination-btn') || target.closest('.pagination-btn')) {
                event.preventDefault();
                const btn = target.classList.contains('pagination-btn') ? target : target.closest('.pagination-btn');
                const page = parseInt(btn.dataset.page);
                if (page && !isNaN(page)) {
                    this.loadComments(page);
                }
                return;
            }
        },

        // Handle form submissions (reply forms)
        async handleSubmit(event) {
            if (event.target.classList.contains('reply-comment-form')) {
                event.preventDefault();

                const form = event.target;
                const parentId = form.dataset.parentId;
                const content = form.content.value.trim();

                if (!content) {
                    window.showNotification('error', 'Vui lòng nhập nội dung phản hồi');
                    return;
                }

                try {
                    this.showLoadingState();

                    let response;
                    if (this.context === 'episode' && this.episodeId) {
                        response = await window.userAPI.addEpisodeComment(this.movieId, this.episodeId, content, parentId);
                    } else {
                        response = await window.userAPI.addComment(this.movieId, content, parentId);
                    }

                    if (response.status) {
                        form.reset();
                        this.toggleReplyForm(parentId);
                        window.showNotification('success', response.message || 'Phản hồi thành công!');

                        // Reload comments to show new reply
                        await this.loadComments(this.currentPage);
                    } else {
                        throw new Error(response.message || 'Có lỗi xảy ra');
                    }
                } catch (error) {
                    console.error('Reply error:', error);
                    window.showNotification('error', error.message || 'Có lỗi xảy ra khi gửi phản hồi');
                } finally {
                    this.hideLoadingState();
                }
            }
        }
    };

    // Make CommentManager globally available
    window.CommentManager = CommentManager;

    // Setup event delegation on page load
    CommentManager.setupEventDelegation();

    // Handle main comment form submission
    if (commentForm) {
        commentForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const content = formData.get('content');

            if (!content.trim()) {
                window.showNotification('error', 'Vui lòng nhập nội dung bình luận');
                return;
            }

            try {
                loadingOverlay.classList.remove('hidden');

                let response;
                if (CommentManager.context === 'episode' && CommentManager.episodeId) {
                    response = await window.userAPI.addEpisodeComment(CommentManager.movieId, CommentManager.episodeId, content);
                } else {
                    response = await window.userAPI.addComment(CommentManager.movieId, content);
                }

                if (response.status) {
                    // Clear form
                    this.reset();

                    // Show success message
                    window.showNotification('success', response.message || 'Bình luận thành công!');

                    // Reload comments via AJAX instead of page reload
                    await CommentManager.loadComments(1);
                } else {
                    throw new Error(response.message || 'Có lỗi xảy ra');
                }
            } catch (error) {
                console.error('Comment error:', error);
                window.showNotification('error', error.message || 'Có lỗi xảy ra khi gửi bình luận');
            } finally {
                loadingOverlay.classList.add('hidden');
            }
        });
    }
});
</script>
@endpush
