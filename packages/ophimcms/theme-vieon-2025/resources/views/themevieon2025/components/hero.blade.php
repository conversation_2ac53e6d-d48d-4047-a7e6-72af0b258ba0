@php
    $banners = $banners ?? (isset($featured_movie) ? [$featured_movie] : []);
@endphp
@if(!empty($banners) && count($banners) > 0)
    <div class="hero-section relative w-full h-screen min-h-[600px] flex items-end bg-black">
        <div class="hero-swiper swiper w-full h-full">
            <div class="swiper-wrapper">
                @foreach($banners as $movie)
                    <div class="swiper-slide relative w-full h-screen min-h-[600px]">
                        <img src="{{ $movie->thumb_url ?? $movie->poster_url }}" alt="{{ $movie->name }}"
                             class="absolute inset-0 w-full h-full object-cover object-center"/>
                        <div class="absolute inset-0 bg-gradient-to-t from-black via-black/70 to-transparent"></div>
                        <div class="relative z-10 pl-12 pb-20 max-w-2xl text-white flex flex-col justify-end h-full">
                            @if($movie->origin_name)
                                <div class="text-lg font-medium mb-1 opacity-90">{{ $movie->origin_name }}</div>
                            @endif
                            <h1 class="text-7xl font-bold mb-3 drop-shadow-lg leading-tight"
                                style="font-family: 'UTM Bebas',sans-serif;">
                                @php
                                    $parts = explode(' ', $movie->name, 2);
                                @endphp
                                {{ $parts[0] ?? $movie->name }}<br>
                                <span class="text-main">{{ $parts[1] ?? '' }}</span>
                            </h1>
                            <div class="flex items-center gap-4 mb-4">
                                <div
                                    class="badge-main px-4 py-1.5 rounded-full text-sm font-medium bg-white/10 text-white border border-white/20">
                                    {{ $movie->episode_current }}
                                </div>
                                <div class="flex items-center gap-2 text-sm text-white/80">
                                    <i class="fas fa-star text-yellow-400"></i>
                                    <span>{{ number_format($movie->rating_star, 1) }}</span>
                                </div>
                                <div class="flex items-center gap-2 text-sm text-white/80">
                                    <i class="fas fa-eye"></i>
                                    <span>{{ number_format($movie->view_total) }}</span>
                                </div>
                            </div>
                            <div class="flex flex-wrap items-center gap-2 mb-4 text-sm">
                                @foreach($movie->categories ?? [] as $cat)
                                    <span
                                        class="px-3 py-1 rounded-full bg-white/10 text-white/90">{{ $cat->name }}</span>
                                @endforeach
                            </div>
                            <p class="mb-6 text-base line-clamp-2 max-w-xl text-white/90">{{ $movie->content }}</p>
                            <div class="flex gap-4">
                                <a href="{{ route('movies.show', $movie->slug) }}"
                                   class="button-main px-8 py-3 flex items-center text-base font-medium rounded-lg shadow-lg bg-main hover:bg-main/90 transition-all duration-300">
                                    <i class="fa-solid fa-play mr-2"></i> Xem ngay
                                </a>
                                <a href="{{ route('movies.show', $movie->slug) }}"
                                   class="button-outline-main px-8 py-3 flex items-center text-base font-medium rounded-lg border-2 border-white/20 hover:border-white/40 transition-all duration-300">
                                    <i class="fa-solid fa-circle-info mr-2"></i> Chi tiết
                                </a>

                                @auth
                                    <!-- Favorite Button -->
                                    <button onclick="toggleFavorite({{ $movie->id }})"
                                            data-movie-id="{{ $movie->id }}"
                                            data-is-favorite="{{ isset($movie->userFavorite) && $movie->userFavorite ? 'true' : 'false' }}"
                                            class="hero-favorite-btn px-4 py-3 flex items-center text-base font-medium rounded-lg border-2 transition-all duration-300
                                                   {{ isset($movie->userFavorite) && $movie->userFavorite
                                                      ? 'bg-red-500 border-red-500 text-white hover:bg-red-600 hover:border-red-600'
                                                      : 'border-white/20 text-white hover:border-white/40 hover:bg-white/10' }}"
                                            title="{{ isset($movie->userFavorite) && $movie->userFavorite ? 'Bỏ khỏi yêu thích' : 'Thêm vào yêu thích' }}">
                                        <i class="{{ isset($movie->userFavorite) && $movie->userFavorite ? 'fas' : 'far' }} fa-heart"></i>
                                    </button>

                                    <!-- Watchlist Button -->
                                    <button onclick="toggleWatchlist({{ $movie->id }})"
                                            data-movie-id="{{ $movie->id }}"
                                            data-is-watchlist="{{ isset($movie->userWatchlist) && $movie->userWatchlist ? 'true' : 'false' }}"
                                            class="hero-watchlist-btn px-4 py-3 flex items-center text-base font-medium rounded-lg border-2 transition-all duration-300
                                                   {{ isset($movie->userWatchlist) && $movie->userWatchlist
                                                      ? 'bg-blue-500 border-blue-500 text-white hover:bg-blue-600 hover:border-blue-600'
                                                      : 'border-white/20 text-white hover:border-white/40 hover:bg-white/10' }}"
                                            title="{{ isset($movie->userWatchlist) && $movie->userWatchlist ? 'Xóa khỏi danh sách xem' : 'Thêm vào danh sách xem' }}">
                                        <i class="fas fa-bookmark"></i>
                                    </button>
                                @endauth
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            <!-- Swiper Controls: Pagination + Navigation -->
            <div class="hero-swiper-controls absolute right-8 bottom-8 z-20 flex items-center gap-6">
                <div class="hero-swiper-pagination flex gap-2"></div>
                <div class="flex items-center gap-3">
                    <div
                        class="hero-swiper-prev cursor-pointer w-12 h-12 flex items-center justify-center rounded-full bg-white/10 hover:bg-white/20 transition-all duration-300">
                        <i class="fa-solid fa-chevron-left text-lg"></i>
                    </div>
                    <div
                        class="hero-swiper-next cursor-pointer w-12 h-12 flex items-center justify-center rounded-full bg-white/10 hover:bg-white/20 transition-all duration-300">
                        <i class="fa-solid fa-chevron-right text-lg"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endif
