@extends('themevieon2025::layouts.app')
@section('content')
    <div class="max-w-md mx-auto mt-20 bg-gray-900 p-8 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold mb-6 text-center">Đặt lại mật khẩu</h2>
        <form method="POST" action="{{ route('api.password.reset') }}" class="ajax-form">
            @csrf
            <input type="hidden" name="token" value="{{ $token }}">
            <div class="mb-4">
                <label class="block mb-1">Email</label>
                <input type="email" name="email"
                       class="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" required autofocus
                       value="{{ old('email') }}">
                @error('email')
                <div class="text-red-500 text-sm mt-1">{{ $message }}</div>@enderror
            </div>
            <div class="mb-4">
                <label class="block mb-1">M<PERSON>t khẩu mới</label>
                <input type="password" name="password"
                       class="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" required>
                @error('password')
                <div class="text-red-500 text-sm mt-1">{{ $message }}</div>@enderror
            </div>
            <div class="mb-4">
                <label class="block mb-1">Xác nhận mật khẩu</label>
                <input type="password" name="password_confirmation"
                       class="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" required>
            </div>
            <button type="submit"
                    class="w-full bg-main hover:bg-yellow-500 text-black font-bold py-2 rounded transition">Đặt lại mật
                khẩu
            </button>
        </form>
    </div>
@endsection
