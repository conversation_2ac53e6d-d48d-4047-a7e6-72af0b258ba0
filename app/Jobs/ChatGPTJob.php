<?php

namespace App\Jobs;


use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Bus\Batchable;

class ChatGPTJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels, InteractsWithSockets;

    protected $movie;
    protected $keys;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($movie, $keys)
    {
        $this->movie = $movie;
        $this->keys = $keys;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $content = $this->movie['content'];
        $name = $this->movie['name'];
        $origin_name = $this->movie['origin_name'];
        $year = $this->movie['publish_year'];
        $status = $this->movie['status'];
        $episode_time = $this->movie['episode_time'];
        $episode_total = $this->movie['episode_total'];

        $categories = null;
        $actors = null;
        $directors = null;

        if (count($this->movie->categories)) {
            $categories = $this->movie->categories->map(function ($category) {
                return $category->name;
            })->implode(', ');
        }

        if (count($this->movie->directors)) {
            $directors = $this->movie->directors->map(function ($director) {
                return $director->name;
            })->implode(', ');
        }

        if (count($this->movie->actors)) {
            $actors = $this->movie->actors->map(function ($actor) {
                return $actor->name;
            })->implode(', ');
        }

        if (!is_null('content')) {
            // Tạo các prompt khác nhau
            $prompt1 = "Tạo một đoạn văn bằng Tiếng Việt miêu tả nội dung phim {$name}({$origin_name}) kể về '{$content}', {$categories}, {$actors} {$directors}.";
            $prompt2 = "Tạo một đoạn văn bằng Tiếng Việt tóm tắt cốt truyện phim {$name}({$origin_name}) . Sử dụng những từ khóa liên quan đến tên phim, {$actors} và tình tiết quan trọng của phim '{$content}'.";
            $prompt3 = "Hãy viết một đoạn văn bằng Tiếng Việt miêu tả nội dung phim {$name}({$origin_name}) xoay quanh'{$content}',{$categories}, {$actors} {$directors}.";
            $prompt4 = "Tạo một đoạn văn ngắn bằng Tiếng Việt tóm tắt cốt truyện '{$content}' phim {$name}({$origin_name}). {$actors} cùng với {$directors}.";
            $prompt5 = "Viết một đoạn văn bằng Tiếng Việt miêu tả nội dung phim {$name}({$origin_name}) và nhấn mạnh vào {$actors} . Hãy giới thiệu đầy đủ thông tin về cốt truyện '{$content}' và nhân vật chính trong bộ phim để thu hút sự quan tâm của khán giả.";
            $prompt6 = "Tạo một đoạn văn ngắn bằng Tiếng Việt tóm tắt cốt truyện '{$content}' phim {$name}({$origin_name}) {$directors}.";
            $prompt7 = "Tạo một đoạn văn ngắn bằng Tiếng Việt tóm tắt phim {$name}({$origin_name}) theo cốt truyện '{$content}' .";

            // Chọn một trong các prompt ở trên một cách ngẫu nhiên
            $selectedPrompt = rand(1, 7);

            // Tùy thuộc vào prompt được chọn, gán giá trị cho biến $prompt
            if ($selectedPrompt === 1) {
                $prompt = $prompt1;
            } elseif ($selectedPrompt === 2) {
                $prompt = $prompt2;
            } elseif ($selectedPrompt === 3) {
                $prompt = $prompt3;
            } elseif ($selectedPrompt === 4) {
                $prompt = $prompt4;
            } elseif ($selectedPrompt === 5) {
                $prompt = $prompt5;
            } elseif ($selectedPrompt === 6) {
                $prompt = $prompt6;
            } else {
                $prompt = $prompt7;
            }
        
        } else {
            // bắt đầu prompt
            $prompt = "Dựa vào các thông tin sau, hãy viết cho tôi 1 đoạn giới thiệu bộ phim {$name}, tên khác {$origin_name}";
            // nếu có đạo diễn thì thêm đoạn dưới
            if ($directors) {
                $prompt .= " được đạo diễn bởi $directors";
            }
            // nếu có diễn viên thì thêm đoạn dưới
            if ($actors) {
                $prompt .= " với sự tham gia của các diễn viên $actors";
            }
            // nối tiếp với đoạn dưới
            $prompt .= " và có nội dung khái quát {$content} bằng tiếng Việt";

        }

        $dataContent = $this->callToChatGPT($prompt);
        if ($dataContent) {
            $this->movie->update([
                'content' => $dataContent,
                'prompt' => $prompt
            ]);
        } else {
            $this->movie->update([
                'content' => $content,
                'prompt' => null
            ]);
        }
    }

    public function failed()
    {
        Log::error('failed job');
        $this->movie->update([
            'content' => $this->movie['content'],
            'prompt' => null
        ]);
    }

    public function callToChatGPT($prompt)
    {
        $openKey = $this->getRandomKeyValueFromCSV($this->keys) ?? getenv('OPENAI_API_KEY');

        $data = Http::timeout(120)->withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $openKey,
        ])->post("https://api.openai.com/v1/chat/completions", [
            "model" => "gpt-3.5-turbo",
            'messages' => [
                [
                    "role" => "system",
                    "content" => "You are a helpful assistant for writing SEO-friendly articles in Vietnamese. Please make sure the articles are well-structured, contain relevant keywords, and have a clear and concise writing style to improve search engine rankings."
                ],
                [
                    "role" => "user",
                    "content" => $prompt
                ]
            ],
            'temperature' => 0.5,
            'n' => 1,
            "max_tokens" => 2000,
            "top_p" => 1.0,
            "frequency_penalty" => 0.52,
            "presence_penalty" => 0.5,
            "stop" => ["11."],
        ])->json();

        $content = null;

        if (!empty($data) && isset($data['choices'])) {
            $content = $this->cutStringToMaxlengthWithEllipsis($data['choices'][0]['message']['content']);
        }

        return $content;
    }

    function cutStringToMaxlengthWithEllipsis($inputString, $maxLength = 2000) {
        if (mb_strlen($inputString) <= $maxLength) {
            return $inputString;
        }

        $truncatedString = mb_substr($inputString, 0, $maxLength);
        $lastDotIndex = mb_strrpos($truncatedString, '.');

        if ($lastDotIndex !== false && $lastDotIndex > $maxLength - 30) {
            $truncatedString = mb_substr($truncatedString, 0, $lastDotIndex + 1);
        }

        return $truncatedString . '...';
    }

    function getRandomKeyValueFromCSV($keys) {
        if (!empty($keys)) {
            // Lấy ngẫu nhiên một phần tử từ mảng $keys
            return $keys[array_rand($keys)];
        }
        return null;
    }
}
