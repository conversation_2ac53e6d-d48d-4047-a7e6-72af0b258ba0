<?php

namespace Ophim\ThemeVieon2025\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Movie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Ophim\Core\Models\Category;
use Ophim\Core\Models\Region;
use Ophim\Core\Models\Actor;
use Ophim\Core\Models\Director;
use Ophim\Core\Models\Studio;
use Ophim\Core\Models\Tag;

class HomeController extends Controller
{
    public function index()
    {
        // Lấy danh sách banner động từ option, cache lại đúng chuẩn codebase
        $home_page_slider_poster = Cache::remember('site.movies.home_page_slider_poster', setting('site_cache_ttl', 5 * 60), function () {
            $list = get_theme_option('home_page_slider_poster');
            $data = null;
            if(trim($list)) {
                $list = explode('|', $list);
                [$label, $relation, $field, $val, $sortKey, $alg, $limit] = array_merge($list, ['Phim đề cử', '', 'is_recommended', '1', 'updated_at', 'desc', 10]);
                try {
                    $data = [
                        'label' => $label,
                        'data' => Movie::when($relation, function ($query) use ($relation, $field, $val) {
                                $query->whereHas($relation, function ($rel) use ($field, $val) {
                                $rel->where($field, $val);
                            });
                        })
                        ->when(!$relation, function ($query) use ($field, $val) {
                            $query->where($field, $val);
                        })
                        ->orderBy($sortKey, $alg)
                        ->limit($limit)
                        ->get()
                    ];
                } catch (\Exception $e) {
                }
            }
            // fallback nếu không có option hoặc không có data
            if (empty($data) || empty($data['data']) || count($data['data']) == 0) {
                $data = [
                    'label' => 'Phim đề cử',
                    'data' => Movie::published()
                        ->where('is_recommended', 1)
                        ->orderBy('updated_at', 'desc')
                        ->limit(10)
                        ->get()
                ];
            }
            return $data;
        });

        // Lấy trending movies
        $trending_movies = Cache::remember('site.movies.trending', setting('site_cache_ttl', 5 * 60), function () {
            $list = get_theme_option('trending');
            $data = null;
            if(trim($list)) {
                $list = explode('|', $list);
                [$label, $relation, $field, $val, $sortKey, $alg, $limit] = array_merge($list, ['Thịnh hành', '', 'view_week', '>', '0', 'view_week', 'desc', 12]);
                try {
                    $data = Movie::when($relation, function ($query) use ($relation, $field, $val) {
                            $query->whereHas($relation, function ($rel) use ($field, $val) {
                            $rel->where($field, $val);
                        });
                    })
                    ->when(!$relation, function ($query) use ($field, $val) {
                        $query->where($field, '>', 0);
                    })
                    ->orderBy($sortKey, $alg)
                    ->limit($limit)
                    ->get();
                } catch (\Exception $e) {
                }
            }
            // fallback
            if (empty($data) || count($data) == 0) {
                $data = Movie::published()
                    ->where('view_week', '>', 0)
                    ->orderBy('view_week', 'desc')
                    ->limit(12)
                    ->get();
            }
            return $data;
        });

        // Lấy latest movies
        $latest_movies = Cache::remember('site.movies.latest', setting('site_cache_ttl', 5 * 60), function () {
            $list = get_theme_option('latest');
            $data = null;
            if(trim($list)) {
                $list = explode('|', $list);
                [$label, $relation, $field, $val, $sortKey, $alg, $limit] = array_merge($list, ['Mới nhất', '', 'updated_at', '>', '0', 'updated_at', 'desc', 12]);
                try {
                    $data = Movie::when($relation, function ($query) use ($relation, $field, $val) {
                            $query->whereHas($relation, function ($rel) use ($field, $val) {
                            $rel->where($field, $val);
                        });
                    })
                    ->when(!$relation, function ($query) use ($field, $val) {
                        $query->where($field, '>', 0);
                    })
                    ->orderBy($sortKey, $alg)
                    ->limit($limit)
                    ->get();
                } catch (\Exception $e) {
                }
            }
            // fallback
            if (empty($data) || count($data) == 0) {
                $data = Movie::published()
                    ->orderBy('updated_at', 'desc')
                    ->limit(12)
                    ->get();
            }
            return $data;
        });

        // Lấy sections động từ theme options
        $sections = Cache::remember('site.movies.sections', setting('site_cache_ttl', 5 * 60), function () {
            $lists = preg_split('/[\n\r]+/', get_theme_option('sections'));
            $data = [];
            foreach ($lists as $list) {
                if (trim($list)) {
                    $list = explode('|', $list);
                    [$label, $relation, $field, $val, $sortKey, $alg, $limit, $link, $template] = array_merge($list, ['Section', '', '', '', 'created_at', 'desc', 12, '', 'movie-slider']);
                    try {
                        $movies = Movie::when($relation, function ($query) use ($relation, $field, $val) {
                            $query->whereHas($relation, function ($rel) use ($field, $val) {
                                $rel->where($field, $val);
                            });
                        })
                        ->when(!$relation, function ($query) use ($field, $val) {
                            if ($field && $val) {
                                $query->where($field, $val);
                            }
                        })
                        ->orderBy($sortKey, $alg)
                        ->limit($limit)
                        ->get();

                        if ($movies->count() > 0) {
                            $data[] = [
                                'title' => $label,
                                'movies' => $movies,
                                'link' => $link ?: '#',
                                'template' => $template ?: 'movie-slider'
                            ];
                        }
                    } catch (\Exception $e) {
                        // Skip invalid sections
                    }
                }
            }
            return $data;
        });

        // Load user state for authenticated users
        if (Auth::check()) {
            // Load user state for slider movies
            if (isset($home_page_slider_poster['data'])) {
                $movieIds = $home_page_slider_poster['data']->pluck('id');
                $moviesWithState = Movie::whereIn('id', $movieIds)->withUserState()->get()->keyBy('id');
                $home_page_slider_poster['data'] = $home_page_slider_poster['data']->map(function($movie) use ($moviesWithState) {
                    $movieWithState = $moviesWithState->get($movie->id);
                    if ($movieWithState) {
                        $movie->userFavorite = $movieWithState->userFavorite;
                        $movie->userWatchlist = $movieWithState->userWatchlist;
                    }
                    return $movie;
                });
            }

            // Load user state for trending movies
            if ($trending_movies && count($trending_movies) > 0) {
                $movieIds = $trending_movies->pluck('id');
                $moviesWithState = Movie::whereIn('id', $movieIds)->withUserState()->get()->keyBy('id');
                $trending_movies = $trending_movies->map(function($movie) use ($moviesWithState) {
                    $movieWithState = $moviesWithState->get($movie->id);
                    if ($movieWithState) {
                        $movie->userFavorite = $movieWithState->userFavorite;
                        $movie->userWatchlist = $movieWithState->userWatchlist;
                    }
                    return $movie;
                });
            }

            // Load user state for latest movies
            if ($latest_movies && count($latest_movies) > 0) {
                $movieIds = $latest_movies->pluck('id');
                $moviesWithState = Movie::whereIn('id', $movieIds)->withUserState()->get()->keyBy('id');
                $latest_movies = $latest_movies->map(function($movie) use ($moviesWithState) {
                    $movieWithState = $moviesWithState->get($movie->id);
                    if ($movieWithState) {
                        $movie->userFavorite = $movieWithState->userFavorite;
                        $movie->userWatchlist = $movieWithState->userWatchlist;
                    }
                    return $movie;
                });
            }

            // Load user state for sections
            foreach ($sections as &$section) {
                if (isset($section['movies']) && count($section['movies']) > 0) {
                    $movieIds = $section['movies']->pluck('id');
                    $moviesWithState = Movie::whereIn('id', $movieIds)->withUserState()->get()->keyBy('id');
                    $section['movies'] = $section['movies']->map(function($movie) use ($moviesWithState) {
                        $movieWithState = $moviesWithState->get($movie->id);
                        if ($movieWithState) {
                            $movie->userFavorite = $movieWithState->userFavorite;
                            $movie->userWatchlist = $movieWithState->userWatchlist;
                        }
                        return $movie;
                    });
                }
            }
        }

        // Merge all data
        $data = [
            'home_page_slider_poster' => $home_page_slider_poster,
            'trending_movies' => $trending_movies,
            'latest_movies' => $latest_movies,
            'sections' => $sections
        ];

        return view('themevieon2025::home', $data);
    }

    public function search(Request $request)
    {
        $keyword = $request->get('keyword');

        if (empty($keyword)) {
            return redirect()->route('home');
        }

        $movies = Movie::published()
            ->withUserState()
            ->where(function($query) use ($keyword) {
                $query->where('name', 'like', "%{$keyword}%")
                    ->orWhere('origin_name', 'like', "%{$keyword}%")
                    ->orWhere('content', 'like', "%{$keyword}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return view('themevieon2025::pages.search', [
            'keyword' => $keyword,
            'movies' => $movies
        ]);
    }

    public function category($slug)
    {
        $category = Category::where('slug', $slug)->firstOrFail();

        $movies = $category->movies()
            ->published()
            ->withUserState()
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return view('themevieon2025::category', [
            'category' => $category,
            'movies' => $movies
        ]);
    }

    public function region($slug)
    {
        $region = Region::where('slug', $slug)->firstOrFail();

        $movies = $region->movies()
            ->published()
            ->withUserState()
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return view('themevieon2025::region', [
            'region' => $region,
            'movies' => $movies
        ]);
    }

    public function actor($slug)
    {
        $actor = Actor::where('slug', $slug)->firstOrFail();

        $movies = $actor->movies()
            ->published()
            ->withUserState()
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return view('themevieon2025::actor', [
            'actor' => $actor,
            'movies' => $movies
        ]);
    }

    public function director($slug)
    {
        $director = Director::where('slug', $slug)->firstOrFail();

        $movies = $director->movies()
            ->published()
            ->withUserState()
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return view('themevieon2025::director', [
            'director' => $director,
            'movies' => $movies
        ]);
    }

    public function studio($slug)
    {
        $studio = Studio::where('slug', $slug)->firstOrFail();

        $movies = $studio->movies()
            ->published()
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return view('themevieon2025::studio', [
            'studio' => $studio,
            'movies' => $movies
        ]);
    }

    public function tag($slug)
    {
        $tag = Tag::where('slug', $slug)->firstOrFail();

        $movies = $tag->movies()
            ->published()
            ->orderBy('created_at', 'desc')
            ->paginate(24);

        return view('themevieon2025::tag', [
            'tag' => $tag,
            'movies' => $movies
        ]);
    }

    public function movie($slug)
    {
        $movie = Movie::where('slug', $slug)->firstOrFail();

        // Increment view count
        $movie->increment('view_total');
        $movie->increment('view_day');
        $movie->increment('view_week');
        $movie->increment('view_month');

        // Get related movies
        $related_movies = Cache::remember("movie_related_{$movie->id}", setting('site_cache_ttl', 5 * 60), function () use ($movie) {
            return $movie->categories->first()->movies()
                ->where('id', '!=', $movie->id)
                ->published()
                ->orderBy('view_total', 'desc')
                ->limit(12)
                ->get();
        });

        return view('themevieon2025::movie', [
            'movie' => $movie,
            'related_movies' => $related_movies
        ]);
    }

    public function episode($movieSlug, $episodeSlug)
    {
        $movie = Movie::where('slug', $movieSlug)->firstOrFail();
        $episode = $movie->episodes()->where('slug', $episodeSlug)->firstOrFail();

        // Increment view count
        $movie->increment('view_total');
        $movie->increment('view_day');
        $movie->increment('view_week');
        $movie->increment('view_month');

        // Get related movies
        $related_movies = Cache::remember("movie_related_{$movie->id}", setting('site_cache_ttl', 5 * 60), function () use ($movie) {
            return $movie->categories->first()->movies()
                ->where('id', '!=', $movie->id)
                ->published()
                ->orderBy('view_total', 'desc')
                ->limit(12)
                ->get();
        });

        return view('themevieon2025::episode', [
            'movie' => $movie,
            'episode' => $episode,
            'related_movies' => $related_movies
        ]);
    }
}
