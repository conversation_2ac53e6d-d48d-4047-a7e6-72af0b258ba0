<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Helpers\MigrationHelper;

return new class extends Migration
{
    public function up()
    {
        MigrationHelper::safeCreateTable('comments', function (Blueprint $table) {
            // Add episode_id column after movie_id
            $table->unsignedBigInteger('episode_id')->nullable()->after('movie_id');

            // Add foreign key constraint
            $table->foreign('episode_id')->references('id')->on('episodes')->onDelete('cascade');

            // Add indexes for performance
            $table->index(['movie_id', 'episode_id'], 'idx_comments_movie_episode');
            $table->index(['episode_id'], 'idx_comments_episode');
        });
    }

    public function down()
    {
        Schema::table('comments', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex('idx_comments_movie_episode');
            $table->dropIndex('idx_comments_episode');

            // Drop foreign key constraint
            $table->dropForeign(['episode_id']);

            // Drop column
            $table->dropColumn('episode_id');
        });
    }
};
