<!-- Report Episode Modal -->
<div x-data="{
    showReportModal: false,
    reportForm: {
        reason: '',
        message: '',
        loading: false
    },
    reasons: [
        '<PERSON> không hoạt động',
        '<PERSON><PERSON><PERSON> lượ<PERSON> kém',
        '<PERSON><PERSON><PERSON><PERSON> có phụ đề',
        '<PERSON><PERSON> đề sai',
        '<PERSON><PERSON> thanh có vấn đề',
        '<PERSON> bị lag',
        '<PERSON><PERSON><PERSON><PERSON> cáo quá nhiều',
        'Nội dung không phù hợp',
        '<PERSON><PERSON><PERSON><PERSON>'
    ]
}"
     @show-report-modal.window="showReportModal = true"
     @keydown.escape.window="showReportModal = false"
     class="relative z-50">

    <!-- Modal Backdrop -->
    <div x-show="showReportModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
         @click="showReportModal = false">
    </div>

    <!-- Modal Content -->
    <div x-show="showReportModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
         x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
         x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
         class="fixed inset-0 z-50 overflow-y-auto">

        <div class="flex min-h-full items-center justify-center p-4">
            <div
                class="relative w-full max-w-md transform overflow-hidden rounded-lg bg-theme-secondary shadow-xl transition-all">

                <!-- Modal Header -->
                <div class="flex items-center justify-between p-4 border-b border-theme-primary">
                    <h3 class="text-lg font-semibold text-theme-primary">
                        <i class="fas fa-flag mr-2 text-red-500"></i>
                        Báo cáo tập phim
                    </h3>
                    <button @click="showReportModal = false"
                            class="text-theme-tertiary hover:text-theme-primary transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- Modal Body -->
                <form class="ajax-form"
                      action="{{ route('episodes.report', [$movie->slug, $episode->slug]) }}"
                      method="POST"
                      @submit.prevent="submitReport">
                    @csrf

                    <div class="p-4 space-y-4">
                        <!-- Episode Info -->
                        <div class="bg-theme-dropdown rounded-lg p-3">
                            <div class="flex items-center gap-3">
                                <img src="{{ $movie->thumb_url }}" alt="{{ $movie->name }}"
                                     class="w-12 h-16 object-cover rounded">
                                <div>
                                    <h4 class="font-medium text-theme-primary">{{ $movie->name }}</h4>
                                    <p class="text-sm text-theme-tertiary">{{ $episode->name }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Reason Selection -->
                        <div>
                            <label class="block text-sm font-medium text-theme-primary mb-2">
                                Lý do báo cáo <span class="text-red-500">*</span>
                            </label>
                            <select name="reason"
                                    x-model="reportForm.reason"
                                    required
                                    class="w-full px-3 py-2 bg-theme-dropdown border border-theme-primary rounded-lg text-theme-primary focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent">
                                <option value="">Chọn lý do</option>
                                <template x-for="reason in reasons" :key="reason">
                                    <option :value="reason" x-text="reason"></option>
                                </template>
                            </select>
                        </div>

                        <!-- Message -->
                        <div>
                            <label class="block text-sm font-medium text-theme-primary mb-2">
                                Mô tả chi tiết <span class="text-red-500">*</span>
                            </label>
                            <textarea name="message"
                                      x-model="reportForm.message"
                                      required
                                      rows="4"
                                      maxlength="500"
                                      placeholder="Vui lòng mô tả chi tiết vấn đề bạn gặp phải..."
                                      class="w-full px-3 py-2 bg-theme-dropdown border border-theme-primary rounded-lg text-theme-primary placeholder-theme-tertiary focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent resize-none"></textarea>
                            <div class="text-right text-xs text-theme-tertiary mt-1">
                                <span x-text="reportForm.message.length"></span>/500
                            </div>
                        </div>

                        <!-- Warning -->
                        <div class="bg-yellow-500 bg-opacity-10 border border-yellow-500 rounded-lg p-3">
                            <div class="flex items-start gap-2">
                                <i class="fas fa-exclamation-triangle text-yellow-500 mt-0.5"></i>
                                <div class="text-sm text-theme-primary">
                                    <p class="font-medium mb-1">Lưu ý:</p>
                                    <ul class="text-xs text-theme-tertiary space-y-1">
                                        <li>• Chỉ báo cáo khi thực sự có vấn đề với tập phim</li>
                                        <li>• Báo cáo sai có thể dẫn đến hạn chế tài khoản</li>
                                        <li>• Chúng tôi sẽ xử lý trong vòng 24h</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Modal Footer -->
                    <div class="flex items-center justify-end gap-3 p-4 border-t border-theme-primary">
                        <button type="button"
                                @click="showReportModal = false"
                                class="px-4 py-2 text-theme-tertiary hover:text-theme-primary transition-colors">
                            Hủy
                        </button>
                        <button type="submit"
                                :disabled="reportForm.loading || !reportForm.reason || !reportForm.message"
                                :class="reportForm.loading ? 'opacity-50 cursor-not-allowed' : ''"
                                class="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <span x-show="!reportForm.loading">Gửi báo cáo</span>
                            <span x-show="reportForm.loading" class="flex items-center gap-2">
                                <i class="fas fa-spinner fa-spin"></i>
                                Đang gửi...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function submitReport(event) {
        const form = event.target;
        const formData = new FormData(form);

        // Set loading state
        this.reportForm.loading = true;

        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.status) {
                    // Show success notification
                    window.dispatchEvent(new CustomEvent('show-notification', {
                        detail: {
                            type: 'success',
                            message: data.message
                        }
                    }));

                    // Reset form and close modal
                    this.reportForm = {reason: '', message: '', loading: false};
                    this.showReportModal = false;
                } else {
                    throw new Error(data.message || 'Có lỗi xảy ra');
                }
            })
            .catch(error => {
                // Show error notification
                window.dispatchEvent(new CustomEvent('show-notification', {
                    detail: {
                        type: 'error',
                        message: error.message || 'Có lỗi xảy ra khi gửi báo cáo'
                    }
                }));
            })
            .finally(() => {
                this.reportForm.loading = false;
            });
    }
</script>
