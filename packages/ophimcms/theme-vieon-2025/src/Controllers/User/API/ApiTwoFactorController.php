<?php

namespace Ophim\ThemeVieon2025\Controllers\User\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Mail;

class ApiTwoFactorController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    public function apiSend2faCode(Request $request)
    {
        $user = Auth::user();
        $code = rand(100000, 999999);
        
        Session::put('2fa_code', $code);
        Session::put('2fa_expires', now()->addMinutes(10));

        // Gửi code qua email
        Mail::raw('Mã xác thực 2 lớp của bạn là: ' . $code, function ($message) use ($user) {
            $message->to($user->email)->subject('Mã xác thực 2 lớp');
        });

        return response()->json([
            'status' => true,
            'message' => '<PERSON>ã gửi mã xác thực 2 lớp tới email của bạn!'
        ]);
    }

    public function apiVerify2fa(Request $request)
    {
        $request->validate(['code' => 'required|digits:6']);

        $code = Session::get('2fa_code');
        $expires = Session::get('2fa_expires');

        if (!$code || !$expires || now()->gt($expires)) {
            return response()->json([
                'status' => false,
                'message' => 'Mã xác thực đã hết hạn, vui lòng gửi lại!'
            ], 422);
        }

        if ($request->code != $code) {
            return response()->json([
                'status' => false,
                'message' => 'Mã xác thực không đúng!'
            ], 422);
        }

        Session::forget(['2fa_code', '2fa_expires']);
        Session::put('2fa_passed', true);

        return response()->json([
            'status' => true,
            'message' => 'Xác thực 2 lớp thành công!'
        ]);
    }
} 