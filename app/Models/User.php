<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Auth\MustVerifyEmail as MustVerifyEmailTrait;
use Illuminate\Support\Facades\URL;

use Ophim\Core\Models\User as OphimUser;

class User extends OphimUser implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, MustVerifyEmailTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar',
        'birthday',
        'gender',
        'language',
        'subtitle',
        'auto_play_next',
        'save_watch_history',
        'notify_new_movie',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'auto_play_next' => 'boolean',
        'save_watch_history' => 'boolean',
        'notify_new_movie' => 'boolean',
    ];

    /**
     * Get user avatar URL with fallback
     *
     * @return string
     */
    public function getAvatar(): string
    {
        if ($this->avatar) {
            // Nếu là file mẫu (chỉ tên file, không có thư mục)
            if (preg_match('/^([0-9]{2})\.jpg$/', $this->avatar) && !str_contains($this->avatar, '/')) {
                return asset('/themes/vieon-2025/images/avatars/' . $this->avatar);
            }
            // Nếu là upload (có đường dẫn thư mục)
            return asset('storage/' . $this->avatar);
        }
        // Fallback UI Avatars
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&background=random&color=ffffff';
    }

    /**
     * Get the verification URL for the user.
     *
     * @return string
     */
    public function getEmailForVerification()
    {
        return $this->email;
    }

    /**
     * Determine if the user has verified their email address.
     *
     * @return bool
     */
    public function hasVerifiedEmail()
    {
        return ! is_null($this->email_verified_at);
    }

    /**
     * Mark the given user's email as verified.
     *
     * @return bool
     */
    public function markEmailAsVerified()
    {
        return $this->forceFill([
            'email_verified_at' => $this->freshTimestamp(),
        ])->save();
    }

    /**
     * Send the email verification notification.
     *
     * @return void
     */
    public function sendEmailVerificationNotification()
    {
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            [
                'id' => $this->getKey(),
                'hash' => sha1($this->getEmailForVerification()),
            ]
        );

        $this->notify(new \Illuminate\Auth\Notifications\VerifyEmail($verificationUrl));
    }

    /**
     * User's favorite movies relationship
     */
    public function favorites()
    {
        return $this->hasMany(\Ophim\ThemeVieon2025\Models\Favorite::class);
    }

    /**
     * User's viewing history relationship
     */
    public function histories()
    {
        return $this->hasMany(\Ophim\ThemeVieon2025\Models\History::class);
    }

    /**
     * User's movie ratings relationship
     */
    public function ratings()
    {
        return $this->hasMany(\Ophim\ThemeVieon2025\Models\Rating::class);
    }

    /**
     * User's watchlist relationship
     */
    public function watchlist()
    {
        return $this->hasMany(\Ophim\ThemeVieon2025\Models\Watchlist::class);
    }

    /**
     * Get user's favorite movies
     */
    public function favoriteMovies()
    {
        return $this->belongsToMany(\App\Models\Movie::class, 'favorites', 'user_id', 'movie_id')
            ->withTimestamps();
    }

    /**
     * Get user's watchlist movies
     */
    public function watchlistMovies()
    {
        return $this->belongsToMany(\App\Models\Movie::class, 'watchlists', 'user_id', 'movie_id')
            ->withTimestamps();
    }
}
