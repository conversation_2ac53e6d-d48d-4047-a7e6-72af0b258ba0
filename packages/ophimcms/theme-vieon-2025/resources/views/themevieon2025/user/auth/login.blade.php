@extends('themevieon2025::layouts.app')

@section('title', 'Đăng nhập - ' . config('app.name') . ' 2025')

@section('content')
    <div class="auth-page-container bg-gradient-to-br from-main/10 to-main/5">
        <div class="user-form-container">
            <div class="text-center mb-8">
                <div class="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-main/20 mb-4">
                    <svg class="h-10 w-10 text-main" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-extrabold text-theme-primary mb-2">
                    Đ<PERSON><PERSON> nhập vào {{ config('app.name') }}
                </h2>
                <p class="text-theme-tertiary">
                    Hoặc
                    <a href="{{ route('register') }}"
                       class="font-medium text-main hover:text-main/80 transition-colors">
                        đăng ký tài khoản mới
                    </a>
                </p>
            </div>

            <form class="space-y-6 ajax-form" action="{{ route('api.login') }}" method="POST">
                @csrf
                <div class="space-y-4">
                    <div>
                        <label for="email" class="block text-sm font-medium text-theme-secondary mb-2">Email</label>
                        <input id="email" name="email" type="email" autocomplete="email" required
                               class="w-full px-4 py-3 bg-theme-tertiary border border-theme-input rounded-lg text-theme-primary placeholder-theme-muted focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all"
                               placeholder="Nhập email của bạn" value="{{ old('email') }}">
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-theme-secondary mb-2">Mật
                            khẩu</label>
                        <input id="password" name="password" type="password" autocomplete="current-password" required
                               class="w-full px-4 py-3 bg-theme-tertiary border border-theme-input rounded-lg text-theme-primary placeholder-theme-muted focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all"
                               placeholder="Nhập mật khẩu">
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember" name="remember" type="checkbox"
                               class="h-4 w-4 text-main focus:ring-main border-theme-input rounded bg-theme-tertiary">
                        <label for="remember" class="ml-2 block text-sm text-theme-secondary">
                            Ghi nhớ đăng nhập
                        </label>
                    </div>

                    <div class="text-sm">
                        <a href="#" class="font-medium text-main hover:text-main/80 transition-colors">
                            Quên mật khẩu?
                        </a>
                    </div>
                </div>

                <button type="submit"
                        class="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-black bg-main hover:bg-main/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main transition-all duration-200 transform hover:scale-105">
                    <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    Đăng nhập
                </button>
            </form>
        </div>
    </div>
@endsection
