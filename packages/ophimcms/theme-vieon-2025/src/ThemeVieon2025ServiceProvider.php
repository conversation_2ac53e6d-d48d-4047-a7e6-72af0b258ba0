<?php

namespace Ophim\ThemeVieon2025;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class ThemeVieon2025ServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->setupDefaultThemeCustomizer();
    }

    public function boot()
    {
        $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
        $this->loadRoutesFrom(__DIR__ . '/../routes/api.php');
        $this->loadRoutesFrom(__DIR__ . '/../routes/admin.php');
        $this->loadViewsFrom(__DIR__ . '/../resources/views/admin', 'theme-vieon-2025');
        $this->loadViewsFrom(__DIR__ . '/../resources/views/themevieon2025', 'themevieon2025');
        $this->loadTranslationsFrom(__DIR__ . '/../resources/lang', 'themevieon2025');
        $this->loadMigrationsFrom(__DIR__ . '/../database/migrations');

        $this->publishes([
            __DIR__ . '/../resources/assets' => public_path('themes/vieon-2025'),
            __DIR__ . '/Config/theme.php' => config_path('themes/vieon-2025/config.php'),
            __DIR__ . '/Config/loading_themes.php' => config_path('themes/vieon-2025/loading_themes.php'),
        ], 'vieon-2025-assets');

        // Publish translation files to Laravel's lang directory
        $this->publishes([
            __DIR__ . '/../resources/lang' => resource_path('lang'),
        ], 'vieon-2025-lang');

        // Register commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                \Ophim\ThemeVieon2025\Console\Commands\SeedLoadingThemes::class,
            ]);
        }
    }

    protected function setupDefaultThemeCustomizer()
    {
        config(['themes' => array_merge(config('themes', []), [
            'vieon-2025' => [
                'name' => 'Theme Vieon 2025',
                'author' => '<EMAIL>',
                'package_name' => 'ophimcms/theme-vieon-2025',
                'publishes' => ['vieon-2025-assets'],
                'preview_image' => '',
                'options' => [
                    [
                        'name' => 'recommendations_limit',
                        'label' => 'Recommended movies limit',
                        'type' => 'number',
                        'value' => 10,
                        'wrapperAttributes' => [
                            'class' => 'form-group col-md-4',
                        ],
                        'tab' => 'List',
                    ],
                    [
                        'name' => 'per_page_limit',
                        'label' => 'Pages limit',
                        'type' => 'number',
                        'value' => 18,
                        'wrapperAttributes' => [
                            'class' => 'form-group col-md-4',
                        ],
                        'tab' => 'List',
                    ],
                    [
                        'name' => 'movie_related_limit',
                        'label' => 'Movies related limit',
                        'type' => 'number',
                        'value' => 12,
                        'wrapperAttributes' => [
                            'class' => 'form-group col-md-4',
                        ],
                        'tab' => 'List',
                    ],
                    [
                        'name' => 'latest',
                        'label' => 'Home Page',
                        'type' => 'code',
                        'hint' => 'Label|relation|find_by_field|value|sort_by_field|sort_algo|limit|show_more_url',
                        'value' => "Phim lẻ mới||type|single|updated_at|desc|12|/danh-sach/phim-le\r\nPhim bộ mới||type|series|updated_at|desc|12|/danh-sach/phim-bo\r\nPhim thịnh hành||is_copyright|0|view_week|desc|12|",
                        'attributes' => [
                            'rows' => 5,
                        ],
                        'tab' => 'List',
                    ],
                    [
                        'name' => 'hotest',
                        'label' => 'Hot Movies (Menu)',
                        'type' => 'code',
                        'hint' => 'Label|relation|find_by_field|value|sort_by_field|sort_algo|limit (one per line)',
                        'value' => "Phim hot||type|series|view_total|desc|8",
                        'attributes' => [
                            'rows' => 3,
                        ],
                        'tab' => 'List',
                    ],
                    [
                        'name' => 'home_page_slider_poster',
                        'label' => 'Hero Slider',
                        'type' => 'code',
                        'hint' => 'Label|relation|find_by_field|value|sort_by_field|sort_algo|limit',
                        'value' => "Phim đề cử||is_recommended|1|updated_at|desc|10",
                        'attributes' => [
                            'rows' => 2,
                        ],
                        'tab' => 'List',
                    ],
                    [
                        'name' => 'user_features',
                        'label' => 'User Features',
                        'type' => 'code',
                        'hint' => 'Nhập các tính năng người dùng, mỗi dòng 1 key. Ví dụ: favorites, watchlist, ratings, history, recommendations. Các key hợp lệ: favorites, watchlist, ratings, history, recommendations.',
                        'value' => "favorites\nwatchlist\nratings\nhistory\nrecommendations",
                        'attributes' => [
                            'rows' => 5,
                        ],
                        'tab' => 'User Features',
                    ],
                    [
                        'name' => 'auth_settings',
                        'label' => 'Authentication Settings',
                        'type' => 'code',
                        'hint' => 'Nhập các tuỳ chọn xác thực, mỗi dòng 1 key. Ví dụ: enable_registration, remember_me. Các key hợp lệ: enable_registration, email_verification, social_login, remember_me.',
                        'value' => "enable_registration\nremember_me",
                        'attributes' => [
                            'rows' => 4,
                        ],
                        'tab' => 'Authentication',
                    ],
                    [
                        'name' => 'additional_css',
                        'label' => 'Additional CSS',
                        'type' => 'code',
                        'value' => "",
                        'tab' => 'Custom CSS',
                    ],
                    [
                        'name' => 'body_attributes',
                        'label' => 'Body attributes',
                        'type' => 'text',
                        'value' => "",
                        'tab' => 'Custom CSS',
                    ],
                    [
                        'name' => 'additional_header_js',
                        'label' => 'Header JS',
                        'type' => 'code',
                        'value' => "",
                        'tab' => 'Custom JS',
                    ],
                    [
                        'name' => 'additional_body_js',
                        'label' => 'Body JS',
                        'type' => 'code',
                        'value' => "",
                        'tab' => 'Custom JS',
                    ],
                    [
                        'name' => 'additional_footer_js',
                        'label' => 'Footer JS',
                        'type' => 'code',
                        'value' => "",
                        'tab' => 'Custom JS',
                    ],
                    [
                        'name' => 'footer',
                        'label' => 'Footer',
                        'type' => 'code',
                        'value' => <<<EOT
                        <div class="bg-gradient-to-r from-main/20 to-main/10 p-6 rounded-lg">
                            <div class="text-center">
                                <h3 class="text-xl font-bold text-main mb-4">🎬 VieON - Nền Tảng Xem Phim Hàng Đầu</h3>
                                <p class="text-gray-300 mb-4">
                                    Khám phá kho phim khổng lồ với hàng nghìn bộ phim chất lượng cao,
                                    cập nhật liên tục và trải nghiệm xem phim tuyệt vời nhất.
                                </p>
                                <div class="flex justify-center space-x-4">
                                    <span class="text-main font-semibold">📺 Phim Bộ</span>
                                    <span class="text-main font-semibold">🎭 Phim Lẻ</span>
                                    <span class="text-main font-semibold">🎨 Hoạt Hình</span>
                                    <span class="text-main font-semibold">📺 TV Show</span>
                                </div>
                            </div>
                        </div>
                        EOT,
                        'tab' => 'Custom HTML',
                    ],
                    [
                        'name' => 'footer_style',
                        'label' => 'Footer Style',
                        'type' => 'select_from_array',
                        'options' => [
                            'compact' => 'Compact (Centered, minimal)',
                            'full' => 'Full (4 columns, detailed)'
                        ],
                        'default' => 'compact',
                        'tab' => 'Custom HTML',
                    ],
                    [
                        'name' => 'menu_tabs',
                        'label' => 'Menu Tabs Configuration',
                        'type' => 'code',
                        'hint' => 'Configure menu tabs. Format: tab_id|label|icon|type|limit (one per line)',
                        'value' => "categories|Thể Loại|fas fa-th-large|categories|16\nhot|Phim Hot|fas fa-fire|hot|10\nregions|Quốc Gia|fas fa-globe|regions|16\ntypes|Định Dạng|fas fa-film|types|4",
                        'attributes' => [
                            'rows' => 5,
                        ],
                        'tab' => 'Custom HTML',
                    ],
                    [
                        'name' => 'ads_header',
                        'label' => 'Ads header',
                        'type' => 'code',
                        'value' => <<<EOT
                        <img src="" alt="">
                        EOT,
                        'tab' => 'Ads',
                    ],
                    [
                        'name' => 'ads_catfish',
                        'label' => 'Ads catfish',
                        'type' => 'code',
                        'value' => <<<EOT
                        <img src="" alt="">
                        EOT,
                        'tab' => 'Ads',
                    ],
                ],
            ],
        ])]);
    }
}
