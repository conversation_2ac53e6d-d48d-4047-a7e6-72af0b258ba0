<?php

namespace Ophim\ThemeVieon2025\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Ophim\Core\Models\Movie;
use Ophim\Core\Models\Episode;
use Ophim\ThemeVieon2025\Models\Comment;

class EpisodeController extends Controller
{
    public function show($movie, $episode)
    {
        $movie = Movie::with(['categories', 'regions', 'type', 'episodes'])
            ->where('slug', $movie)
            ->firstOrFail();

        $episode = Episode::where('movie_id', $movie->id)
            ->where('slug', $episode)
            ->firstOrFail();

        $nextEpisode = Episode::where('movie_id', $movie->id)
            ->where('order', '>', $episode->order)
            ->orderBy('order', 'asc')
            ->first();

        $prevEpisode = Episode::where('movie_id', $movie->id)
            ->where('order', '<', $episode->order)
            ->orderBy('order', 'desc')
            ->first();

        // Increment view count
        $movie->increment('view_total');
        $movie->increment('view_day');
        $movie->increment('view_week');
        $movie->increment('view_month');

        // Get episode-level comments
        $comments = Comment::where('movie_id', $movie->id)
            ->where('episode_id', $episode->id)
            ->whereNull('parent_id')
            ->with('children', 'user')
            ->latest()
            ->paginate(10);

        return view('themevieon2025::pages.episode', [
            'movie' => $movie,
            'episode' => $episode,
            'nextEpisode' => $nextEpisode,
            'prevEpisode' => $prevEpisode,
            'comments' => $comments
        ]);
    }

    /**
     * Report an episode (AJAX endpoint)
     */
    public function reportEpisode(Request $request, $movieSlug, $episodeSlug)
    {
        // Validate request
        $request->validate([
            'message' => 'required|string|max:500',
            'reason' => 'nullable|string|max:100'
        ]);

        // Find movie and episode
        $movie = Movie::where('slug', $movieSlug)->firstOrFail();
        $episode = Episode::where('movie_id', $movie->id)
            ->where('slug', $episodeSlug)
            ->firstOrFail();

        // Update episode with report information
        $episode->update([
            'report_message' => $request->message,
            'report_reason' => $request->reason ?? 'Khác',
            'has_report' => true,
            'reported_at' => now()
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Báo cáo tập phim thành công! Chúng tôi sẽ kiểm tra và xử lý sớm nhất.',
            'data' => [
                'episode_id' => $episode->id,
                'reported_at' => $episode->reported_at->format('d/m/Y H:i')
            ]
        ]);
    }
}
