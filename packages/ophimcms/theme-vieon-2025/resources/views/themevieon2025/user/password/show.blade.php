@extends('themevieon2025::layouts.user')

@section('title', 'Đ<PERSON>i mật khẩu - ' . config('app.name') . ' 2025')

@section('user-content')
    <div class="max-w-2xl mx-auto">
        <div class="text-center mb-8">
            <div class="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-main/20 mb-4">
                <svg class="h-10 w-10 text-main" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"></path>
                </svg>
            </div>
            <h2 class="text-3xl font-bold text-white mb-2"><PERSON><PERSON><PERSON> m<PERSON><PERSON> khẩ<PERSON></h2>
            <p class="text-gray-400">Cậ<PERSON> nh<PERSON><PERSON> mật khẩu tà<PERSON> kho<PERSON>n của b<PERSON>n</p>
        </div>

        {{-- Loading Overlay --}}
        <div id="password-loading"
             class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
            <div class="bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-main"></div>
                <span class="text-white">Đang cập nhật...</span>
            </div>
        </div>

        {{-- Message Containers --}}
        <div id="password-error"
             class="hidden mb-6 bg-red-500 bg-opacity-20 border border-red-500 text-red-200 px-4 py-3 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                          clip-rule="evenodd"></path>
                </svg>
                <span id="password-error-message"></span>
            </div>
        </div>

        <div id="password-success"
             class="hidden mb-6 bg-green-500 bg-opacity-20 border border-green-500 text-green-200 px-4 py-3 rounded-lg">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clip-rule="evenodd"></path>
                </svg>
                <span id="password-success-message"></span>
            </div>
        </div>

        <form id="password-form" class="space-y-6">
            @csrf

            <!-- Current Password -->
            <div>
                <label for="current_password" class="block text-sm font-medium text-theme-secondary mb-2">Mật khẩu hiện
                    tại</label>
                <input type="password" name="current_password" id="current_password" required
                       autocomplete="current-password"
                       class="w-full px-4 py-3 bg-theme-tertiary border border-theme-input rounded-lg text-theme-primary placeholder-theme-muted focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all"
                       placeholder="Nhập mật khẩu hiện tại">
            </div>

            <!-- New Password -->
            <div>
                <label for="new_password" class="block text-sm font-medium text-theme-secondary mb-2">Mật khẩu
                    mới</label>
                <input type="password" name="new_password" id="new_password" required autocomplete="new-password"
                       class="w-full px-4 py-3 bg-theme-tertiary border border-theme-input rounded-lg text-theme-primary placeholder-theme-muted focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all"
                       placeholder="Nhập mật khẩu mới">
            </div>

            <!-- Confirm New Password -->
            <div>
                <label for="new_password_confirmation" class="block text-sm font-medium text-theme-secondary mb-2">Xác
                    nhận mật khẩu mới</label>
                <input type="password" name="new_password_confirmation" id="new_password_confirmation" required
                       autocomplete="new-password"
                       class="w-full px-4 py-3 bg-theme-tertiary border border-theme-input rounded-lg text-theme-primary placeholder-theme-muted focus:outline-none focus:ring-2 focus:ring-main focus:border-transparent transition-all"
                       placeholder="Nhập lại mật khẩu mới">
            </div>

            <button type="submit"
                    class="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-theme-inverse bg-main hover:bg-main/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main transition-all duration-200 transform hover:scale-105">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"></path>
                </svg>
                Đổi mật khẩu
            </button>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('password-form');

            if (form) {
                form.addEventListener('submit', async function (e) {
                    e.preventDefault();

                    // Hide previous messages
                    hideMessage();

                    // Show loading
                    showLoading(true);

                    // Get form data
                    const formData = new FormData(form);
                    const passwordData = {};

                    // Convert FormData to object
                    for (let [key, value] of formData.entries()) {
                        passwordData[key] = value;
                    }

                    try {
                        // Change password via API
                        const response = await window.userAPI.changePassword(passwordData);

                        // If we reach here, the request was successful
                        console.log('Password change response:', response);
                        showSuccess(response.message || 'Mật khẩu đã được thay đổi thành công');

                        // Clear form
                        form.reset();

                    } catch (error) {
                        console.error('Error changing password:', error);

                        // Parse error message from response
                        let errorMessage = 'Có lỗi xảy ra khi thay đổi mật khẩu';

                        if (error.message) {
                            try {
                                // Try to parse JSON error response
                                const errorData = JSON.parse(error.message);
                                if (errorData.errors) {
                                    // Format validation errors
                                    const errorList = [];
                                    for (const field in errorData.errors) {
                                        errorList.push(...errorData.errors[field]);
                                    }
                                    errorMessage = errorList.join(', ');
                                } else if (errorData.message) {
                                    errorMessage = errorData.message;
                                }
                            } catch (parseError) {
                                // If not JSON, use the original message
                                errorMessage = error.message;
                            }
                        }

                        showError(errorMessage);
                    } finally {
                        showLoading(false);
                    }
                });
            }
        });

        function showLoading(show) {
            const loadingEl = document.getElementById('password-loading');
            if (show) {
                loadingEl.classList.remove('hidden');
            } else {
                loadingEl.classList.add('hidden');
            }
        }

        function showError(message) {
            const errorEl = document.getElementById('password-error');
            const messageEl = document.getElementById('password-error-message');

            if (!errorEl || !messageEl) {
                console.error('Error elements not found');
                return;
            }

            messageEl.textContent = message;
            errorEl.classList.remove('hidden');

            // Auto hide after 5 seconds
            setTimeout(() => {
                errorEl.classList.add('hidden');
            }, 5000);
        }

        function showSuccess(message) {
            const successEl = document.getElementById('password-success');
            const messageEl = document.getElementById('password-success-message');

            if (!successEl || !messageEl) {
                console.error('Success elements not found');
                return;
            }

            messageEl.textContent = message;
            successEl.classList.remove('hidden');

            // Auto hide after 3 seconds
            setTimeout(() => {
                successEl.classList.add('hidden');
            }, 3000);
        }

        function hideMessage() {
            document.getElementById('password-error').classList.add('hidden');
            document.getElementById('password-success').classList.add('hidden');
        }
    </script>
@endpush
