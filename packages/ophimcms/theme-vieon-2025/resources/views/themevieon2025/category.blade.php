@extends('themevieon2025::layouts.app')

@section('title', 'Phim ' . $category->name . ' - ' . setting('site_meta_title', 'VieOn 2025'))
@section('description', 'Xem phim ' . $category->name . ' hay nhất, mới nhất. Danh sách phim ' . $category->name . ' chấ<PERSON> l<PERSON> HD miễn phí.')

@section('content')
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h1 class="text-2xl font-semibold text-gray-900 mb-6">{{ $category->name }}</h1>

                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        @foreach($movies as $movie)
                            <div class="bg-white rounded-lg shadow overflow-hidden">
                                <a href="{{ route('movies.show', $movie->slug) }}">
                                    <img src="{{ $movie->thumb_url }}" alt="{{ $movie->name }}"
                                         class="w-full h-48 object-cover">
                                    <div class="p-4">
                                        <h3 class="text-sm font-medium text-gray-900 truncate">{{ $movie->name }}</h3>
                                        <p class="text-xs text-gray-500 mt-1">{{ $movie->origin_name }}</p>
                                        <div class="mt-2 flex items-center">
                                            <span class="text-xs text-gray-500">{{ $movie->episode_current }}</span>
                                            <span class="mx-2 text-gray-300">|</span>
                                            <span class="text-xs text-gray-500">{{ $movie->quality }}</span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>

                    <div class="mt-6">
                        {{ $movies->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
