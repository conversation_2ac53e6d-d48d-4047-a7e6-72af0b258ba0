@extends('themevieon2025::layouts.app')

@php
    use Illuminate\Support\Str;
    $watch_url = '';
    if (isset($movie->is_copyright) && !$movie->is_copyright &&
        isset($movie->episodes) && $movie->episodes &&
        count($movie->episodes) > 0 &&
        isset($movie->episodes[0]) &&
        !empty($movie->episodes[0]->link)) {

        $firstEpisode = $movie->episodes
            ->sortBy([['server', 'asc']])
            ->groupBy('server')
            ->first()
            ->sortByDesc('name', SORT_NATURAL)
            ->groupBy('name')
            ->last()
            ->sortByDesc('type')
            ->first();
        $watch_url = route('episodes.show', [$movie->slug, $firstEpisode->slug]);
    }
@endphp

@section('title', ($movie->name ?? 'Movie') . ' - ' . setting('site_meta_title', 'VieOn 2025'))
@section('description', 'Xem phim ' . ($movie->name ?? 'Movie') . ' - ' . ($movie->origin_name ?? '') . ' ' . ($movie->publish_year ?? '') . ' chất l<PERSON>ợng HD miễn phí.')

@section('head')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta property="og:title" content="{{ $movie->name ?? 'Movie' }}">
    <meta property="og:description" content="{{ strip_tags(Str::limit($movie->content ?? '', 150)) }}">
    <meta property="og:image" content="{{ $movie->poster_url ?? '' }}">
    <meta property="og:url" content="{{ request()->url() }}">
    <meta property="og:type" content="video.movie">

    {{-- Favorite button styles --}}
    <link rel="stylesheet" href="{{ asset('themes/vieon-2025/css/favorite-button.css') }}">
@endsection

@section('content')
    {{-- Hero Banner Section --}}
    <section
            class="movie-hero relative min-h-[60vh] lg:min-h-[70vh] flex items-end bg-gradient-to-t from-theme-primary via-theme-primary/80 to-transparent">
        {{-- Background Image --}}
        <div class="absolute inset-0 z-0">
            <img src="{{ $movie->poster_url ?? asset('themes/vieon-2025/images/default-poster.jpg') }}"
                 alt="{{ $movie->name ?? 'Movie poster' }}"
                 class="w-full h-full object-cover object-center">
            <div class="absolute inset-0 bg-gradient-to-t from-theme-primary via-theme-primary/60 to-transparent"></div>
            <div class="absolute inset-0 bg-gradient-to-r from-theme-primary/80 via-transparent to-theme-primary/40"></div>
        </div>

        {{-- Hero Content --}}
        <div class="relative z-10 w-full">
            <div class="container mx-auto px-4 pb-8 lg:pb-12">
                <div class="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-8">
                    {{-- Movie Poster (Mobile/Tablet) --}}
                    <div class="lg:hidden flex justify-center">
                        <div class="w-48 sm:w-56">
                            <img src="{{ $movie->thumb_url ?? asset('themes/vieon-2025/images/default-thumb.jpg') }}"
                                 alt="{{ $movie->name ?? 'Movie poster' }}"
                                 class="movie-poster w-full rounded-lg shadow-2xl">
                        </div>
                    </div>

                    {{-- Movie Poster (Desktop) --}}
                    <div class="hidden lg:block lg:col-span-3">
                        <div class="sticky top-24">
                            <img src="{{ $movie->thumb_url ?? asset('themes/vieon-2025/images/default-thumb.jpg') }}"
                                 alt="{{ $movie->name ?? 'Movie poster' }}"
                                 class="movie-poster w-full rounded-lg shadow-2xl">

                            {{-- Quick Actions --}}
                            <div class="mt-4 space-y-3">
                                @if($watch_url)
                                    <a href="{{ $watch_url }}"
                                       class="w-full flex items-center justify-center px-4 py-3 bg-main hover:bg-green-600 text-white font-semibold rounded-lg transition-colors">
                                        <i class="fas fa-play mr-2"></i>
                                        Xem Phim
                                    </a>
                                @endif

                                @if(isset($movie->trailer_url) && $movie->trailer_url)
                                    <button onclick="showTrailer()"
                                            class="w-full flex items-center justify-center px-4 py-3 bg-theme-button hover:bg-theme-button-hover text-theme-primary font-semibold rounded-lg transition-colors">
                                        <i class="fas fa-play-circle mr-2"></i>
                                        Trailer
                                    </button>
                                @endif

                                @auth
                                    <button onclick="toggleFavorite({{ $movie->id ?? 0 }})"
                                            data-movie-id="{{ $movie->id ?? 0 }}"
                                            data-is-favorite="{{ $isFavorite ? 'true' : 'false' }}"
                                            class="favorite-btn w-full flex items-center justify-center px-4 py-3 font-semibold rounded-lg transition-all duration-300
                                                   {{ $isFavorite
                                                      ? 'bg-red-500 hover:bg-red-600 text-white border-2 border-red-500 hover:border-red-600'
                                                      : 'bg-theme-secondary hover:bg-theme-tertiary text-theme-primary border-2 border-transparent hover:border-theme-accent' }}">
                                        <i class="{{ $isFavorite ? 'fas' : 'far' }} fa-heart mr-2 transition-all duration-300"></i>
                                        <span class="favorite-text">{{ $isFavorite ? 'Đã yêu thích' : 'Yêu thích' }}</span>
                                    </button>
                                @else
                                    <button onclick="showLoginRequired()"
                                            class="w-full flex items-center justify-center px-4 py-3 bg-theme-secondary hover:bg-theme-tertiary text-theme-primary font-semibold rounded-lg transition-colors border-2 border-transparent hover:border-theme-accent">
                                        <i class="far fa-heart mr-2"></i>
                                        Yêu thích
                                    </button>
                                @endauth
                            </div>
                        </div>
                    </div>

                    {{-- Movie Information --}}
                    <div class="lg:col-span-9">
                        <div class="text-center lg:text-left">
                            {{-- Title --}}
                            <h1 class="text-3xl lg:text-5xl font-bold text-white mb-2">
                                {{ $movie->name ?? 'Unknown Movie' }}
                            </h1>

                            {{-- Original Name --}}
                            @if(isset($movie->origin_name) && $movie->origin_name && $movie->origin_name !== $movie->name)
                                <p class="text-lg lg:text-xl text-theme-tertiary mb-4">
                                    {{ $movie->origin_name }}
                                </p>
                            @endif

                            {{-- Status & Info Bar --}}
                            <div class="flex flex-wrap items-center justify-center lg:justify-start gap-4 mb-6">
                            <span class="px-3 py-1 bg-main text-white text-sm font-semibold rounded-full">
                                @if(isset($movie->status))
                                    @switch($movie->status)
                                        @case("ongoing")
                                            Đang chiếu
                                            @break
                                        @case("completed")
                                            Trọn bộ
                                            @break
                                        @default
                                            Trailer
                                    @endswitch
                                @else
                                    N/A
                                @endif
                            </span>

                                <div class="flex items-center text-theme-secondary">
                                    <i class="fas fa-star text-yellow-400 mr-1"></i>
                                    <span class="font-semibold">
                                    @if(method_exists($movie, 'getRatingStar'))
                                            {{ number_format($movie->getRatingStar() ?? 0, 1) }}
                                        @else
                                            0.0
                                        @endif
                                </span>
                                </div>

                                <span class="text-theme-secondary">{{ $movie->publish_year ?? 'N/A' }}</span>

                                @if(isset($movie->episode_time) && $movie->episode_time)
                                    <span class="text-theme-secondary">{{ $movie->episode_time }}</span>
                                @endif

                                <span class="text-theme-secondary">
                                @if(isset($movie->type) && $movie->type == 'single')
                                        Phim lẻ
                                    @else
                                        {{ $movie->episode_current ?? '?' }}/{{ $movie->episode_total ?? '?' }}
                                    @endif
                            </span>
                            </div>

                            {{-- Genres --}}
                            @if(isset($movie->categories) && $movie->categories && method_exists($movie->categories, 'count') && $movie->categories->count() > 0)
                                <div class="flex flex-wrap items-center justify-center lg:justify-start gap-2 mb-6">
                                    @foreach($movie->categories as $category)
                                        <a href="{{ route('categories.movies.index', $category->slug) }}"
                                           class="px-3 py-1 bg-theme-secondary hover:bg-theme-button text-theme-primary text-sm rounded-full transition-colors">
                                            {{ $category->name ?? 'Unknown' }}
                                        </a>
                                    @endforeach
                                </div>
                            @endif

                            {{-- Description --}}
                            @if(isset($movie->content) && $movie->content)
                                <div class="max-w-4xl">
                                    <div class="movie-description text-theme-secondary leading-relaxed"
                                         x-data="{ expanded: false }">
                                        <div x-show="!expanded" class="line-clamp-3">
                                            {!! strip_tags($movie->content) !!}
                                        </div>
                                        <div x-show="expanded" x-collapse>
                                            {!! strip_tags($movie->content) !!}
                                        </div>
                                        <button @click="expanded = !expanded"
                                                class="mt-2 text-main hover:text-green-400 font-semibold transition-colors">
                                            <span x-text="expanded ? 'Thu gọn' : 'Xem thêm'"></span>
                                            <i class="fas fa-chevron-down ml-1 transition-transform"
                                               :class="expanded ? 'rotate-180' : ''"></i>
                                        </button>
                                    </div>
                                </div>
                            @endif

                            {{-- Mobile Actions --}}
                            <div class="lg:hidden mt-6 flex flex-col sm:flex-row gap-3">
                                @if($watch_url)
                                    <a href="{{ $watch_url }}"
                                       class="flex-1 flex items-center justify-center px-4 py-3 bg-main hover:bg-green-600 text-white font-semibold rounded-lg transition-colors">
                                        <i class="fas fa-play mr-2"></i>
                                        Xem Phim
                                    </a>
                                @endif

                                @if(isset($movie->trailer_url) && $movie->trailer_url)
                                    <button onclick="showTrailer()"
                                            class="flex-1 flex items-center justify-center px-4 py-3 bg-theme-button hover:bg-theme-button-hover text-theme-primary font-semibold rounded-lg transition-colors">
                                        <i class="fas fa-play-circle mr-2"></i>
                                        Trailer
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    {{-- Basic Movie Info Section --}}
    <section class="py-8 lg:py-12">
        <div class="container mx-auto px-4">
            <div class="bg-theme-secondary rounded-lg p-6">
                <h2 class="text-xl lg:text-2xl font-bold text-theme-primary mb-6">
                    Thông tin phim
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <span class="w-24 text-theme-tertiary font-semibold">Năm:</span>
                            <span class="text-theme-primary">{{ $movie->publish_year ?? 'N/A' }}</span>
                        </div>

                        <div class="flex items-start">
                            <span class="w-24 text-theme-tertiary font-semibold">Chất lượng:</span>
                            <span class="px-2 py-1 bg-main text-white text-sm rounded">{{ $movie->quality ?? 'N/A' }}</span>
                        </div>

                        <div class="flex items-start">
                            <span class="w-24 text-theme-tertiary font-semibold">Ngôn ngữ:</span>
                            <span class="text-theme-primary">{{ $movie->language ?? 'N/A' }}</span>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-start">
                            <span class="w-24 text-theme-tertiary font-semibold">Lượt xem:</span>
                            <span class="text-theme-primary">{{ number_format($movie->view_total ?? 0) }}</span>
                        </div>

                        <div class="flex items-start">
                            <span class="w-24 text-theme-tertiary font-semibold">Đánh giá:</span>
                            <span class="text-theme-primary">
                            @if(method_exists($movie, 'getRatingCount'))
                                    {{ $movie->getRatingCount() ?? 0 }} đánh giá
                                @else
                                    0 đánh giá
                                @endif
                        </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    {{-- Comments Section --}}
    <div id="comments">
        @include('themevieon2025::components.comment-section', [
            'movie' => $movie,
            'comments' => $comments
        ])
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize favorite button state
    const favoriteBtn = document.querySelector('.favorite-btn[data-movie-id]');
    if (favoriteBtn) {
        const isFavorite = favoriteBtn.getAttribute('data-is-favorite') === 'true';

        // Ensure button has correct initial state
        if (window.MovieFunctions && window.MovieFunctions.updateFavoriteButton) {
            window.MovieFunctions.updateFavoriteButton(favoriteBtn, isFavorite);
        }

        console.log('Favorite button initialized:', {
            movieId: favoriteBtn.getAttribute('data-movie-id'),
            isFavorite: isFavorite
        });
    }
});
</script>
@endpush

@push('scripts')
    <script>
        window.movieData = {
            slug: '{{ $movie->slug ?? "" }}',
            name: '{{ str_replace("'", "\\'", $movie->name ?? "") }}',
            url: '{{ request()->url() }}',
            @if(isset($movie->trailer_url) && $movie->trailer_url && strpos($movie->trailer_url, 'youtube'))
                    @php
                        parse_str(parse_url($movie->trailer_url, PHP_URL_QUERY), $trailer_vars);
                        $video_id = $trailer_vars['v'] ?? '';
                    @endphp
            trailer: 'https://www.youtube.com/embed/{{ $video_id }}?autoplay=1'
            @else
            trailer: null
            @endif
        };

        // Override showTrailer to use movie data
        window.showTrailer = function () {
            if (window.movieData && window.movieData.trailer) {
                window.MovieFunctions.showTrailer(window.movieData.trailer);
            } else {
                console.log('No trailer available');
            }
        };
    </script>
    <script src="{{ asset('themes/vieon-2025/js/rating.js') }}"></script>
@endpush
