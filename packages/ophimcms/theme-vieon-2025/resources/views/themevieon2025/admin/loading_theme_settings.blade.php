@extends(backpack_view('blank'))

@section('content')
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Chọn giao diện loading</h2>
            <button type="button" class="btn btn-success" id="add-theme-btn">
                <i class="las la-plus"></i> Thêm Theme
            </button>
        </div>
        <div id="alert-area"></div>

        <!-- Loading Theme Selection -->
        <div class="form-group mb-3">
            <label><b>Chọn theme loading:</b></label>
            <div class="row">
                @foreach($themes as $theme)
                    <div class="col-md-4 mb-3" data-theme-id="{{ $theme['id'] ?? 0 }}">
                        <div
                            class="card h-100 theme-card {{ $current == $theme['slug'] ? 'border-primary shadow' : '' }}"
                            data-slug="{{ $theme['slug'] }}"
                            style="cursor:pointer; transition: all 0.3s ease;">
                            <div class="card-body p-3">
                                <div class="d-flex align-items-center mb-2">
                                    <input class="form-check-input me-2 theme-radio" type="radio" name="theme"
                                           id="theme-{{ $theme['slug'] }}"
                                           value="{{ $theme['slug'] }}"
                                           {{ $current == $theme['slug'] ? 'checked' : '' }}
                                           style="cursor:pointer;">
                                    <label class="form-check-label fw-bold theme-name" for="theme-{{ $theme['slug'] }}">
                                        {{ $theme['name'] }} <span
                                            class="badge bg-secondary theme-title">{{ $theme['title'] }}</span>
                                    </label>
                                    <div class="ms-auto">
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary edit-theme-btn"
                                                    data-theme-id="{{ $theme['id'] ?? 0 }}"
                                                    title="Sửa theme">
                                                <i class="las la-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger delete-theme-btn"
                                                    data-theme-id="{{ $theme['id'] ?? 0 }}"
                                                    title="Xóa theme">
                                                <i class="las la-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                            <span class="badge bg-badge"
                                  data-bg-color="{{ $theme['background_color'] }}"
                                  data-text-color="{{ $theme['text_color'] }}">BG</span>
                                    <span class="badge text-badge"
                                          data-bg-color="{{ $theme['text_color'] }}"
                                          data-text-color="{{ $theme['background_color'] }}">Text</span>
                                    <span class="badge accent-badge"
                                          data-bg-color="{{ $theme['accent_color'] }}"
                                          data-text-color="#fff">Accent</span>
                                </div>
                                <div class="small text-muted theme-subtitle">{{ $theme['subtitle'] }}</div>
                                <div class="small theme-description">{{ $theme['description'] }}</div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Loading Settings -->
        <div class="form-group mb-3">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="enabled" id="loading-enabled"
                       value="1" {{ $enabled ? 'checked' : '' }}>
                <label class="form-check-label" for="loading-enabled">
                    Bật hiệu ứng loading
                </label>
            </div>
        </div>

        <div class="form-group mb-3">
            <label for="loading-duration" class="form-label"><b>Thời gian hiển thị loading (giây):</b></label>
            <input type="number" class="form-control" id="loading-duration" name="duration"
                   value="{{ $duration }}" min="1" max="30" step="1"
                   style="max-width: 200px;">
            <div class="form-text">Thời gian tối thiểu 1 giây, tối đa 30 giây</div>
        </div>
    </div>

    <!-- Modal Create/Edit Theme -->
    <div class="modal fade" id="themeFormModal" tabindex="-1" aria-labelledby="themeFormModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="themeFormModalLabel">Thêm Theme Mới</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="theme-form">
                        <input type="hidden" id="theme-id" name="id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="theme-name" class="form-label">Tên Theme *</label>
                                    <input type="text" class="form-control" id="theme-name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="theme-slug" class="form-label">Slug *</label>
                                    <input type="text" class="form-control" id="theme-slug" name="slug" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="theme-title" class="form-label">Tiêu đề *</label>
                                    <input type="text" class="form-control" id="theme-title" name="title" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="theme-subtitle" class="form-label">Phụ đề</label>
                                    <input type="text" class="form-control" id="theme-subtitle" name="subtitle">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="theme-description" class="form-label">Mô tả</label>
                            <textarea class="form-control" id="theme-description" name="description"
                                      rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="theme-bg-color" class="form-label">Màu nền *</label>
                                    <input type="color" class="form-control" id="theme-bg-color" name="background_color"
                                           required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="theme-text-color" class="form-label">Màu chữ *</label>
                                    <input type="color" class="form-control" id="theme-text-color" name="text_color"
                                           required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="theme-accent-color" class="form-label">Màu nhấn *</label>
                                    <input type="text" class="form-control" id="theme-accent-color" name="accent_color"
                                           placeholder="#ffd700" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="theme-priority" class="form-label">Độ ưu tiên</label>
                                    <input type="number" class="form-control" id="theme-priority" name="priority"
                                           min="0" max="100" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="theme-active"
                                               name="is_active" checked>
                                        <label class="form-check-label" for="theme-active">
                                            Kích hoạt
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="las la-times"></i> Hủy
                    </button>
                    <button type="button" class="btn btn-primary" id="save-theme-btn">
                        <i class="las la-save"></i> Lưu
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('after_styles')
    <link rel="stylesheet"
          href="https://maxst.icons8.com/vue-static/landings/line-awesome/line-awesome/1.3.0/css/line-awesome.min.css">
    <link rel="stylesheet" href="{{ asset('themes/vieon-2025/css/modal-fix.css') }}">
@endpush

@push('after_scripts')
    <script src="{{ asset('themes/vieon-2025/js/loading-theme-manager.js') }}"></script>
    <script>
        // Initialize LoadingThemeManager when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            new LoadingThemeManager({
                currentTheme: '{{ $current }}',
                csrfToken: '{{ csrf_token() }}',
                routes: {
                    save: '{{ route("admin.loading-theme-settings.save") }}',
                    show: '{{ route("admin.loading-themes.show", "") }}',
                    store: '{{ route("admin.loading-themes.store") }}',
                    update: '{{ route("admin.loading-themes.update", "") }}',
                    destroy: '{{ route("admin.loading-themes.destroy", "") }}'
                }
            });
        });
    </script>
@endpush
