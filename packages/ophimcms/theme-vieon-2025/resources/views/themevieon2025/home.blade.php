@extends('themevieon2025::layouts.app')

@section('title', setting('site_meta_title', 'VieOn 2025 - Xem phim online miễn phí'))
@section('description', setting('site_meta_description', 'Xem phim online miễn phí chất lượng HD. <PERSON>m bộ, phim lẻ, anime mới nhất được cập nhật liên tục.'))

@php
    $headerFixed = true;
@endphp

@section('content')
    {{-- Hero/Banner Section --}}
    @includeIf('themevieon2025::components.hero', ['banners' => $home_page_slider_poster['data'] ?? []])

    {{-- Thanh menu thể loại/phim hot --}}
    @includeIf('themevieon2025::components.menu')

    {{-- Quảng cáo VIP --}}
    @includeIf('themevieon2025::components.vip-banner')

    {{-- Section: Phim thịnh hành --}}
    @if(isset($trending_movies) && $trending_movies->count() > 0)
        @includeIf('themevieon2025::components.movie-slider', [
            'title' => 'Thịnh hành',
            'movies' => $trending_movies
        ])
    @endif

    {{-- Section: Phim mới nhất --}}
    @if(isset($latest_movies) && $latest_movies->count() > 0)
        @includeIf('themevieon2025::components.movie-slider', [
            'title' => 'Mới nhất',
            'movies' => $latest_movies
        ])
    @endif

    {{-- Sections động từ theme options --}}
    @if(isset($sections) && is_array($sections))
        @foreach($sections as $section)
            @if($section['movies']->count() > 0)
                @includeIf("themevieon2025::components.{$section['template']}", [
                    'title' => $section['title'],
                    'movies' => $section['movies'],
                    'link' => $section['link'] ?? '#'
                ])
            @endif
        @endforeach
    @endif
@endsection
