@extends('themevieon2025::layouts.app')

@section('title', '<PERSON><PERSON> xuất cho bạn - ' . config('app.name') . ' 2025')

@section('content')
    <div class="user-profile-container">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-white mb-2">Đ<PERSON> xuất cho bạn</h2>
                <p class="text-gray-400">Những bộ phim phù hợp với sở thích của bạn</p>
            </div>

            {{-- Loading Overlay --}}
            <div id="recommendations-loading"
                 class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                <div class="bg-gray-800 rounded-lg p-6 flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-main"></div>
                    <span class="text-white"><PERSON><PERSON> tải...</span>
                </div>
            </div>

            {{-- Message Containers --}}
            <div id="recommendations-error"
                 class="hidden mb-6 bg-red-500 bg-opacity-20 border border-red-500 text-red-200 px-4 py-3 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                              clip-rule="evenodd"></path>
                    </svg>
                    <span id="recommendations-error-message"></span>
                </div>
            </div>

            {{-- Recommendations Content --}}
            <div id="recommendations-content">
                {{-- Content will be loaded via AJAX --}}
                <div class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-main"></div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            loadRecommendations();
        });

        async function loadRecommendations() {
            try {
                hideMessage();

                const recommendationsData = await window.userAPI.getRecommendations();

                if (recommendationsData.status) {
                    renderRecommendations(recommendationsData.data);
                } else {
                    showError('Không thể tải danh sách đề xuất');
                }

            } catch (error) {
                console.error('Error loading recommendations:', error);
                showError('Có lỗi xảy ra khi tải danh sách đề xuất');
            }
        }

        function renderRecommendations(recommendations) {
            const contentEl = document.getElementById('recommendations-content');

            if (recommendations.length > 0) {
                contentEl.innerHTML = `
            <div class="user-content-grid">
                ${recommendations.map(movie => renderRecommendationCard(movie)).join('')}
            </div>
        `;
            } else {
                contentEl.innerHTML = `
            <div class="text-center py-12">
                <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-gray-800/50 mb-6">
                    <svg class="h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">Chưa có đề xuất</h3>
                <p class="text-gray-400 mb-6">Hãy xem và đánh giá một số bộ phim để chúng tôi có thể đề xuất phim phù hợp với bạn.</p>
                <a href="{{ route('home') }}"
                   class="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-black bg-main hover:bg-main/90 transition-all duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                    </svg>
                    Khám phá phim mới
                </a>
            </div>
        `;
            }
        }

        function renderRecommendationCard(movie) {
            return `
        <div class="user-card group">
            <a href="/phim/${movie.slug}" class="block">
                <div class="relative overflow-hidden">
                    <img src="${movie.poster_url || movie.thumb_url || '/placeholder.jpg'}"
                         class="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
                         alt="${movie.name}">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="absolute bottom-0 left-0 right-0 p-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                        <div class="text-white">
                            <div class="font-semibold text-lg mb-1">${movie.name}</div>
                            ${movie.year ? `<div class="text-sm text-gray-300">${movie.year}</div>` : ''}
                        </div>
                    </div>
                </div>
                <div class="p-4">
                    <div class="font-semibold text-white text-lg mb-2 line-clamp-2">${movie.name}</div>
                    ${movie.origin_name ? `<div class="text-sm text-gray-400 mb-2">${movie.origin_name}</div>` : ''}
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            ${movie.rating_star ? `
                                <div class="flex items-center text-yellow-400">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    <span class="text-sm">${parseFloat(movie.rating_star).toFixed(1)}</span>
                                </div>
                            ` : ''}
                            ${movie.view_total ? `
                                <div class="flex items-center text-gray-400">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    <span class="text-sm">${formatViewCount(movie.view_total)}</span>
                                </div>
                            ` : ''}
                        </div>
                        <div class="text-xs text-gray-500">
                            ${movie.publish_year || movie.year || ''}
                        </div>
                    </div>
                </div>
            </a>
            <div class="p-4 pt-0 flex space-x-2">
                <button type="button"
                        data-action="add-favorite"
                        data-movie-id="${movie.id}"
                        class="flex-1 text-red-400 hover:text-red-300 text-sm py-2 px-3 rounded-lg hover:bg-red-500/10 transition-all duration-200 flex items-center justify-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                    Yêu thích
                </button>
                <button type="button"
                        data-action="add-watchlist"
                        data-movie-id="${movie.id}"
                        class="flex-1 text-blue-400 hover:text-blue-300 text-sm py-2 px-3 rounded-lg hover:bg-blue-500/10 transition-all duration-200 flex items-center justify-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Danh sách
                </button>
            </div>
        </div>
    `;
        }

        function formatViewCount(count) {
            if (count >= 1000000) {
                return (count / 1000000).toFixed(1) + 'M';
            } else if (count >= 1000) {
                return (count / 1000).toFixed(1) + 'K';
            }
            return count.toString();
        }

        function showError(message) {
            const errorEl = document.getElementById('recommendations-error');
            const messageEl = document.getElementById('recommendations-error-message');
            messageEl.textContent = message;
            errorEl.classList.remove('hidden');

            setTimeout(() => {
                errorEl.classList.add('hidden');
            }, 5000);
        }

        function hideMessage() {
            document.getElementById('recommendations-error').classList.add('hidden');
        }
    </script>
@endpush
