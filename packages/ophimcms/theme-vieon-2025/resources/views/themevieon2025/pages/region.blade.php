@extends('themevieon2025::layouts.app')

@section('title', 'Phim ' . $region->name . ' - Trang ' . ($movies->currentPage() ?? 1))
@section('description', 'Xem phim ' . $region->name . ' hay nhất, mới nhất. Danh sách phim quốc gia ' . $region->name . ' chấ<PERSON> <PERSON> HD miễn phí.')

@section('content')
    <div class="min-h-screen bg-theme-primary">
        {{-- Breadcrumb --}}
        @include('themevieon2025::components.breadcrumb', [
            'items' => [
                [
                    'label' => 'Quốc gia',
                    'url' => '#',
                    'icon' => 'fas fa-globe'
                ],
                [
                    'label' => $region->name,
                    'icon' => 'fas fa-flag'
                ]
            ]
        ])

        <!-- Page Header -->
        <div class="bg-theme-secondary border-b border-theme-primary">
            <div class="container mx-auto px-4 py-6">

                <!-- Region Header -->
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl md:text-3xl font-bold text-theme-primary mb-2">
                            <i class="fas fa-globe mr-2 text-main"></i>
                            Phim {{ $region->name }}
                        </h1>
                        <p class="text-theme-tertiary">
                            Tìm thấy {{ $movies->total() }} phim quốc gia {{ $region->name }}
                        </p>
                    </div>

                    <!-- Region Stats -->
                    <div class="hidden md:block text-right">
                        <div class="bg-theme-dropdown rounded-lg p-3">
                            <div class="text-2xl font-bold text-main">{{ number_format($movies->total()) }}</div>
                            <div class="text-sm text-theme-tertiary">Tổng phim</div>
                        </div>
                    </div>
                </div>

                <!-- Region Description -->
                @if($region->description)
                    <div class="mt-4 p-4 bg-theme-dropdown rounded-lg">
                        <p class="text-theme-secondary text-sm leading-relaxed">
                            {{ $region->description }}
                        </p>
                    </div>
                @endif
            </div>
        </div>

        <div class="container mx-auto px-4 py-6">
            <!-- Filter & Sort Bar -->
            <div class="bg-theme-secondary rounded-lg p-4 mb-6">
                <div class="flex flex-wrap items-center justify-between gap-4">
                    <!-- View Toggle -->
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-theme-tertiary">Hiển thị:</span>
                        <div class="flex bg-theme-dropdown rounded-lg p-1">
                            <button onclick="setViewMode('grid')"
                                    class="px-3 py-1 text-sm rounded transition-colors view-toggle {{ request('view', 'grid') === 'grid' ? 'bg-main text-white' : 'text-theme-primary hover:bg-theme-button-hover' }}">
                                <i class="fas fa-th mr-1"></i>
                                Lưới
                            </button>
                            <button onclick="setViewMode('list')"
                                    class="px-3 py-1 text-sm rounded transition-colors view-toggle {{ request('view') === 'list' ? 'bg-main text-white' : 'text-theme-primary hover:bg-theme-button-hover' }}">
                                <i class="fas fa-list mr-1"></i>
                                Danh sách
                            </button>
                        </div>
                    </div>

                    <!-- Sort Options -->
                    <div class="flex items-center gap-2">
                        <span class="text-sm text-theme-tertiary">Sắp xếp:</span>
                        <div class="flex flex-wrap gap-2">
                            @php
                                $sortOptions = [
                                    'created_at' => 'Mới nhất',
                                    'view' => 'Lượt xem',
                                    'rating' => 'Đánh giá',
                                    'year' => 'Năm sản xuất',
                                    'name' => 'Tên phim'
                                ];
                            @endphp

                            @foreach($sortOptions as $sortKey => $sortLabel)
                                <a href="{{ request()->fullUrlWithQuery(['sort' => $sortKey, 'order' => (($currentSort ?? 'created_at') === $sortKey && ($currentOrder ?? 'desc') === 'desc') ? 'asc' : 'desc']) }}"
                                   class="px-3 py-1 text-xs rounded-lg transition-colors {{ ($currentSort ?? 'created_at') === $sortKey ? 'bg-main text-white' : 'bg-theme-button hover:bg-theme-button-hover text-theme-primary' }}">
                                    {{ $sortLabel }}
                                    @if(($currentSort ?? 'created_at') === $sortKey)
                                        <i class="fas fa-chevron-{{ ($currentOrder ?? 'desc') === 'desc' ? 'down' : 'up' }} ml-1"></i>
                                    @endif
                                </a>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Movies Grid/List -->
            @if($movies->count() > 0)
                <div id="movies-container"
                     class="{{ request('view', 'grid') === 'list' ? 'space-y-4' : 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4' }} mb-6">
                    @foreach($movies as $movie)
                        @include('themevieon2025::components.movie-card', [
                            'movie' => $movie,
                            'layout' => request('view', 'grid'),
                            'showRating' => true,
                            'showActions' => true
                        ])
                    @endforeach
                </div>

                <!-- Pagination -->
                @if($movies->hasPages())
                    <div class="bg-theme-secondary rounded-lg p-4">
                        {{ $movies->appends(request()->query())->links('themevieon2025::components.pagination-ajax') }}
                    </div>
                @endif
            @else
                <!-- No Movies -->
                <div class="bg-theme-secondary rounded-lg p-8 text-center">
                    <div class="max-w-md mx-auto">
                        <i class="fas fa-globe text-6xl text-theme-tertiary mb-4"></i>
                        <h3 class="text-xl font-semibold text-theme-primary mb-2">
                            Chưa có phim nào
                        </h3>
                        <p class="text-theme-tertiary mb-6">
                            Hiện tại chưa có phim nào từ {{ $region->name }}. Hãy quay lại sau hoặc khám phá các quốc
                            gia khác.
                        </p>
                        <a href="{{ route('home') }}"
                           class="inline-block px-6 py-2 bg-main hover:bg-green-600 text-white rounded-lg transition-colors">
                            Về trang chủ
                        </a>
                    </div>
                </div>
            @endif
        </div>

        <!-- Popular Regions -->
        <div class="bg-theme-secondary border-t border-theme-primary">
            <div class="container mx-auto px-4 py-6">
                <h3 class="text-lg font-semibold text-theme-primary mb-4">
                    <i class="fas fa-fire mr-2"></i>
                    Quốc gia phổ biến
                </h3>

                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    @php
                        $popularRegions = [
                            ['name' => 'Hàn Quốc', 'slug' => 'han-quoc', 'flag' => '🇰🇷'],
                            ['name' => 'Trung Quốc', 'slug' => 'trung-quoc', 'flag' => '🇨🇳'],
                            ['name' => 'Nhật Bản', 'slug' => 'nhat-ban', 'flag' => '🇯🇵'],
                            ['name' => 'Thái Lan', 'slug' => 'thai-lan', 'flag' => '🇹🇭'],
                            ['name' => 'Âu Mỹ', 'slug' => 'au-my', 'flag' => '🇺🇸'],
                            ['name' => 'Việt Nam', 'slug' => 'viet-nam', 'flag' => '🇻🇳'],
                            ['name' => 'Ấn Độ', 'slug' => 'an-do', 'flag' => '🇮🇳'],
                            ['name' => 'Hồng Kông', 'slug' => 'hong-kong', 'flag' => '🇭🇰'],
                            ['name' => 'Đài Loan', 'slug' => 'dai-loan', 'flag' => '🇹🇼'],
                            ['name' => 'Singapore', 'slug' => 'singapore', 'flag' => '🇸🇬'],
                            ['name' => 'Philippines', 'slug' => 'philippines', 'flag' => '🇵🇭'],
                            ['name' => 'Khác', 'slug' => 'khac', 'flag' => '🌍']
                        ];
                    @endphp

                    @foreach($popularRegions as $popularRegion)
                        <a href="{{ route('regions.movies.index', $popularRegion['slug']) }}"
                           class="flex items-center gap-2 p-3 bg-theme-dropdown hover:bg-theme-button-hover rounded-lg transition-colors {{ $region->slug === $popularRegion['slug'] ? 'ring-2 ring-main' : '' }}">
                            <span class="text-2xl">{{ $popularRegion['flag'] }}</span>
                            <span class="text-sm font-medium text-theme-primary">{{ $popularRegion['name'] }}</span>
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // View mode toggle
        function setViewMode(mode) {
            const url = new URL(window.location);
            url.searchParams.set('view', mode);
            window.location.href = url.toString();
        }

        // AJAX pagination
        document.addEventListener('DOMContentLoaded', function () {
            // Handle pagination clicks
            document.addEventListener('click', function (e) {
                if (e.target.matches('.pagination a') || e.target.closest('.pagination a')) {
                    e.preventDefault();
                    const link = e.target.matches('a') ? e.target : e.target.closest('a');
                    loadPage(link.href);
                }
            });

            function loadPage(url) {
                // Show loading state
                const container = document.getElementById('movies-container');
                container.style.opacity = '0.5';

                fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'text/html'
                    }
                })
                    .then(response => response.text())
                    .then(html => {
                        // Parse the response and update content
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(html, 'text/html');

                        // Update movies container
                        const newContainer = doc.getElementById('movies-container');
                        if (newContainer) {
                            container.innerHTML = newContainer.innerHTML;
                        }

                        // Update pagination
                        const pagination = document.querySelector('.pagination');
                        const newPagination = doc.querySelector('.pagination');
                        if (pagination && newPagination) {
                            pagination.innerHTML = newPagination.innerHTML;
                        }

                        // Update URL
                        window.history.pushState({}, '', url);

                        // Scroll to top
                        window.scrollTo({top: 0, behavior: 'smooth'});
                    })
                    .catch(error => {
                        console.error('AJAX pagination error:', error);
                    })
                    .finally(() => {
                        container.style.opacity = '1';
                    });
            }
        });
    </script>
@endpush
