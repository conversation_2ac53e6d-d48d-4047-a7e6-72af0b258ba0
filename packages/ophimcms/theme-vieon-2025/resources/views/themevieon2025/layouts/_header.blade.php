<!-- Header -->
<header class="w-full top-0 z-50 header-gradient backdrop-blur-md shadow-lg transition-all duration-300 {{ $headerFixed ? 'fixed' : 'relative' }}">
    <div class="px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16 items-center">
            <!-- Left Side: Logo + Navigation -->
            <div class="flex items-center flex-1">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="{{ route('home') }}" class="flex items-center">
                        @include('themevieon2025::components.logo', ['class' => 'h-8 w-auto'])
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex space-x-6 ml-8 items-center">
                    @php
                        $menu = \Ophim\Core\Models\Menu::getTree();
                    @endphp
                    @if($menu && $menu->count() > 0)
                        @foreach($menu->take(6) as $item)
                            @if(count($item->children) > 0)
                                <div class="relative group">
                                    <a href="{{ $item->link }}"
                                       class="inline-flex items-center px-3 py-2 text-sm font-medium text-theme-primary hover:text-main border-b-2 border-transparent hover:border-main transition-all duration-200">
                                        {{ $item->name }}
                                        <i class="fas fa-chevron-down ml-1 text-xs"></i>
                                    </a>
                                    <!-- Dropdown Menu -->
                                    <div
                                            class="absolute top-full left-0 mt-1 w-48 bg-theme-dropdown backdrop-blur-md rounded-lg shadow-theme-dropdown opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                        @foreach($item->children->take(8) as $child)
                                            <a href="{{ $child->link }}"
                                               class="block px-4 py-2 text-sm text-theme-secondary hover:text-main hover:bg-theme-button-hover transition-all duration-200">
                                                {{ $child->name }}
                                            </a>
                                        @endforeach
                                    </div>
                                </div>
                            @else
                                <a href="{{ $item->link }}"
                                   class="inline-flex items-center px-3 py-2 text-sm font-medium text-theme-primary hover:text-main border-b-2 border-transparent hover:border-main transition-all duration-200 {{ request()->is($item->link) ? 'menu-item-active' : '' }}">
                                    {{ $item->name }}
                                </a>
                            @endif
                        @endforeach
                    @endif
                </nav>

                <!-- Tablet Navigation (Reduced) -->
                <nav class="hidden md:flex lg:hidden space-x-4 ml-6 items-center">
                    @if($menu && $menu->count() > 0)
                        @foreach($menu->take(3) as $item)
                            <a href="{{ $item->link }}"
                               class="inline-flex items-center px-2 py-2 text-sm font-medium text-theme-primary hover:text-main border-b-2 border-transparent hover:border-main transition-all duration-200 {{ request()->is($item->link) ? 'menu-item-active' : '' }}">
                                {{ $item->name }}
                            </a>
                        @endforeach
                    @endif
                </nav>
            </div>

            <!-- Right Side: Actions -->
            <div class="flex items-center space-x-2 sm:space-x-4">
                @include('themevieon2025::components.search-button')
                <!-- User Profile Dropdown -->
                <div x-data="{ open: false }" class="relative">
                    <button @click="@guest window.dispatchEvent(new CustomEvent('show-login-modal')); @else open = !open; @endguest"
                            @keydown.escape="open = false"
                            class="text-theme-primary hover:text-main transition-all duration-300 focus:outline-none border border-main/40 hover:border-main rounded-full p-1"
                            title="Tài khoản" :aria-expanded="open">
                        @guest
                            <div class="flex items-center space-x-2 text-xl p-2 hover:bg-theme-button-hover rounded-lg">
                                <i class="fas fa-user-circle"></i>
                                <span class="text-sm font-medium">Thành viên</span>
                            </div>
                        @endguest
                        @auth
                            <img class="w-9 h-9 rounded-full object-cover"
                                 src="{{ Auth::user()->getAvatar() }}"
                                 alt="{{ Auth::user()->name }}">
                        @endauth
                    </button>
                    @auth
                        <div x-show="open" @click.away="open = false"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-95"
                             x-transition:enter-end="opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 scale-100"
                             x-transition:leave-end="opacity-0 scale-95"
                             class="absolute right-0 mt-2 w-48 bg-theme-dropdown border border-theme-primary rounded-lg shadow-theme-dropdown z-50 py-2 text-sm min-w-[180px]"
                             :class="{'block': open, 'hidden': !open}">
                            <div class="px-4 py-2 text-theme-tertiary border-b border-theme-primary flex items-center space-x-2">
                                <i class="fas fa-user text-main"></i>
                                <span>{{ Auth::user()->name }}</span>
                            </div>
                            <a href="{{ route('user.profile.show') }}"
                               class="block px-4 py-2 text-theme-secondary hover:bg-main/10 hover:text-main transition">Hồ
                                sơ</a>
                            <a href="{{ route('user.favorites') }}"
                               class="block px-4 py-2 text-theme-secondary hover:bg-main/10 hover:text-main transition">Yêu
                                thích</a>
                            <a href="{{ route('user.history') }}"
                               class="block px-4 py-2 text-theme-secondary hover:bg-main/10 hover:text-main transition">Lịch
                                sử</a>
                            <a href="{{ route('user.settings.show') }}"
                               class="block px-4 py-2 text-theme-secondary hover:bg-main/10 hover:text-main transition">Cài
                                đặt</a>
                            <form method="POST" action="{{ route('logout') }}" x-data class="mt-2">
                                @csrf
                                <button type="submit"
                                        class="w-full text-left px-4 py-2 text-red-400 hover:bg-red-500/10 hover:text-red-500 transition">
                                    Đăng xuất
                                </button>
                            </form>
                        </div>
                    @endauth
                </div>

                <!-- Hamburger Menu (Mobile only) -->
                <button id="menu-toggle"
                        class="flex lg:hidden items-center text-theme-primary focus:outline-none text-2xl p-2 hover:bg-theme-button-hover rounded-lg transition-all duration-300">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <nav id="mobile-menu" class="lg:hidden hidden flex-col space-y-2 mt-2 bg-theme-dropdown rounded-lg p-4">
            @if($menu && $menu->count() > 0)
                @foreach($menu as $item)
                    @if(count($item->children) > 0)
                        <div class="space-y-1">
                            <a href="{{ $item->link }}"
                               class="flex items-center text-theme-primary hover:text-main py-2 px-3 rounded-lg hover:bg-theme-button-hover transition-all duration-300 font-medium">
                                {{ $item->name }}
                                <i class="fas fa-chevron-down ml-auto text-xs"></i>
                            </a>
                            <div class="ml-4 space-y-1">
                                @foreach($item->children->take(5) as $child)
                                    <a href="{{ $child->link }}"
                                       class="flex items-center text-theme-tertiary hover:text-main py-1 px-3 rounded transition-all duration-300 text-sm">
                                        {{ $child->name }}
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @else
                        <a href="{{ $item->link }}"
                           class="flex items-center text-theme-primary hover:text-main py-2 px-3 rounded-lg hover:bg-theme-button-hover transition-all duration-300">
                            {{ $item->name }}
                        </a>
                    @endif
                @endforeach
            @endif
        </nav>
    </div>
</header>
