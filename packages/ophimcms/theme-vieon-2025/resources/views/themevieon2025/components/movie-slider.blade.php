@if(isset($movies) && $movies->count() > 0)
    <div class="movie-slider-section py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-2xl font-bold text-theme-primary mb-6">{{ $title ?? '[Section Title]' }}</h2>

            <div class="swiper movie-swiper">
                <div class="swiper-wrapper">
                    @foreach($movies as $movie)
                        <div class="swiper-slide">
                            <a href="{{ $movie->getUrl() }}" class="block group">
                                <div class="relative bg-theme-card rounded-lg overflow-hidden shadow-theme-secondary hover:shadow-theme-primary transition-all duration-300 transform hover:-translate-y-1">
                                    <!-- Movie Thumbnail -->
                                    <div class="relative aspect-[2/3] overflow-hidden">
                                        @if($movie->thumb_url)
                                            <img src="{{ $movie->thumb_url }}"
                                                 alt="{{ $movie->name }}"
                                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                                                 loading="lazy">
                                        @else
                                            <div class="w-full h-full bg-theme-tertiary flex items-center justify-center">
                                                <i class="fas fa-film text-theme-muted text-3xl"></i>
                                            </div>
                                        @endif

                                        <!-- Movie Type Badge -->
                                        <div class="absolute top-2 left-2">
                                            @if($movie->type === 'series')
                                                <span class="bg-red-500 text-theme-inverse text-xs px-2 py-1 rounded font-bold">
                                                Bộ
                                            </span>
                                            @else
                                                <span class="bg-blue-500 text-theme-inverse text-xs px-2 py-1 rounded font-bold">
                                                Lẻ
                                            </span>
                                            @endif
                                        </div>

                                        <!-- Quality Badge -->
                                        @if($movie->quality)
                                            <div class="absolute top-2 right-2">
                                            <span class="bg-yellow-500 text-theme-inverse text-xs px-2 py-1 rounded font-bold">
                                                {{ $movie->quality }}
                                            </span>
                                            </div>
                                        @endif

                                        <!-- Episode Count -->
                                        @if($movie->episode_total > 0)
                                            <div class="absolute bottom-2 left-2">
                                            <span class="bg-black bg-opacity-75 text-theme-inverse text-xs px-2 py-1 rounded">
                                                {{ $movie->episode_total }} tập
                                            </span>
                                            </div>
                                        @endif

                                        <!-- Play Button Overlay -->
                                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                                            <div class="bg-main text-theme-inverse p-3 rounded-full opacity-0 group-hover:opacity-100 transform scale-75 group-hover:scale-100 transition-all duration-300">
                                                <i class="fas fa-play text-lg"></i>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Movie Info -->
                                    <div class="p-3">
                                        <h3 class="text-sm font-semibold text-theme-primary truncate mb-1"
                                            title="{{ $movie->name }}">
                                            {{ $movie->name }}
                                        </h3>

                                        <div class="flex items-center justify-between text-xs text-theme-tertiary">
                                            @if($movie->publish_year)
                                                <span>{{ $movie->publish_year }}</span>
                                            @endif
                                            @if($movie->view_total > 0)
                                                <span class="flex items-center">
                                                <i class="fas fa-eye mr-1"></i>
                                                {{ number_format($movie->view_total) }}
                                            </span>
                                            @endif
                                        </div>

                                        <!-- Rating -->
                                        @if($movie->rating > 0)
                                            <div class="flex items-center mt-2">
                                                <div class="flex text-yellow-400 text-xs">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        @if($i <= $movie->rating)
                                                            <i class="fas fa-star"></i>
                                                        @else
                                                            <i class="far fa-star"></i>
                                                        @endif
                                                    @endfor
                                                </div>
                                                <span class="text-theme-tertiary text-xs ml-1">{{ number_format($movie->rating, 1) }}</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </a>
                        </div>
                    @endforeach
                </div>

                <!-- Navigation Arrows -->
                <div class="swiper-button-next movie-swiper-next"></div>
                <div class="swiper-button-prev movie-swiper-prev"></div>

                <!-- Pagination -->
                <div class="swiper-pagination movie-swiper-pagination"></div>
            </div>
        </div>
    </div>
@endif

<style>
.movie-slider-section {
    @apply bg-theme-secondary;
}

.movie-swiper {
    @apply relative;
}

.swiper-button-next,
.swiper-button-prev {
    @apply bg-theme-dropdown text-theme-primary border border-theme-primary rounded-full w-10 h-10;
    @apply hover:bg-theme-button-hover transition-all duration-300;
}

.swiper-button-next::after,
.swiper-button-prev::after {
    @apply text-sm;
}

.swiper-pagination-bullet {
    @apply bg-theme-tertiary opacity-50;
}

.swiper-pagination-bullet-active {
    @apply bg-main opacity-100;
}

@media (max-width: 768px) {
    .movie-slider-section {
        @apply py-6;
    }

    .swiper-button-next,
    .swiper-button-prev {
        @apply w-8 h-8;
    }

    .swiper-button-next::after,
    .swiper-button-prev::after {
        @apply text-xs;
    }
}
</style>
